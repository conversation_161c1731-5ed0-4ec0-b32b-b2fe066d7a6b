# -*- coding: utf-8 -*-
import os
import sys


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


from app.models import db, LockedAssetBalance


def delete_data():
    last_id = 0
    limit_size = 2000
    while True:
        rows = LockedAssetBalance.query.filter(
            LockedAssetBalance.business == LockedAssetBalance.Business.HISTORY,
            LockedAssetBalance.id > last_id,
        ).order_by(
            LockedAssetBalance.id.asc()
        ).limit(limit_size).all()
        ids = [r.id for r in rows]
        LockedAssetBalance.query.filter(
            LockedAssetBalance.id.in_(ids)
        ).delete()
        db.session.commit()

        if len(rows) > 0:
            last_id = rows[-1].id
        if len(rows) != limit_size:
            break

    print('delete done')


def main():
    delete_data()


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
