from init_base import app

import click
from sqlalchemy import func
from app.models import db
from app.models.media import Video
from app.models.user import File
from app.utils import AWSBucketPublic
from app.utils.file import ThumbnailScale


@click.command()
@click.option("--video-ids", default=None, help="指定视频ID列表，用逗号分隔。如果不指定则处理所有没有封面的视频")
@click.option("--batch-size", default=10, help="批处理大小，默认10个视频一批")
@click.option("--dry-run", is_flag=True, default=False, help="仅显示将要处理的视频，不实际生成封面")
def main(video_ids, batch_size, dry_run):
    """为现有视频生成封面图"""

    # 构建查询条件
    query = db.session.query(Video, File).join(
        File, File.id == Video.file_id
    ).filter(
        Video.cover_key.is_(None)  # 只处理没有封面的视频
    )

    if video_ids:
        # 如果指定了视频ID，只处理这些视频
        video_id_list = [int(vid.strip()) for vid in video_ids.split(",")]
        query = query.filter(Video.id.in_(video_id_list))
        print(f"指定处理视频ID: {video_id_list}")
    else:
        print("处理所有没有封面的视频")

    # 获取视频总数
    total_count = query.count()
    print(f"找到 {total_count} 个需要处理的视频")

    if total_count == 0:
        print("没有需要处理的视频")
        return

    if dry_run:
        print("\n=== 预览模式：将要处理的视频 ===")
        videos = query.order_by(Video.id).all()
        for video, file in videos:
            print(f"ID: {video.id}, 名称: {video.name}, 文件: {file.key}")
        return

    # 分批处理
    processed_count = 0
    failed_count = 0

    offset = 0
    while offset < total_count:
        # 获取当前批次的视频
        batch_videos = query.order_by(Video.id).offset(offset).limit(batch_size).all()

        if not batch_videos:
            break

        print(f"\n处理第 {offset + 1} - {min(offset + batch_size, total_count)} 个视频...")

        for video, file in batch_videos:
            try:
                print(f"  处理视频 ID {video.id}: {video.name}")

                # 检查文件是否存在
                if not AWSBucketPublic.check_exists(file.key):
                    print(f"    警告: 文件 {file.key} 不存在，跳过")
                    failed_count += 1
                    continue

                # 获取文件后缀
                file_suffix = file.key.split('.')[-1].lower()

                # 生成缩略图（原始尺寸作为封面）
                thumbnail_keys = AWSBucketPublic.put_file_thumbnail(
                    file.key,
                    AWSBucketPublic.get_file_url(file.key),
                    file_suffix,
                    ThumbnailScale(ThumbnailScale.Size.ORIGINAL)
                )

                if thumbnail_keys and len(thumbnail_keys) > 0:
                    # 更新视频的封面字段
                    cover_key = thumbnail_keys[0]
                    video.cover_key = cover_key
                    db.session.commit()

                    print(f"    成功生成封面: {cover_key}")
                    processed_count += 1
                else:
                    print(f"    失败: 无法生成封面")
                    failed_count += 1

            except Exception as e:
                print(f"    错误: {str(e)}")
                failed_count += 1
                # 回滚当前事务
                db.session.rollback()

        offset += batch_size

    print(f"\n=== 处理完成 ===")
    print(f"总计处理: {total_count} 个视频")
    print(f"成功生成封面: {processed_count} 个")
    print(f"失败: {failed_count} 个")


if __name__ == '__main__':
    with app.app_context():
        main()
