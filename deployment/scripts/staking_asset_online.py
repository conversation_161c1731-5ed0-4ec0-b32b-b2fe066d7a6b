
from datetime import timedelta
from decimal import Decimal
from app.assets.asset import get_asset_config
from app.business.clients.wallet import WalletClient
from app.business.staking import StakingOperation
from app.common.constants import PrecisionEnum
from app.models.base import db
from app.models.daily import DailyStakingReport
from app.models.staking import StakingAccount, StakingPool
from app.utils.amount import quantize_amount
from app.utils.date_ import date_to_datetime, today
from app.business.clients.server import ServerClient
from pyroaring import BitMap


ASSETS = ['ADA', ]

today_ = today()
yday = today_ - timedelta(days=1)

for asset in ASSETS:
    db.session.add(StakingPool(asset=asset))
    account = StakingAccount(account_id=StakingAccount.ACCOUNT_ID, 
                             asset=asset,
                             status=StakingAccount.Status.OPEN)
    db.session.add(account)

    report = DailyStakingReport(report_date=yday, asset=asset)
    total_reward_amount = WalletClient().get_staking_reward(asset,
                                                            date_to_datetime(yday), 
                                                            date_to_datetime(yday + timedelta(days=1)))['amount']
    report.user_reward_amount = 0 # 用户收益
    report.reward_amount = total_reward_amount
    report.system_reward_amount = total_reward_amount - report.user_reward_amount
    report.income_user_bitmap = BitMap([]).serialize()
    report.user_bitmap = BitMap([]).serialize()
    report.history_income_user_bitmap = BitMap([]).serialize()
    report.history_user_bitmap = BitMap([]).serialize()
    income_rate = 0
    operation = StakingOperation(asset)
    summary = WalletClient().get_staking_summary(asset)
    income_rate = summary['apr']
    income_rate = income_rate * (1 - operation.get_configs()["system_sharing_ratio"])
    report.income_rate = quantize_amount(income_rate, PrecisionEnum.RATE_PLACES)
    report_at = date_to_datetime(yday + timedelta(days=1))
    report.system_staking_amount = operation.get_total_staking_amount(report_at)
    db.session.add(report)

db.session.commit()


# 质押配置
conf = get_asset_config("ADA")
conf.staking_bufsize = Decimal("1000000")
conf.staking_withdraw_period = Decimal("240") # 10天

# 更新server币种
ServerClient().update_assets()