# -*- coding: utf-8 -*-
import os
import sys


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


from app.models import db, LockedAssetBalance
from app.models.wallet import LockedAssetBalanceBackup


def back_up():
    last_id = 0
    limit_size = 2000
    while True:
        rows = LockedAssetBalance.query.filter(
            LockedAssetBalance.business == LockedAssetBalance.Business.HISTORY,
            LockedAssetBalance.id > last_id,
        ).order_by(
            LockedAssetBalance.id.asc()
        ).limit(limit_size).all()

        new_rows = []
        for r in rows:
            r: LockedAssetBalance
            backup = LockedAssetBalanceBackup(
                id=r.id,
                created_at=r.created_at,
                updated_at=r.updated_at,
                user_id=r.user_id,
                created_by=r.created_by,
                asset=r.asset,
                amount=r.amount,
                sub_amount=r.sub_amount,
                locked_at=r.locked_at,
                unlocked_at=r.unlocked_at,
                status=r.status,
                remark=r.remark,
                retry_type=r.retry_type,
                lock_type=r.lock_type,
                lock_business=r.lock_business,
                unlock_type=r.unlock_type,
                unlock_business=r.unlock_business,
                business=r.business,
                business_id=r.business_id
            )
            new_rows.append(backup)
        db.session.bulk_save_objects(new_rows)
        db.session.commit()

        if len(rows) > 0:
            last_id = rows[-1].id
        if len(rows) != limit_size:
            break

    num1 = LockedAssetBalance.query.filter(
        LockedAssetBalance.business == LockedAssetBalance.Business.HISTORY
    ).count()
    num2 = LockedAssetBalanceBackup.query.count()
    if num1 != num2:
        print(f'备份异常，num1:{num1}，num2:{num2}')
        raise


def main():
    back_up()


if __name__ == '__main__':
    from app import create_app

    app = create_app()
    with app.app_context():
        main()
