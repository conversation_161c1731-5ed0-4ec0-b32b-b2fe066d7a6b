from collections import defaultdict
import random
from datetime import datetime, date, timedelta
from decimal import Decimal, ROUND_UP
from pprint import pprint
from unittest.mock import MagicMock, patch
import uuid

import pytest
from flask import g
from pytz import UTC, utc

from app import Language
from app.caches import func
from app.common.constants import BalanceBusiness, ReportType
from app.models import AssetInvestmentConfig, User
from app.models.base import db
from app.models.daily import DailyInvestmentReport, DailySiteInvestmentReport
from app.models.investment import InterestStatisticTime, UserDayInterestDetail, UserDayInterestHistory, UserHourInterestDetail, UserHourInterestHistory, UserInvestmentSummary
from app.models.monthly import MonthlyInvestmentReport, MonthlySiteInvestmentReport
from app.utils import amount_to_str, quantize_amount_non_zero, quantize_amount
from app.utils.date_ import now, today, timestamp_to_datetime
from pyroaring import BitMap

USER_ID = 20044


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.auth_user = g.user = User.query.get(USER_ID)
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


@pytest.mark.usefixtures('module_setup')
class TestInvestmentBiz:
    
    def test_calc_day_base_rate(self, tcontext):
        from app.business.investment import InvestmentDataProc
        from decimal import Decimal
        with tcontext:
            interest_data = {"BTC": Decimal("100"), "ETH": Decimal("50")}
            asset_total_balance = {"BTC": Decimal("1000"), "ETH": Decimal("500")}
            result = InvestmentDataProc.calc_day_base_rate(interest_data, asset_total_balance)
            assert result == {"BTC": Decimal("0.1"), "ETH": Decimal("0.1")}

    def test_get_dt_balance_snapshot(self, tcontext):
        from app.business.investment import InvestmentDataProc
        with tcontext:
            report_hour = now()
            result = InvestmentDataProc.get_dt_balance_snapshot(report_hour)
            pprint(result)

    def test_get_dt_asset_user_snap_map(self, tcontext):
        from app.business.investment import InvestmentDataProc
        with tcontext:
            report_hour = datetime(2025, 8, 4, 15, 0, 0)
            result = InvestmentDataProc.get_dt_asset_user_snap_map(report_hour)
            pprint(result)

    def test_get_margin_interest_data(self, tcontext):
        from app.business.investment import InvestmentDataProc
        with tcontext:
            start = datetime(2025, 8, 2, 0, 0, 0)
            end = datetime(2025, 8, 3, 0, 0, 0)
            result = InvestmentDataProc.get_margin_interest_data(start, end)
            pprint(result)

    def test_get_pledge_interest_data(self, tcontext):
        from app.business.investment import InvestmentDataProc
        with tcontext:
            start = datetime(2025, 7, 27, 0, 0, 0)
            end = datetime(2025, 7, 28, 0, 0, 0)
            result = InvestmentDataProc.get_pledge_interest_data(start, end)
            pprint(result)

    def test_get_margin_pledge_interest_data(self, tcontext):
        from app.business.investment import InvestmentDataProc
        with tcontext:
            start = datetime(2025, 8, 3, 0, 0, 0)
            end = datetime(2025, 8, 4, 0, 0, 0)
            result = InvestmentDataProc.get_margin_pledge_interest_data(start, end)
            pprint(result)

    def test_get_valid_assets_config(self, tcontext):
        from app.business.investment import InvestmentDataProc
        with tcontext:
            result = InvestmentDataProc.get_valid_assets_config()
            for asset, config in result.items():
                print(f"Asset: {asset}, Config: {config.to_dict(enum_to_name=True)}")

    def test_get_sub_account_ids(self, tcontext):
        from app.business.investment import InvestmentDataProc
        with tcontext:
            user_ids = [1001, 1002, 1003]
            result = InvestmentDataProc().get_sub_account_ids(user_ids)
            pprint(result)


@pytest.mark.usefixtures('module_setup')
class TestInvestmentHourlyProc:
    
    def test_check_hour_interest_ready(self, tcontext):
        from app.business.investment import InvestmentHourlyProc
        with tcontext:
            processor = InvestmentHourlyProc()
            report_hour = datetime(2025, 7, 27, 1, 0, 0)
            result = processor.check_hour_interest_ready(report_hour)
            pprint(result)

    def test_process_hourly_interest(self, tcontext):
        from app.business.investment import InvestmentHourlyProc
        with tcontext:
            processor = InvestmentHourlyProc()
            report_hour = datetime(2025, 8, 20, 5, 0, 0, tzinfo=utc)
            result = processor.process_hourly_interest(report_hour)
            # pprint(result)

    def test_save_hour_rate(self, tcontext):
        from app.business.investment import InvestmentHourlyProc
        from decimal import Decimal
        with tcontext:
            processor = InvestmentHourlyProc()
            asset_rate = {"BTC": Decimal("0.05"), "ETH": Decimal("0.03")}
            result = processor.update_asset_hour_rate(asset_rate)
            pprint(result)

    def test_save_hourly_interest(self, tcontext):
        from app.business.investment import InvestmentHourlyProc
        from decimal import Decimal
        with tcontext:
            processor = InvestmentHourlyProc()
            asset_user_interest = {
                "BTC": {
                    1001: {
                        "BASE": {"amount": Decimal("10"), "rate": Decimal("0.05"), "balance": Decimal("1000")}
                    }
                }
            }
            report_hour = datetime(2025, 7, 27, 1, 0, 0)
            result = processor._save_hourly_interest(asset_user_interest, report_hour)
            pprint(result)

    def test_calculate_user_hourly_interest(self, tcontext):
        from app.business.investment import InvestmentHourlyProc
        from decimal import Decimal
        with tcontext:
            processor = InvestmentHourlyProc()
            balance = Decimal("1000")
            base_rate = Decimal("0.05")
            rule = {AssetInvestmentConfig.ConfigType.LADDER: {"limit": "1000", "rate": "0.02"},
                    AssetInvestmentConfig.ConfigType.FIXED: {"rate": "0.01"}}
            is_sub_account = False
            result = processor.calculate_user_hourly_interest(balance, base_rate, rule, is_sub_account)
            pprint(result)

    def test_calculate_base_interest(self, tcontext):
        from app.business.investment import InvestmentHourlyProc
        from decimal import Decimal
        with tcontext:
            processor = InvestmentHourlyProc()
            balance = Decimal("1000")
            rate = Decimal("0.05")
            result = processor._calculate_base_interest(balance, rate)
            pprint(result)

    def test_calculate_ladder_interest(self, tcontext):
        from app.business.investment import InvestmentHourlyProc
        from decimal import Decimal
        with tcontext:
            processor = InvestmentHourlyProc()
            balance = Decimal("1000")
            rule = {"limit": "1000", "rate": "0.02"}
            is_sub_account = False
            result = processor._calculate_ladder_interest(balance, rule, is_sub_account)
            pprint(result)

    def test_calculate_fixed_interest(self, tcontext):
        from app.business.investment import InvestmentHourlyProc
        from decimal import Decimal
        with tcontext:
            processor = InvestmentHourlyProc()
            balance = Decimal("1000")
            rule = {"rate": "0.01"}
            result = processor._calculate_fixed_interest(balance, rule)
            pprint(result)

    def test_save_hour_statistic_time(self, tcontext):
        from app.business.investment import InvestmentHourlyProc
        with tcontext:
            processor = InvestmentHourlyProc()
            report_hour = datetime(2025, 7, 27, 1, 0, 0)
            result = processor._save_hour_statistic_time(report_hour)
            pprint(result)

    def test_min_amount_map(self, tcontext):
        from app.business.investment import InvestmentDataProc
        with tcontext:
            from app.business import PriceManager
            price = PriceManager.asset_to_usd("BTC")
            ret = InvestmentDataProc.get_asset_min_amount()
            assert ret['BTC'] == quantize_amount_non_zero(Decimal("100") / price, 1, ROUND_UP)


@pytest.mark.usefixtures('module_setup')
class TestInvestmentDailyProc:
    
    def test_check_day_hour_interest_ready(self, tcontext):
        from app.business.investment import InvestmentDailyProc
        with tcontext:
            processor = InvestmentDailyProc()
            report_date = date(2025, 7, 27)
            result = processor.check_day_hour_interest_ready(report_date)
            pprint(result)

    def test_get_day_last_hour(self, tcontext):
        from app.business.investment import InvestmentDailyProc
        with tcontext:
            processor = InvestmentDailyProc()
            report_date = date(2025, 7, 27)
            result = processor._get_day_last_hour(report_date)
            pprint(result)

    def test_process_daily_interest(self, tcontext):
        from app.business.investment import InvestmentDailyProc
        with tcontext:
            processor = InvestmentDailyProc()
            report_date = date(2025, 7, 27)
            result = processor.process_daily_interest(report_date)
            pprint(result)

    def test_aggregate_daily_interest(self, tcontext):
        from app.business.investment import InvestmentDailyProc
        with tcontext:
            processor = InvestmentDailyProc()
            report_date = date(2025, 7, 27)
            result = processor.aggregate_daily_interest(report_date)
            pprint(result)

    def test_save_daily_interest(self, tcontext):
        from app.business.investment import InvestmentDailyProc
        from decimal import Decimal
        with tcontext:
            processor = InvestmentDailyProc()
            user_asset_interest = {"BTC": {1001: Decimal("10")}}
            user_asset_details = {"BTC": {1001: []}}
            report_date = date(2025, 7, 27)
            result = processor._save_daily_interest(user_asset_interest, user_asset_details, report_date)
            pprint(result)

    def test_save_day_statistic_time(self, tcontext):
        from app.business.investment import InvestmentDailyProc
        with tcontext:
            processor = InvestmentDailyProc()
            report_date = date(2025, 7, 27)
            result = processor._save_day_statistic_time(report_date)
            pprint(result)

    def test_is_user_asset_zero(self, tcontext):
        from app.business.investment import InvestmentDailyProc
        with tcontext:
            processor = InvestmentDailyProc()
            user_id = 1001
            asset = "BTC"
            report_date = date(2025, 7, 27)
            result = processor._is_user_asset_zero(user_id, asset)
            pprint(result)

    def test_aggregate_interest_details(self, tcontext):
        from app.business.investment import InvestmentDailyProc
        with tcontext:
            processor = InvestmentDailyProc()
            hourly_details = []
            report_date = date(2025, 7, 27)
            user_id = 1001
            asset = "BTC"
            result = processor._aggregate_interest_details(hourly_details, report_date, user_id, asset)
            pprint(result)


@pytest.mark.usefixtures('module_setup')
class TestInvestmentDailyPayoutProc:
    
    def test_check_day_interest_ready(self, tcontext):
        from app.business.investment import InvestmentDailyPayoutProc
        with tcontext:
            processor = InvestmentDailyPayoutProc()
            report_date = date(2025, 7, 27)
            result = processor.check_day_interest_ready(report_date)
            pprint(result)

    def test_get_day_interest_results(self, tcontext):
        from app.business.investment import InvestmentDailyPayoutProc
        with tcontext:
            processor = InvestmentDailyPayoutProc()
            report_date = date(2025, 7, 27)
            result = processor._get_day_interest_results(report_date)
            pprint(result)

    def test_get_day_interest_pending_rows(self, tcontext):
        from app.business.investment import InvestmentDailyPayoutProc
        with tcontext:
            processor = InvestmentDailyPayoutProc()
            report_date = date(2025, 7, 27)
            result = processor._get_day_interest_pending_rows(report_date)
            pprint(result)

    def test_process_daily_interest_payout(self, tcontext):
        from app.business.investment import InvestmentDailyPayoutProc
        with tcontext:
            processor = InvestmentDailyPayoutProc()
            report_date = date(2025, 7, 27)
            result = processor.process_daily_interest_payout(report_date)
            pprint(result)

    def test_save_daily_interest(self, tcontext):
        from app.business.investment import InvestmentDailyPayoutProc
        with tcontext:
            processor = InvestmentDailyPayoutProc()
            interest_results = []
            result = processor._save_daily_interest(interest_results)
            pprint(result)

    def test_payout_interest(self, tcontext):
        from app.business.investment import InvestmentDailyPayoutProc
        with tcontext:
            processor = InvestmentDailyPayoutProc()
            model = UserDayInterestHistory
            st = date(2025, 8, 15)
            row = model.query.filter(model.report_date == st).first()
            row.status = row.Status.PENDING
            processor._payout_interest([row])
            
            row = model.query.filter(model.report_date == st).first()
            assert row.status == row.Status.SUCCESS

    def test_http_retry(self, tcontext):
        with tcontext:
            from app.utils import BaseHTTPClient
            from requests.exceptions import ConnectionError
            times = 3
            retry = BaseHTTPClient.retry(times)
            retry_list = []

            def error_func(retry_list):
                retry_list.append(1)
                raise ConnectionError()

            try:
                retry(error_func)(retry_list)
            except Exception as e:
                assert type(e) == ConnectionError
            assert len(retry_list) == times

    def test_success_payout(self, tcontext):
        from app.business.investment import InvestmentDailyPayoutProc
        with tcontext:
            processor = InvestmentDailyPayoutProc()
            row = None  # 需要实际的UserDayInterestHistory对象
            result = processor._success_payout(row)
            pprint(result)

    def test_update_interest_statistic_time(self, tcontext):
        from app.business.investment import InvestmentDailyPayoutProc
        with tcontext:
            processor = InvestmentDailyPayoutProc()
            report_date = date(2025, 8, 15)
            s_model = InterestStatisticTime
            row = s_model.query.first()
            old_val = row.day_payout_date
            row.day_payout_date = report_date - timedelta(days=1)

            d_model = UserDayInterestHistory
            d_row = d_model.query.filter(d_model.report_date == report_date).first()
            d_row.status = d_model.Status.PENDING

            ret = processor._update_interest_statistic_time(report_date)
            assert ret is False

            d_row.status = d_model.Status.SUCCESS
            ret = processor._update_interest_statistic_time(report_date)
            assert ret is True

            row.day_payout_date = old_val
            db.session.commit()
            
    def test_clear_fragment_data(self, tcontext):
        from app.business.investment import InvestmentSchedule
        from app.business.clients.server import ServerClient
        with tcontext:
            # 给测试账户转入 低于 min_amount 的资产
            client = ServerClient()
            
            model = AssetInvestmentConfig
            configs = model.get_open_configs()
            min_amount_map = {row.asset: row.min_amount for row in configs}
            user_balance = client.get_user_balances(USER_ID, account_id=model.ACCOUNT_ID)
            test_asset, test_amount = "", Decimal()
            for asset, balance in user_balance.items():
                if not balance["frozen"] and balance["available"] < min_amount_map[asset]:
                    test_amount = (min_amount_map[asset] - balance["available"]) * Decimal("0.9")
                    client.add_user_balance(
                        user_id=USER_ID,
                        asset=asset,
                        amount=test_amount,
                        business=BalanceBusiness.SYSTEM,
                        business_id=int(random.randint(0, 10000) * random.randint(0, 10000)),
                        account_id=model.ACCOUNT_ID,
                    )
                    test_asset = asset
                    break

            with patch("app.business.investment.InvestmentDataProc") as proc:
                proc.get_dt_asset_user_snap_map = MagicMock(return_value={test_asset: {USER_ID: test_amount}})
                InvestmentSchedule().clear_fragment_data_schedule()
                
            balance = client.get_user_balances(USER_ID, test_asset, account_id=model.ACCOUNT_ID)
            assert balance[test_asset]["available"] == 0
            

@pytest.mark.usefixtures('module_setup')
class TestInvestmentSchedule:
    
    def test_hour_rate_schedule(self, tcontext):
        from app.schedules.investment import InvestmentSchedule
        with tcontext:
            result = InvestmentSchedule().update_conifg_rate_schedule()
            pprint(result)
    
    def test_asset_min_amount_schedule(self, tcontext):
        from app.schedules.investment import InvestmentSchedule
        with tcontext:
            result = InvestmentSchedule().update_conifg_min_amount_schedule()
            pprint(result)
    
    def test_investment_hourly_schedule(self, tcontext):
        """测试理财小时任务调度"""
        from app.schedules.investment import InvestmentSchedule
        with tcontext:
            result = InvestmentSchedule().hour_interest_schedule()
            pprint(result)
            
    def test_investment_day_schedule(self, tcontext):
        """测试理财日任务调度"""
        from app.schedules.investment import InvestmentSchedule
        with tcontext:
            result = InvestmentSchedule().day_interest_schedule()
            pprint(result)
            
    def test_investment_day_payout_schedule(self, tcontext):
        """测试理财日利息发放调度"""
        from app.schedules.investment import InvestmentSchedule
        with tcontext:
            result = InvestmentSchedule().day_payout_schedule()
            pprint(result)
            
    def test_update_summary_schedule(self, tcontext):
        """测试更新累计利息完成时间调度"""
        from app.schedules.investment import InvestmentSchedule
        with tcontext:
            result = InvestmentSchedule().update_summary_schedule()
            pprint(result)
            
    def test_update_investment_config_schedule(self, tcontext):
        """测试更新理财配置缓存调度"""
        from app.schedules.investment import update_investment_config_schedule
        with tcontext:
            update_investment_config_schedule()
            
    def test_update_investment_balance_rank_schedule(self, tcontext):
        """测试更新理财余额排名缓存调度"""
        from app.schedules.investment import update_investment_balance_rank_schedule
        with tcontext:
            update_investment_balance_rank_schedule()
            
    def test_update_investment_interest_rank_schedule(self, tcontext):
        """测试更新理财利息排名缓存调度"""
        from app.schedules.investment import update_investment_interest_rank_schedule
        with tcontext:
            update_investment_interest_rank_schedule()
            
    def test_update_day_rate_schedule(self, tcontext):
        """测试更新日利率缓存调度"""
        with tcontext:
            from app.schedules.investment import update_day_rate_schedule
            from app.schedules.prices import update_coin_income_rate_cache
            update_day_rate_schedule()
            update_coin_income_rate_cache()

    def test_update_user_invest_summary_task(self, tcontext):
        """测试更新用户理财汇总数据任务"""
        from app.schedules.investment import update_user_invest_summary_task
        from app.utils.date_ import today
        with tcontext:
            date_str = today().strftime("%Y-%m-%d")
            update_user_invest_summary_task(date_str)
            
    def test_update_yesterday_rate(self, tcontext):
        """测试更新昨日利率"""
        from app.caches.investment import InvestmentYesterdayARRCache
        with tcontext:
            cache = InvestmentYesterdayARRCache()
            cache.reload()
            pprint(cache.read_data())
            
            
    def test_update_user_invest_summary(self, tcontext):
        """测试更新用户理财汇总数据任务"""
        from app.utils.date_ import today
        with tcontext:
            day_model = UserDayInterestHistory
            last_date = day_model.query.order_by(day_model.report_date.desc()).first().report_date
            rows = day_model.query.group_by(
                day_model.user_id,
                day_model.asset
                ).with_entities(
                    day_model.user_id,
                    day_model.asset,
                    func.sum(day_model.interest_amount).label("total_interest")
                ).all()
                
            day_map = {(row.user_id, row.asset): row.total_interest for row in rows}
            
            model = UserInvestmentSummary
            summary_map = {
                (row.user_id, row.asset): row for row in model.query.all()
            }
            
            for (user_id, asset), amount in day_map.items():
                summary_row = summary_map.get((user_id, asset))
                if summary_row:
                    summary_row.amount = amount
                    summary_row.report_date = last_date
                else:
                    summary_row = model(
                        user_id=user_id,
                        asset=asset,
                        amount=amount,
                        report_date=last_date
                    )
                    db.session.add(summary_row)
            db.session.commit()
            
        
    def test_revert_interest_data(self, tcontext):
        from app.business.clients.server import ServerClient

        with tcontext:
            start_date = date(2025, 7, 29)
            day_model = UserDayInterestHistory
            rows = day_model.query.filter(day_model.report_date >= start_date).all()
            client = ServerClient()
            summary_row = UserInvestmentSummary.query.all()
            user_summary_map = {(row.user_id, row.asset): row for row in summary_row}

            for row in rows:
                print(row.id)
                client.add_user_balance(
                    user_id=row.user_id,
                    asset=row.asset,
                    amount=-row.interest_amount,
                    business=BalanceBusiness.SYSTEM,
                    business_id=row.id,
                    account_id=AssetInvestmentConfig.ACCOUNT_ID,
                )
                
            for row in rows:
                summary_row = user_summary_map.get((row.user_id, row.asset))
                if summary_row:
                    summary_row.amount -= row.interest_amount
                    summary_row.report_date = start_date - timedelta(days=1)
            db.session.commit()
                
            day_model.query.filter(day_model.report_date >= start_date).delete()
            UserDayInterestDetail.query.filter(UserDayInterestDetail.report_date >= start_date).delete()
            UserHourInterestHistory.query.filter(UserHourInterestHistory.report_hour >= start_date).delete()
            UserHourInterestDetail.query.filter(UserHourInterestDetail.report_hour >= start_date).delete()
            InterestStatisticTime.query.delete()
            db.session.commit()
            
    def test_asset_investment_report(self, tcontext):
        from app.business.report.investment import InvestmentReporter
        with tcontext:
            start_date = date(2025, 8, 15)
            while start_date < now():
                end_date = start_date + timedelta(days=1)
                InvestmentReporter().run(start_date, end_date)
                start_date = end_date

    def test_daily_investment(self, tcontext):
        from app.business.investment import DailyInvestmentReporter
        from app.business.investment import InvestmentDataProc
        with tcontext:
            st = date(2025, 8, 19)

            expect_date = {
                "id": 29865,
                "report_date": st,
                "asset": "USDT",
                "amount": "24551962.09264109",
                "usd": "24551962.09264109",
                "investment_interest_amount": "25231.66545709",
                "investment_interest_usd": "25231.66545709",
                "day_rate": "0.00102768",
                "user_count": 85,
                "interest_user_count": 85,
                "increase_investment_user": 0,
                "increase_interest_user": 0,
                "base_rate": "0.38612864",
                "base_interest_usd": "21997.74963448",
                "ladder_interest_usd": "10.78667616",
                "fixed_interest_usd": "3223.12914645",
            }

            et = st + timedelta(days=1)
            # report_hour = timestamp_to_datetime(1755486000)
            # return_value = InvestmentDataProc.get_dt_asset_user_snap_map(report_hour)
            # with patch("app.business.investment.InvestmentDataProc.get_dt_asset_user_snap_map") as mock_method:
                # mock_method.return_value = return_value
            DailyInvestmentReporter.daily_user_investment_interest_report(st, et)
            model = DailyInvestmentReport
            row = model.query.filter(model.asset == expect_date["asset"], model.report_date == st).first()
            new_data = {}
            for field in expect_date:
                new_val = getattr(row, field)
                if isinstance(new_val, Decimal):
                    new_val = amount_to_str(new_val, 8)
                new_data[field] = new_val
             
            assert new_data == expect_date

    def test_gen_daily_investment_report(self, tcontext):
        from app.business.investment import DailyInvestmentReporter
        with tcontext:
            st = date(2025, 8, 19)
            et = st + timedelta(days=1)
            DailyInvestmentReporter.daily_user_investment_interest_report(st, et)

    def test_check_statistic(self, tcontext):
        from app.business.investment import DailyInvestmentReporter
        with tcontext:
            st = date(2025, 8, 19)
            et = st + timedelta(days=1)
            s_model = InterestStatisticTime
            old_day_val = s_model.query.first().day_interest_date
            s_model.query.update({"day_interest_date": st - timedelta(days=1)})
            try:
                ret = DailyInvestmentReporter.daily_user_investment_interest_report(st, et)
            except Exception as e:
                print(f"Error: {e}")
            finally:
                s_model.query.update({"day_interest_date": old_day_val})
            assert ret is False

    def test_site_investment_report(self, tcontext):
        from app.business.report.investment import SiteInvestmentReporter
        with tcontext:
            start_date = date(2025, 8, 10)
            td = today()
            model = DailySiteInvestmentReport
            while start_date < td:
                model.query.filter(model.report_date == start_date).delete()
                db.session.commit()
                end_date = start_date + timedelta(days=1)
                SiteInvestmentReporter().run(start_date, end_date)
                start_date = end_date
                
    def test_send_staking_reward_schedule(self, tcontext):
        from app.schedules.staking import send_staking_reward_schedule
        with tcontext:
            send_staking_reward_schedule()
            
    def test_notify_hour_interest_data(self, tcontext):
        from app.business.investment import InvestmentHourlyProc
        with tcontext:
            processor = InvestmentHourlyProc()
            start = datetime(2025, 8, 20, 0, 0, 0, tzinfo=UTC)
            result = processor.notify_hour_interest_data(start)
            print(result)

    def test_update_daily_asset_business_report_schedule(self, tcontext):
        with tcontext:
            from app.schedules.reports.asset_business_report import update_daily_asset_business_report_schedule
            update_daily_asset_business_report_schedule()

    def test_run_daily_investment_report_schedule(self, tcontext):
        with tcontext:
            from app.schedules.investment import run_daily_investment_report_schedule
            run_daily_investment_report_schedule()

    def test_check_investment_balance(self, tcontext):
        with tcontext:
            from app.business.risk_control.investment import _check_investment_balance
            _check_investment_balance("USDT")

    def test_Investment_tag(self, tcontext):
        with tcontext:
            from app.business.user_tag.handlers.participate import InvestmentTimeTagHandler
            InvestmentTimeTagHandler().flush("")

    def test_p2p_tag(self, tcontext):
        with tcontext:
            from app.business.user_tag.handlers.participate import P2PTimeHandler
            P2PTimeHandler().flush("")
            
    def test_update_monthly_asset_investment_report(self, tcontext):
        """测试更新月度资产理财报表"""
        from app.business.report.investment import update_monthly_asset_investment_report
        from datetime import date
        
        with tcontext:
            # 测试数据准备
            start_month = date(2025, 8, 1)
            end_month = date(2025, 9, 1)
            
            # 执行函数
            result = update_monthly_asset_investment_report(start_month, end_month)
            
            # 验证结果
            asset = 'USDT'
            monthly_reports = MonthlyInvestmentReport.query.filter(
                MonthlyInvestmentReport.report_date == start_month,
                MonthlyInvestmentReport.asset == asset
            ).first()
            
            daily_rows = DailyInvestmentReport.query.filter(
                DailyInvestmentReport.report_date >= start_month,
                DailyInvestmentReport.report_date < end_month,
                DailyInvestmentReport.asset == asset
            ).all()
            data_map = defaultdict(Decimal)
            fields = [
                "amount",
                "usd",
                "investment_interest_amount",
                "investment_interest_usd",
                "increase_investment_user",
                "increase_interest_user",
            ] + DailyInvestmentReport.interest_fields()
            days = (end_month - start_month).days
            
            site_cur_user_bitmap = BitMap([])
            site_cur_interest_user_bitmap = BitMap([])
            for row in daily_rows:
                for field in fields:
                    data_map[field] += quantize_amount(getattr(row, field) or 0, 8)

                site_cur_user_bitmap.update(
                    BitMap.deserialize(row.site_cur_user_bitmap) if row.site_cur_user_bitmap else BitMap([])
                )
                site_cur_interest_user_bitmap.update(
                    BitMap.deserialize(row.site_cur_interest_user_bitmap) if row.site_cur_interest_user_bitmap else BitMap([])
                )
            for field in ["amount", "usd"]:
                data_map[field] = quantize_amount(data_map[field] / days, 8)
            data_map["user_count"] = len(site_cur_user_bitmap)
            data_map["interest_user_count"] = len(site_cur_interest_user_bitmap)

            real_data = dict()
            for k, v in data_map.items():
                real_data[k] = getattr(monthly_reports, k)
            assert data_map == real_data
            
    def test_update_monthly_investment_report(self, tcontext):
        """测试更新月度理财报表"""
        from app.business.report.investment import update_monthly_site_investment_report
        from datetime import date
        
        with tcontext:
            # 测试数据准备
            start_month = date(2025, 8, 1)
            end_month = date(2025, 9, 1)
            
            # 执行函数
            result = update_monthly_site_investment_report(start_month, end_month)
            
            # 验证结果
            monthly_reports = MonthlySiteInvestmentReport.query.filter(
                MonthlySiteInvestmentReport.report_date == start_month
            ).first()
            
            # 获取日报数据用于验证
            asset_rows = DailyInvestmentReport.query.filter(
                DailyInvestmentReport.report_date >= start_month,
                DailyInvestmentReport.report_date < end_month
            ).all()
            
            site_rows = DailySiteInvestmentReport.query.filter(
                DailySiteInvestmentReport.report_date >= start_month,
                DailySiteInvestmentReport.report_date < end_month
            ).all()
            
            days = (end_month - start_month).days
            
            site_cur_user_bitmap = BitMap()
            site_cur_interest_user_bitmap = BitMap()
            
            for item in asset_rows:
                site_cur_user_bitmap.update(
                    BitMap.deserialize(item.site_cur_user_bitmap) if item.site_cur_user_bitmap else BitMap([])
                )
                site_cur_interest_user_bitmap.update(
                    BitMap.deserialize(item.site_cur_interest_user_bitmap) if item.site_cur_interest_user_bitmap else BitMap([])
                )
            
            # 计算期望的字段数据
            data_map = defaultdict(Decimal)
            fields = [
                "increase_investment_user",
                "increase_interest_user", 
                "investment_interest_usd",
                "usd",
            ] + DailyInvestmentReport.interest_fields()
            
            for item in site_rows:
                for field in fields:
                    data_map[field] += getattr(item, field) or 0
            
            # 计算期望值
            expected_data = dict()
            expected_data["usd"] = quantize_amount(data_map.pop("usd", 0) / days, 8)
            expected_data["investment_user_count"] = len(site_cur_user_bitmap)
            expected_data["interest_user_count"] = len(site_cur_interest_user_bitmap)
            
            for k, v in data_map.items():
                expected_data[k] = v
            
            # 验证实际结果
            real_data = dict()
            for k, v in expected_data.items():
                real_data[k] = getattr(monthly_reports, k)
            
            assert expected_data == real_data


@pytest.mark.usefixtures('module_setup')
class TestInvestmentAdminApi:

    ASSETS = ["BTC", "ETH", "USDT", "CET", "USDC"]
    
    def test_investment_rank(self, tcontext):
        with tcontext:
            url = '/admin/investment/rank'
            client = tcontext.app.test_client()
            
            # 不同参数组合
            params_list = [
                {'asset': 'BTC', 'time_type': 'day_7'},
                {'asset': 'BTC', 'time_type': 'day_30'},
                {'asset': 'USDT', 'time_type': 'day_7'},
                {'asset': 'USDT', 'time_type': 'day_30'},
            ]
            
            for params in params_list:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_investment_balance_rank(self, tcontext):
        with tcontext:
            url = '/admin/investment/balance-rank'
            client = tcontext.app.test_client()
            
            # 不同参数组合
            params_list = [
                {'asset': 'BTC'},
                {'asset': 'USDT'},
            ]
            
            for params in params_list:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_investment_record(self, tcontext):
        with tcontext:
            url = '/admin/investment/record'
            client = tcontext.app.test_client()
            
            # 不同参数组合
            params_list = [
                # 划转记录测试
                {'history_type': 'TRANSFER'},
                {'history_type': 'TRANSFER', 'asset': 'BTC'},
                {'history_type': 'TRANSFER', 'user_id': USER_ID},
                {'history_type': 'TRANSFER', 'trans_type': 'IN'},
                {'history_type': 'TRANSFER', 'trans_type': 'OUT'},
                {'history_type': 'TRANSFER', 'asset': 'BTC', 'trans_type': 'IN'},
                {'history_type': 'TRANSFER', 'asset': 'BTC', 'user_id': USER_ID},
                # 利息记录测试
                {'history_type': 'INTEREST'},
                {'history_type': 'INTEREST', 'asset': 'BTC'},
                {'history_type': 'INTEREST', 'user_id': 1001},
            ]
            
            for params in params_list:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_investment_config_get(self, tcontext):
        with tcontext:
            url = '/admin/investment/config'
            client = tcontext.app.test_client()
            resp = client.get(url)
            ret = resp.json
            ret["data"].pop("all_assets")
            pprint(ret)
            assert resp.json["code"] == 0

    def test_investment_config_post(self, tcontext):
        from app.assets.asset import list_all_assets

        with tcontext:
            url = '/admin/investment/config'
            client = tcontext.app.test_client()
            
            model = AssetInvestmentConfig
            exists_assets = [i.asset for i in model.query.all()]
            
            random_asset = list(set(list_all_assets()) - set(exists_assets))[:4]
            print(random_asset)
            # 不同参数组合
            data_list = [
                {
                    'asset': random_asset[0],
                    'min_amount': '100',
                    'ladder_limit': '1000',
                    'ladder_rate': '0.05',
                    'fixed_rate': '0.03',
                    'remark': '测试配置'
                }
            ]
            
            for data in data_list:
                resp = client.post(url, json=data)
                pprint(resp.json)
                assert resp.json["code"] == 0
                
            assets = [i['asset'] for i in data_list]
            # 调用数据库删除
            model.query.filter(model.asset.in_(assets)).delete()
            db.session.commit()

    def test_investment_config_put(self, tcontext):
        with tcontext:
            url = '/admin/investment/config'
            client = tcontext.app.test_client()

            model = AssetInvestmentConfig
            exists_assets = {i.asset for i in model.query.all()}
            exists_assets = list(exists_assets - set(self.ASSETS))
            # 不同参数组合
            data_list = [
                {
                    'asset': exists_assets[0],
                    'min_amount': '200',
                    'ladder_limit': '2000',
                    'ladder_rate': '0.06',
                    'fixed_rate': '0.04',
                    'remark': '更新测试配置'
                },
                {
                    'asset': exists_assets[1],
                    'min_amount': '150',
                    'remark': '最小更新配置测试'
                },
                {
                    'asset': exists_assets[2],
                    'min_amount': '250',
                    'ladder_limit': '3000',
                    'ladder_rate': '0.07',
                    'remark': '更新阶梯配置'
                },
                {
                    'asset': exists_assets[3],
                    'min_amount': '350',
                    'fixed_rate': '0.05',
                    'remark': '更新固定利率配置'
                },
            ]
            
            for data in data_list:
                resp = client.put(url, json=data)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_investment_config_status_put(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            
            # 不同参数组合
            test_cases = [
                {'asset': 'SOL', 'status': 'CLOSE'},
                {'asset': 'SOL', 'status': 'OPEN'},
                {'asset': 'USDC', 'status': 'CLOSE'},
                {'asset': 'USDC', 'status': 'OPEN'},
            ]
            
            for test_case in test_cases:
                url = f'/admin/investment/config/{test_case["asset"]}/status'
                data = {'status': test_case['status']}
                resp = client.put(url, json=data)
                pprint(resp.json)
                assert resp.json["code"] == 0


@pytest.mark.usefixtures('module_setup')
class TestInvestmentResApi:
    
    def test_investment_accounts(self, tcontext):
        with tcontext:
            url = '/res/invest/accounts'
            client = tcontext.app.test_client()
            resp = client.get(url)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_investment_accounts_history(self, tcontext):
        with tcontext:
            url = '/res/invest/accounts/history'
            client = tcontext.app.test_client()
            
            # 测试不同参数组合
            test_cases = [
                {},  # 无参数
                {'page': 1, 'limit': 10},  # 基础分页
                {'coin_type': 'USDT'},  # 指定币种
                {'opt': 'in'},  # 指定操作类型
                {'page': 1, 'limit': 20, 'coin_type': 'USDT', 'opt': 'in'},  # 完整参数
            ]
            
            for params in test_cases:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_investment_transfer_get(self, tcontext):
        with tcontext:
            url = '/res/invest/transfer'
            client = tcontext.app.test_client()
            
            # 测试不同币种
            test_assets = ['USDT', 'BTC', 'ETH']
            
            for asset in test_assets:
                params = {'coin_type': asset}
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_investment_transfer_post(self, tcontext):
        from app.business.clients.server import SPOT_ACCOUNT_ID

        with tcontext:
            url = '/res/invest/transfer'
            client = tcontext.app.test_client()
            
            # 测试不同划转场景
            test_cases = [
                {
                    'from_account': SPOT_ACCOUNT_ID, 
                    'to_account': AssetInvestmentConfig.ACCOUNT_ID, 
                    'coin_type': 'USDT',
                    'amount': '100'
                },
                {
                    'from_account': AssetInvestmentConfig.ACCOUNT_ID,
                    'to_account': SPOT_ACCOUNT_ID,
                    'coin_type': 'USDT',
                    'amount': '100'
                }
            ]
            
            for data in test_cases:
                resp = client.post(url, json=data)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_investment_day_rate_history(self, tcontext):
        with tcontext:
            url = '/res/invest/day_rate/history'
            client = tcontext.app.test_client()
            
            # 测试不同时间范围和资产
            test_cases = [
                {'asset': 'USDT', 'time_type': '30d'},
                {'asset': 'BTC', 'time_type': '90d'},
                {'asset': 'ETH', 'time_type': '180d'},
                {'asset': 'USDC', 'time_type': '365d'},
            ]
            
            for params in test_cases:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0
                # 验证返回数据是列表格式
                data = resp.json.get("data", [])
                assert isinstance(data, list)

    def test_investment_summary(self, tcontext):
        with tcontext:
            url = '/res/invest/summary'
            client = tcontext.app.test_client()
            
            resp = client.get(url)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_investment_summary_new(self, tcontext):
        with tcontext:
            url = '/res/invest/summary/new'
            client = tcontext.app.test_client()
            
            # 测试不同参数组合
            test_cases = [
                {'asset': 'USDT'},  # 指定资产
                {},  # 无参数
            ]
            
            for params in test_cases:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_investment_recommend(self, tcontext):
        with tcontext:
            url = '/res/invest/recommend'
            client = tcontext.app.test_client()
            
            resp = client.get(url)
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_investment_history(self, tcontext):
        with tcontext:
            url = '/res/invest/history'
            client = tcontext.app.test_client()
            
            # 测试不同参数组合
            test_cases = [
                # {},  # 无参数
                # {'bus_type': 'INVESTMENT'},  # 指定类型
                # {'bus_type': 'INVESTMENT', 'type': 'INTEREST'},  # 指定类型
                # {'asset': 'USDT'},  # 指定资产
                {'type': 'INTEREST', 'bus_type': 'INVESTMENT'},  # 指定类型
                # {'next_key': '0-0-0'},  # 分页参数
                # {'asset': 'BTC', 'type': 'OUT', 'next_key': '0-0-0'},  # 完整参数
            ]
            
            for params in test_cases:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_investment_interest_preview(self, tcontext):
        """测试理财利息预览接口"""
        with tcontext:
            url = '/res/invest/interest/preview'
            client = tcontext.app.test_client()
            
            # 测试不同参数组合
            test_cases = [
                # 基础测试：无现有余额，新增投资
                {
                    'asset': 'USDT',
                    'exist_amount': '0',
                    'add_amount': '1000'
                },
                # 有现有余额，新增投资
                {
                    'asset': 'USDT',
                    'exist_amount': '1000',
                    'add_amount': '1000'
                },
                # 0余额
                {
                    'asset': 'USDT',
                    'exist_amount': '0',
                    'add_amount': '0'
                },
            ]
            
            for params in test_cases:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0

@pytest.mark.usefixtures('module_setup')
class TestInvestmentReportApi:
    
    def test_site_investment_report(self, tcontext):
        """测试全站理财报表接口"""
        with tcontext:
            url = '/admin/report/investment/site-investment-report'
            client = tcontext.app.test_client()
            
            # 测试不同参数组合
            test_cases = [
                {'report_type': 'daily'},  # 日报
                {'report_type': 'monthly'},  # 月报
                {'report_type': 'daily', 'start_date': '2025-07-01', 'end_date': '2025-07-31'},  # 日期范围
                {'report_type': 'monthly', 'start_date': '2025-01-01', 'end_date': '2025-12-31'},  # 月报日期范围
                {'report_type': 'daily', 'page': 1, 'limit': 10},  # 分页
                {'report_type': 'daily', 'export': True},  # 导出
            ]
            
            for params in test_cases:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_asset_detail(self, tcontext):
        """测试全站理财详情接口"""
        with tcontext:
            url = '/admin/report/investment/asset-detail'
            client = tcontext.app.test_client()
            
            # 测试不同参数组合
            test_cases = [
                {'report_type': 'daily'},  # 日报
                {'report_type': 'monthly'},  # 月报
                {'report_type': 'daily', 'report_date': '2025-07-27'},  # 指定日期
                {'report_type': 'monthly', 'report_date': '2025-07-01'},  # 指定月份
                {'report_type': 'daily', 'order': 'usd'},  # 按市值排序
                {'report_type': 'daily', 'order': 'investment_interest_usd'},  # 按收益排序
                {'report_type': 'daily', 'order': 'user_count'},  # 按用户数排序
            ]
            
            for params in test_cases:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0

    def test_asset_investment_report(self, tcontext):
        """测试币种理财报表接口"""
        with tcontext:
            url = '/admin/report/investment/asset-investment-report'
            client = tcontext.app.test_client()
            
            # 测试不同参数组合
            test_cases = [
                {'report_type': 'daily'},  # 日报
                {'report_type': 'monthly'},  # 月报
                {'report_type': 'daily', 'asset': 'BTC'},  # 指定币种
                {'report_type': 'daily', 'asset': 'USDT'},  # 指定币种
                {'report_type': 'daily', 'start_date': '2025-07-01', 'end_date': '2025-07-31'},  # 日期范围
                {'report_type': 'monthly', 'start_date': '2025-01-01', 'end_date': '2025-12-31'},  # 月报日期范围
                {'report_type': 'daily', 'page': 1, 'limit': 10},  # 分页
                {'report_type': 'daily', 'export': True},  # 导出
                {'report_type': 'daily', 'asset': 'BTC', 'start_date': '2025-07-01', 'end_date': '2025-07-31'},  # 完整参数
            ]
            
            for params in test_cases:
                resp = client.get(url, query_string=params)
                pprint(resp.json)
                assert resp.json["code"] == 0


@pytest.mark.usefixtures('module_setup')
class TestCoinIncomeRateResource:
    """测试币种收益利率接口"""
    
    def test_get_income_rate_success(self, tcontext):
        """测试成功获取币种收益利率"""
        from app.api.frontend.project import CoinIncomeRateResource
        
        with tcontext:
            # 测试常见币种
            test_coins = ["BNB"]
            for coin in test_coins:
                url = f'/res/vote2/project/{coin}/income-rate'
                client = tcontext.app.test_client()
                resp = client.get(url)
                pprint(resp.json)
                assert resp.json["code"] == 0
