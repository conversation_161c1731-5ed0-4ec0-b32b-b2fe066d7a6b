import io
import os
import ssl
import tempfile
from enum import Enum
from io import BytesIO
from typing import IO
from urllib.request import urlopen

from PIL import Image, ExifTags
import cv2


class _FileType(Enum):

    @classmethod
    def has_valid_type(cls, value):
        for item in cls.__members__.values():
            if item.value == value.lower():
                return True
        return False

    @classmethod
    def get_valid_type(cls, value):
        if cls.has_valid_type(value):
            return cls(value)
        return None


class VideoType(_FileType):
    MP4 = "mp4"
    MOV = "mov"


class ImageType(_FileType):
    PNG = "png"
    JPEG = "jpeg"
    JPG = "jpg"
    WEBP = "webp"


class FileType(_FileType):
    PDF = "pdf"


def _get_thumbnail_key(image_key, w, h, size):
    path, filename = os.path.split(image_key)
    if w > 0 and h > 0:
        size_key = f"{w}x{h}"
    else:
        size_key = size
    if path == "":
        thumbnail_key = f"compress/{size_key}-{filename}"
    else:
        thumbnail_key = f"compress/{path}/{size_key}-{filename}"
    return thumbnail_key


class ThumbnailSize:

    def cal_thumbnail_size(self, origin_w: int, origin_h: int) -> (int, int):
        raise NotImplementedError

    def gen_thumbnail_key(self, origin_file_key: str) -> str:
        raise NotImplementedError

    def is_original_size(self) -> bool:
        """判断是否为原始尺寸（不缩放）"""
        return False  # 默认不是原始尺寸

    @property
    def quality(self):
        return self._quality


class ThumbnailDimensions(ThumbnailSize):
    def __init__(self, w: int, h: int, quality=80) -> None:
        if w <= 0 or h <= 0:
            raise ValueError

        self.w: int = w
        self.h: int = h
        self._quality = quality

    def cal_thumbnail_size(self, origin_w: int, origin_h: int) -> (int, int):
        return self.w, self.h

    def gen_thumbnail_key(self, origin_file_key: str) -> str:
        return _get_thumbnail_key(origin_file_key, self.w, self.h, None)


class ThumbnailScale(ThumbnailSize):
    class Size(Enum):
        SMALL = 'SMALL'
        MEDIUM = 'MEDIUM'
        LARGE = 'LARGE'
        ORIGINAL = 'ORIGINAL'   # 不缩放，这个通常只在获取视频封面的时候有用

    _scale_map = {
        'small': 0.3,
        'medium': 0.5,
        'large': 0.8,
        'original': 1.0,
    }

    def __init__(
            self, scale: str | float | Size, base_w: int = None, base_h: int = None, quality=80
    ) -> None:
        """
        base_w与base_h为缩放的基本尺寸，当图片缩放后，其w与h不为base_w或base_h的整数倍时，其会被压缩为base_w或base_h整数倍
        """
        if isinstance(scale, str):
            self.scale_key = scale
            if scale not in self._scale_map:
                raise ValueError
            self.scale = self._scale_map[scale]
        elif isinstance(scale, self.Size):
            scale_str = scale.value.lower()
            self.scale_key = scale_str
            self.scale = self._scale_map[scale_str]
        elif isinstance(scale, float):
            self.scale = float(scale)
            self.scale_key = f"scale-{scale}"
        else:
            raise ValueError

        self.base_w = None
        if base_w is not None:
            self.base_w = base_w

        self.base_h = None
        if base_h is not None:
            self.base_h = base_h

        self._quality = quality

    def cal_thumbnail_size(self, origin_w: int, origin_h: int) -> (int, int):
        w = int(origin_w * self.scale)
        h = int(origin_h * self.scale)

        if self.base_w is not None and w > self.base_w:
            w -= w % self.base_w
        if self.base_h is not None and h > self.base_h:
            h -= h % self.base_h

        return w, h

    def gen_thumbnail_key(self, origin_file_key: str) -> str:
        return _get_thumbnail_key(origin_file_key, 0, 0, self.scale_key)
    
    def is_original_size(self) -> bool:
        """判断是否为原始尺寸（不缩放）"""
        return self.scale == 1.0


DEFAULT_THUMBNAIL_SCALE = [
    ThumbnailScale('small', 100, 100),
    ThumbnailScale('medium', 100, 100),
    ThumbnailScale('large', 100, 100),
]


def open_file_from_url(url: str):
    context = ssl.create_default_context()
    context.check_hostname = False
    context.verify_mode = ssl.CERT_NONE
    return urlopen(url, context=context)


def handle_file_thumbnail(
        file_key: str, file: bytes | str, suffix: str,
        size: ThumbnailSize | list[ThumbnailSize] | None = None, thumbnail_key_func=None
) -> dict[str, IO]:

    if size is None:
        return {file_key: io.BytesIO(file)}

    if VideoType.has_valid_type(suffix):
        return _handle_video_thumbnail(file_key, file, size, suffix, thumbnail_key_func)
    elif ImageType.has_valid_type(suffix):
        return _handle_image_thumbnail(file_key, file, size, thumbnail_key_func)
    elif FileType.has_valid_type(suffix):
        return _handle_file_thumbnail(file_key, file)
    return {}


def _handle_file_thumbnail(file_key, file: bytes | str):

    if isinstance(file, str):
        file = open_file_from_url(file)

    return {file_key: file}


def get_video_thumbnail_key(video_key):
    prefix = video_key.split('.')[0]
    image_key = f"{prefix}_video_thumbnail.webp"
    return image_key


def _handle_video_thumbnail(video_key, file: bytes | str, size: ThumbnailSize, suffix, thumbnail_key_func=None):
    """
    处理视频，返回第一帧缩略图
    """
    image_key = get_video_thumbnail_key(video_key)

    if isinstance(file, bytes):
        with tempfile.NamedTemporaryFile(suffix=f".{suffix}", delete=True) as tmp:
            tmp.write(file)
            tmp.flush()
            tmp_path = tmp.name
            cap = cv2.VideoCapture(tmp_path)
            ret, frame = cap.read()
            cap.release()
    else:
        cap = cv2.VideoCapture(file)
        ret, frame = cap.read()
        cap.release()

    # 获取首帧
    origin_h, origin_w, _ = frame.shape
    if isinstance(size, ThumbnailSize):
        size = [size]

    res = {}
    for _size in size:
        thumbnail_key = thumbnail_key_func(image_key, 0, 0, _size) \
            if thumbnail_key_func else _size.gen_thumbnail_key(image_key)

        # 压缩首帧
        w, h = _size.cal_thumbnail_size(origin_w, origin_h)
        if _size.is_original_size():
            # 原始尺寸，直接返回首帧
            resized_frame = frame
        else:
            resized_frame = cv2.resize(frame, (w, h))
        ret, _bytes = cv2.imencode('.webp',
                                   resized_frame,
                                   [int(cv2.IMWRITE_WEBP_QUALITY), _size.quality])

        if ret:
            image_binary = BytesIO()
            image_binary.write(_bytes)
            image_binary.seek(0)
            res[thumbnail_key] = image_binary

    return res


def _handle_image_thumbnail(image_key, file: bytes | str, size, thumbnail_key_func=None):
    if isinstance(file, str):
        image = Image.open(open_file_from_url(file))
    else:
        image = Image.open(io.BytesIO(file))
    orig_w, orig_h = image.size

    if isinstance(size, ThumbnailSize):
        size = [size]

    image = correct_image_orientation(image)

    res = {}
    for _size in size:
        image_copy = image.copy()
        w, h = _size.cal_thumbnail_size(orig_w, orig_h)
        image_copy.thumbnail((w, h), Image.BILINEAR)
        thumbnail_key = thumbnail_key_func(image_key, w, h, _size) \
            if thumbnail_key_func else _size.gen_thumbnail_key(image_key)
        # 设置压缩质量webp
        image_binary = BytesIO()
        image_copy.save(image_binary, 'webp', quality=_size.quality)
        image_binary.seek(0)
        res[thumbnail_key] = image_binary

    return res


def correct_image_orientation(image):
    # 处理移动设备的视频旋转信息
    try:
        exif = image._getexif()
        if exif is not None:
            orientation_tag = None
            orientation = None
            for tag, value in ExifTags.TAGS.items():
                if value == 'Orientation':
                    orientation_tag = tag
                    break
            if orientation_tag is not None:
                orientation = exif.get(orientation_tag, None)

            if orientation == 3:
                image = image.rotate(180, expand=True)
            elif orientation == 6:
                image = image.rotate(270, expand=True)
            elif orientation == 8:
                image = image.rotate(90, expand=True)
    except Exception:
        pass  # 处理失败，返回原图
    return image
