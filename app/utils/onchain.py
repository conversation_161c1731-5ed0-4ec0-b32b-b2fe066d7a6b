import re
from typing import Union
from typing import Callable

from base58 import b58decode

from decimal import Decimal
from decimal import Context
from decimal import ROUND_DOWN
from decimal import localcontext

AmountType = Union[Decimal, str, int]

context = Context(prec=78)


class OnchainAddressSOL:
    @classmethod
    def normalise_address(cls, address: str) -> str:
        return address

    @classmethod
    def validate_address(cls, address: str) -> bool:
        try:
            decoded = b58decode(address)
        except ValueError:
            return False
        if len(decoded) != 32:
            return False
        return True


class OnchainAddressETH:

    @classmethod
    def normalise_address(cls, address: str) -> str:
        return address.lower()

    @classmethod
    def validate_address(cls, address: str) -> bool:
        if not address.startswith('0x'):
            return False
        if len(address) != 42:
            return False
        try:
            bytes.fromhex(address[2:])
        except ValueError:
            return False
        return True


class OnchainAddressBSC(OnchainAddressETH):
    pass


def normalise_time(seconds: int, t: int, is_end_time: bool = False) -> int:
    t = int(t // seconds * seconds)
    if is_end_time:
        t += seconds
    return t


def quantize_amount(amount: AmountType, decimals: int, rounding: str = ROUND_DOWN) -> Decimal:
    return Decimal(amount).quantize(Decimal(10) ** -decimals, rounding, context=context)


def amount_to_str(amount: AmountType, decimals: int = None, with_separator: bool = False) -> str:
    amount = Decimal(amount)
    if decimals is not None:
        amount = quantize_amount(amount, decimals)
    return f'{amount.normalize(context=context):{",f" if with_separator else "f"}}'


def _decimal_do(x: AmountType, y: AmountType, func: Callable, decimals: int = None) -> Decimal:
    with localcontext() as ctx:
        ctx.prec = context.prec
        z = func(Decimal(x), Decimal(y))
        if decimals:
            z = quantize_amount(z, decimals)
        return z


def decimal_add(x: AmountType, y: AmountType, decimals: int = None) -> Decimal:
    return _decimal_do(x, y, lambda _x, _y: _x + _y, decimals=decimals)


def decimal_sub(x: AmountType, y: AmountType, decimals: int = None) -> Decimal:
    return _decimal_do(x, y, lambda _x, _y: _x - _y, decimals=decimals)


def decimal_mul(x: AmountType, y: AmountType, decimals: int = None) -> Decimal:
    return _decimal_do(x, y, lambda _x, _y: _x * _y, decimals=decimals)


def decimal_div(x: AmountType, y: AmountType, decimals: int = None) -> Decimal:
    return _decimal_do(x, y, lambda _x, _y: _x / _y, decimals=decimals)


def raw_to_decimal(amount: AmountType, decimals: int) -> Decimal:
    return decimal_div(amount, 10 ** decimals)


def decimal_to_raw(amount: AmountType, decimals: int) -> Decimal:
    return decimal_mul(amount, 10 ** decimals)


def format_decimal_display(value: AmountType, significant_digits: int = 0) -> str:
    """
    :param value: AmountType 类型的值
    :param significant_digits: 有效小数位数，0 表示显示全部
    :return: 格式化后的字符串，大于1时，展示小数点后n位；小于1时，展示小数点后有效数字n位；当数值过小时展示优化，如 "0.0₈12345678"；
    """
    if not value:
        return '0'
    value = Decimal(value)
    if not value:
        return '0'
    if value >= Decimal('1'):
        return amount_to_str(value, significant_digits)
    # 将 Decimal 转换为标准化字符串
    value_str = format(value.normalize(), 'f').rstrip('0').rstrip('.')

    # 使用正则表达式匹配数值部分
    pattern = r'^(-?0\.)(0*)([1-9][0-9]*)$'
    match = re.match(pattern, value_str)
    if not match:
        return value_str

    # 提取匹配的各个部分
    prefix = match.group(1)  # 如 "0." 或 "-0."
    zeros = match.group(2)  # 如 "00000000"
    digits = match.group(3)  # 如 "123"

    # 转换零的数量为下标
    zero_count = len(zeros)
    if zero_count >= 5:
        subscript = _to_subscript(zero_count)
        # 处理有效数字
        if significant_digits > 0:
            digits = digits[:significant_digits]
        return f"{prefix}0{subscript}{digits}"
    else:
        return amount_to_str(value, significant_digits + zero_count)


def _to_subscript(number: int) -> str:
    """将数字转换为下标形式"""
    subscript_map = {
        '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
        '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉'
    }
    return ''.join(subscript_map.get(d, d) for d in str(number))
