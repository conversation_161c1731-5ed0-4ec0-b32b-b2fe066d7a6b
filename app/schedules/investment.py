# -*- coding: utf-8 -*-
import datetime
from datetime import timedelta

from celery.schedules import crontab

from app.business import (
    route_module_to_celery_queue,
    CeleryQueues,
    lock_call,
)
from app.business.report.investment import (
    InvestmentReporter,
    SiteInvestmentReporter,
    update_monthly_site_investment_report,
    update_monthly_asset_investment_report,
)
from app.caches.investment import (
    InterestRankCache,
    Investment7DaysEARCache,
    InvestmentBalanceRankCache,
    InvestmentConfigCache,
    InvestmentYesterdayARRCache,
    InvestmentIncTotalIncomeCache,
    InvestmentIncYesterdayIncomeCache,
)
from app.models.activity import Coupon, CouponDailyBalanceHistory
from app.business.investment import InvestmentIncInterestOperation, InvestmentSchedule
from app.models.daily import DailyInvestmentReport, DailySiteInvestmentReport
from app.models.monthly import MonthlyInvestmentReport, MonthlySiteInvestmentReport
from app.schedules.reports.utils import get_monthly_report_date
from app.utils import scheduled
from app.utils.date_ import next_month, now, convert_datetime, today


route_module_to_celery_queue(__name__, CeleryQueues.INVESTMENT)


@scheduled(crontab(minute="5,10,15", hour="*/1"))
@lock_call()
def investment_hourly_schedule():
    """理财小时任务调度"""
    InvestmentSchedule().hour_interest_schedule()


@scheduled(crontab(minute="10,20,30", hour="*/1"))
@lock_call()
def investment_asset_rate_schedule():
    """理财小时利率更新"""
    InvestmentSchedule().update_conifg_rate_schedule()


@scheduled(crontab(minute="10", hour="0-3"))
@lock_call()
def investment_asset_min_amount_schedule():
    """理财小时最小金额更新"""
    InvestmentSchedule().update_conifg_min_amount_schedule()


@scheduled(crontab(minute="10,20", hour="0-3"))
@lock_call()
def investment_day_schedule():
    """理财日任务调度"""
    InvestmentSchedule().day_interest_schedule()


@scheduled(crontab(minute="20,40", hour="0-3"))
@lock_call()
def investment_day_payout_schedule():
    """理财日利息发放调度"""
    InvestmentSchedule().day_payout_schedule()


@scheduled(crontab(minute="30,50", hour="1-3"))
@lock_call()
def investment_clear_fragment_data_schedule():
    """理财碎片数据清理"""
    InvestmentSchedule().clear_fragment_data_schedule()


@scheduled(crontab(minute="*/5"))
@lock_call()
def update_investment_config_schedule():
    InvestmentConfigCache.reload()


@scheduled(crontab(hour="*/1", minute="10"))
@lock_call()
def update_investment_balance_rank_schedule():
    InvestmentBalanceRankCache.reload()


@scheduled(crontab(minute="10", hour="1-3"))
@lock_call()
def update_investment_interest_rank_schedule():
    InterestRankCache.reload()


@scheduled(crontab(minute="*/10"))
@lock_call()
def update_day_rate_schedule():
    Investment7DaysEARCache().reload()
    InvestmentYesterdayARRCache().reload()


@scheduled(crontab(minute=30, hour=0))
@lock_call()
def investment_inc_rate_coupon_interest_schedule():
    """理财加息券的加息任务"""
    today_ = convert_datetime(now(), "day")
    InvestmentIncInterestOperation.increase_interest(today_)

    InvestmentIncTotalIncomeCache.reload()
    InvestmentIncYesterdayIncomeCache.reload(inc_date=today_.date())


@scheduled(crontab(minute="*/30"))
@lock_call()
def investment_inc_rate_coupon_balance_retry_schedule():
    """理财加息券的加息资产变更-重试任务"""
    today_ = today()
    st = today_ - timedelta(days=4)
    et = today_ - timedelta(days=1)
    rows = CouponDailyBalanceHistory.query.filter(
        CouponDailyBalanceHistory.date >= st,
        CouponDailyBalanceHistory.date <= et,
        CouponDailyBalanceHistory.coupon_type == Coupon.CouponType.INVESTMENT_INCREASE_RATE,
        CouponDailyBalanceHistory.status != CouponDailyBalanceHistory.Status.FINISHED,
    ).all()
    if rows:
        InvestmentIncInterestOperation.transfer_from_history(rows)


@scheduled(crontab(minute='20,40', hour="0-3"))
@lock_call()
def run_daily_investment_report_schedule():
    InvestmentReporter().dispatch()
    SiteInvestmentReporter().dispatch()


@scheduled(crontab(minute=30, hour="4-6", day_of_month=1))
@lock_call()
def update_monthly_asset_investment_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    start_month = get_monthly_report_date(MonthlyInvestmentReport, DailyInvestmentReport)

    if not start_month:
        return

    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_asset_investment_report(start_month, end_month)
        start_month = end_month


@scheduled(crontab(minute=30, hour="4-6", day_of_month=1))
@lock_call()
def update_monthly_investment_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    start_month = get_monthly_report_date(MonthlySiteInvestmentReport, DailySiteInvestmentReport)

    if not start_month:
        return
    
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_site_investment_report(start_month, end_month)
        start_month = end_month
