from decimal import Decimal

from collections import defaultdict

from celery.schedules import crontab

from flask import current_app

from app.config import config

from app.common import CeleryQueues
from app.common.onchain import Chain
from app.common.onchain import CHAIN_MONEY_MAPPING

from app.business import send_alert_notice
from app.business.clients.wallet import WalletClient
from app.business.onchain.base import schedule_enable
from app.business.onchain.base import OnchainAddressHelper
from app.business.onchain.token import get_token

from app.models import db
from app.models.onchain import OnchainToken
from app.models.onchain import OnchainTokenBalance
from app.models.onchain import OnchainAssetLiability

from app.caches.onchain import OnchainTokenQuoteCache
from app.caches.onchain import OnchainThirdPartyAPICallsCache
from app.caches.onchain import OnchainTokenUpdatedAtAssetLiabilityCache

from app.utils import scheduled
from app.utils import route_module_to_celery_queue
from app.utils.onchain import decimal_add
from app.utils.onchain import decimal_sub
from app.utils.onchain import decimal_mul
from app.utils.onchain import decimal_div
from app.utils.onchain import amount_to_str
from app.utils.onchain import quantize_amount

route_module_to_celery_queue(__name__, CeleryQueues.ONCHAIN)


def count_user_balance() -> dict[int, Decimal]:
    latest_id = 0
    limit = 10000
    count = defaultdict(Decimal)
    while True:
        items = OnchainTokenBalance.query.filter(
            OnchainTokenBalance.id > latest_id,
        ).order_by(
            OnchainTokenBalance.id.asc(),
        ).limit(limit).all()
        if not items:
            break
        latest_id = items[-1].id

        for item in items:
            user_balance = decimal_add(item.available, item.frozen)
            token_balance = count[item.token_id]
            count[item.token_id] = decimal_add(token_balance, user_balance)

        if len(items) < limit:
            break

    return count


def _calculate_income_rate(row: OnchainAssetLiability) -> Decimal | None:
    if not Decimal(row.wallet_balance) and not Decimal(row.user_balance):
        return Decimal()
    if not Decimal(row.wallet_balance) and Decimal(row.user_balance):
        return Decimal(-1)
    if Decimal(row.wallet_balance) and not Decimal(row.user_balance):
        return Decimal(1)
    return decimal_div(decimal_sub(row.wallet_balance, row.user_balance), row.wallet_balance, decimals=4)


def send_asset_liability_msg(rows: list[OnchainAssetLiability]):
    # 检查是否超过阈值并发送Slask
    if not schedule_enable():
        current_app.logger.info('send_asset_liability_msg not enable')
        return

    asset_liability_config = config['ONCHAIN_CONFIGS']['asset_liability']
    income_positive_limit = Decimal(asset_liability_config['income_positive_limit'])
    income_negative_limit = Decimal(asset_liability_config['income_negative_limit'])
    income_positive_volume_limit = Decimal(asset_liability_config['income_positive_volume_limit'])
    income_negative_volume_limit = Decimal(asset_liability_config['income_negative_volume_limit'])
    onchain_risk_notice = config['ADMIN_CONTACTS']['onchain_risk_notice']
    onchain_risk_at = ','.join(config['ADMIN_CONTACTS']['slack_at']['onchain_risk_notices'])

    d_100 = Decimal('100')

    quote_data = OnchainTokenQuoteCache().get_many([row.token_id for row in rows])
    updated_at_cache = OnchainTokenUpdatedAtAssetLiabilityCache()
    for row in rows:
        income_rate = _calculate_income_rate(row)
        limit = income_positive_limit if income_rate >= Decimal() else income_negative_limit
        token_id = row.token_id
        if abs(income_rate) <= limit:
            continue
        if not updated_at_cache.need_update(token_id):
            continue
        token = get_token(token_id)
        income = decimal_sub(row.wallet_balance, row.user_balance)
        volume_usd = decimal_mul(income, quote_data.get(row.token_id, {}).get('price', Decimal()), decimals=8)
        volume_limit = income_positive_volume_limit if volume_usd >= Decimal() else income_negative_volume_limit
        if abs(volume_usd) <= volume_limit:
            continue
        send_alert_notice(
            f'*【链上资产负债不平】*\n'
            f'链(合约地址): {token.chain.name}({token.contract})\n'
            f'symbol(name): {token.symbol}({token.name})\n'
            f'平台权益: {income} {token.symbol} (阈值为{limit * d_100}%={abs(income_rate) * d_100}%)\n'
            f'权益市值: {volume_usd} USD\n'
            f'请及时处理',
            onchain_risk_notice,
            at=onchain_risk_at,
        )
        updated_at_cache.update_one(token_id)


def _need_add_new_record(token_id: int) -> bool:
    """这里资产负债的逻辑是半小时检查钱包链上地址余额与web余额表统计的差异, 可能会出现某个Token本来有值但是又买空了的情况,
    买空时该Token钱包与web余额表统计结果都为0, 如果直接不存储该全0结果, 会导致该Token最后的对账记录一直是有值的,
    但是直接存储全0结果的话, 会导致多出来很多全0的数据造成DB存储资源浪费,
    因此这里处理逻辑为只存储第一次全0的结果, 具体实现就是全0时检查该Token上一个对账记录是否为全0, 是的话不新增记录"""
    last_record: OnchainAssetLiability = OnchainAssetLiability.query.filter(
        OnchainAssetLiability.token_id == token_id,
    ).order_by(
        OnchainAssetLiability.id.desc(),
    ).first()
    if not last_record:
        return True
    return Decimal(last_record.wallet_balance) != Decimal() or Decimal(last_record.user_balance) != Decimal()


@scheduled(crontab(minute="0,30", hour='*/1'))
def update_onchain_asset_liability():
    wallet_assets = WalletClient().get_onchain_asset_statistics()

    usdt_address_map = {
        Chain.SOL: CHAIN_MONEY_MAPPING[Chain.SOL]['USDT']['contract'],
        Chain.ERC20: CHAIN_MONEY_MAPPING[Chain.ERC20]['USDT']['contract'],
        Chain.BSC: CHAIN_MONEY_MAPPING[Chain.BSC]['USDT']['contract'],
    }

    token_map = {
        token.id: token
        for token in OnchainToken.query.filter().all() if token.contract != usdt_address_map[token.chain]
    }  # 不包括USDT
    chain_contract_token_id_map = defaultdict(dict)
    for token_id, token in token_map.items():
        chain_contract_token_id_map[token.chain][token.contract] = token_id

    user_balance_map = count_user_balance()

    wallet_balance_map = defaultdict(Decimal)
    for item in wallet_assets:
        chain = {
            'SOL': Chain.SOL,
            'ERC20': Chain.ERC20,
            'BSC': Chain.BSC,
        }.get(item['chain'])
        if not chain:
            continue
        if not item['identity']:
            continue
        contract = OnchainAddressHelper(chain).normalise_address(item['identity'])
        if contract not in chain_contract_token_id_map[chain]:
            continue
        wallet_balance_map[chain_contract_token_id_map[chain][contract]] = item['amount']

    all_token_ids = set(user_balance_map.keys()) | set(wallet_balance_map.keys())

    rows = []
    for token_id in all_token_ids:
        if token_id not in token_map:
            continue
        if not _need_add_new_record(token_id):
            continue
        wallet_balance = quantize_amount(wallet_balance_map.get(token_id, Decimal()), decimals=8)
        user_balance = quantize_amount(user_balance_map.get(token_id, Decimal()), decimals=8)
        rows.append(OnchainAssetLiability(
            token_id=token_id,
            wallet_balance=amount_to_str(wallet_balance, 8),
            user_balance=amount_to_str(user_balance, 8),
        ))
    db.session.add_all(rows)
    db.session.commit()

    send_asset_liability_msg(rows)


@scheduled(crontab(hour='0', minute='10'))
def third_party_api_calls_schedule():
    """每天执行第三方接口调用次数预警"""
    data = OnchainThirdPartyAPICallsCache().get()
    OnchainThirdPartyAPICallsCache().clear()
    message = '\n'.join(f'*{k}*: {v}' for k, v in data.items())
    total_map = defaultdict(int)
    for k, v in data.items():
        total_map[k.split(':', maxsplit=1)[0]] += v
    total_message = '\n'.join(f'*{k}总计*: {v}' for k, v in total_map.items())
    send_alert_notice(
        f'*【第三方接口调用每日统计】*\n{message}\n\n{total_message}',
        config['ADMIN_CONTACTS']['onchain_notice'],
    )
