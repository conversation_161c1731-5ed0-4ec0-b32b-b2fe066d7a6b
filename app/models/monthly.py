# -*- coding: utf-8 -*-
import json
from enum import Enum
from decimal import Decimal

from pyroaring import BitMap

from . import AMOUNT_STRING_LEN
from .base import db, ModelBase
from .referral import Ambassador
from ..common import AccountBalanceType, ReportPlatform, Language


class MonthlyIncomeReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint(
            'report_date', 'asset',
            name='report_date_asset_unique'),
    )
    report_date = db.Column(db.Date, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False)
    total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    pay_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    net_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    net_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyRealIncomeReport(ModelBase):
    """
    实收报表
    """

    __table_args__ = (
        db.UniqueConstraint(
            'report_date', 'asset',
            name='report_date_asset_unique'),
    )
    report_date = db.Column(db.Date, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyAdminNetIncomeReport(ModelBase):
    """Admin账号净收报表"""
    __table_args__ = (
        db.UniqueConstraint(
            'report_date', 'asset',
            name='report_date_asset_unique'),
    )
    report_date = db.Column(db.Date, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlySmallCoinExchangeReport(ModelBase):
    """
    小币兑换报表
    """
    __table_args__ = (
        db.UniqueConstraint(
            'user_id', 'report_date', 'asset',
            name='user_id_report_date_asset_unique'),
    )
    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyInsuranceExchangeReport(ModelBase):
    """ 保险基金兑换报表-月报 """

    __table_args__ = (db.UniqueConstraint("report_date", "user_id", "asset", name="report_date_user_id_asset_unique"),)
    report_date = db.Column(db.Date, nullable=False)
    user_id = db.Column(db.Integer, nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyLossProcessHistory(ModelBase):
    """币种亏损平衡月报表"""
    __table_args__ = (
        db.UniqueConstraint('asset', 'report_date', 'process_type',
                            name='asset_report_date_process_type_unique'),
    )

    class ProcessType(Enum):
        # 购入亏损币种
        BUY_FROM_MARKET = 'BUY_FROM_MARKET'
        # 卖出收入币种
        SELL_TO_MARKET = 'SELL_TO_MARKET'

    process_type = db.Column(db.StringEnum(ProcessType), nullable=False)
    report_date = db.Column(db.Date, nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyCetBuyBackReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint(
            'user_id', 'report_date', 'asset', 'report_type',
            name='user_id_report_date_asset_unique'),
    )

    class ReportType(Enum):
        SPEND = 'spend'
        BUY_BACK = 'buy_back'
        TRANSFER = 'transfer'

    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    report_type = db.Column(db.StringEnum(ReportType), nullable=False)


class MonthlySiteFiatOrderReport(ModelBase):

    class ReportType(Enum):
        ALL = 'all'
        BUY = 'buy'
        SELL = 'sell'

    report_date = db.Column(db.Date, nullable=False)
    report_type = db.Column(db.StringEnum(ReportType), nullable=True)

    apply_user_count = db.Column(db.Integer, nullable=False)
    deal_user_count = db.Column(db.Integer, nullable=False)
    apply_count = db.Column(db.Integer, nullable=False)
    deal_count = db.Column(db.Integer, nullable=False)
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 累积使用人数
    user_count = db.Column(db.Integer, nullable=False)
    # 新增人数
    new_user_count = db.Column(db.Integer, nullable=False)


class MonthlyThirdPartyFiatOrderReport(ModelBase):

    class ReportType(Enum):
        ALL = 'all'
        BUY = 'buy'
        SELL = 'sell'

    report_date = db.Column(db.Date, nullable=False)
    report_type = db.Column(db.StringEnum(ReportType), nullable=True)

    apply_user_count = db.Column(db.Integer, nullable=False)
    deal_user_count = db.Column(db.Integer, nullable=False)
    apply_count = db.Column(db.Integer, nullable=False)
    deal_count = db.Column(db.Integer, nullable=False)
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    third_party = db.Column(db.String(64), nullable=False)
    # 累积使用人数
    user_count = db.Column(db.Integer, nullable=False)
    # 新增人数
    new_user_count = db.Column(db.Integer, nullable=False)


class MonthlyAssetFiatOrderReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint(
            'report_date', 'asset',
            name='report_date_asset_unique'),
    )

    report_date = db.Column(db.Date, nullable=False)
    asset = db.Column(db.String(32), nullable=False)

    apply_user_count = db.Column(db.Integer, nullable=False)
    deal_user_count = db.Column(db.Integer, nullable=False)
    apply_count = db.Column(db.Integer, nullable=False)
    deal_count = db.Column(db.Integer, nullable=False)
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyInvestmentReport(ModelBase):
    """
    理财统计记录表
    """

    __table_args__ = (
        db.UniqueConstraint('asset', 'report_date', name='asset_report_date'),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)

    asset = db.Column(db.String(32), nullable=False)
    # 理财币种总数
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 理财市值
    usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 理财收益
    investment_interest_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 理财收益市值
    investment_interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 理财人数
    user_count = db.Column(db.Integer, nullable=False)
    # 理财收益人数
    interest_user_count = db.Column(db.Integer, nullable=False)
    # 新增理财用户数
    increase_investment_user = db.Column(db.Integer, nullable=False, default=0)
    # 新增理财收益用户数
    increase_interest_user = db.Column(db.Integer, nullable=False, default=0)
    
    base_interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, default=0, comment='基础收益')
    ladder_interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, default=0, comment='阶梯补贴')
    fixed_interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, default=0, comment='固定补贴')


class MonthlySiteInvestmentReport(ModelBase):
    """
    理财统计记录表
    """

    report_date = db.Column(db.Date, nullable=False, index=True)
    # 理财市值
    usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 理财收益市值
    investment_interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 新增理财用户数
    increase_investment_user = db.Column(db.Integer, nullable=False, default=0)
    # 新增利息用户数
    increase_interest_user = db.Column(db.Integer, nullable=False, default=0)
    # 当月理财用户数
    investment_user_count = db.Column(db.Integer, nullable=False, default=0)
    # 当月利息用户数
    interest_user_count = db.Column(db.Integer, nullable=False, default=0)
    base_interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, default=0, comment='基础收益')
    ladder_interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, default=0, comment='阶梯补贴')
    fixed_interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, default=0, comment='固定补贴')


class MonthlyPerpetualTradeReport(ModelBase):
    report_date = db.Column(db.Date, nullable=False, unique=True)

    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deal_user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False,
                               default='')
    deal_user_count = db.Column(db.Integer, nullable=False, default=0)
    # 合约活跃人数：时间段内有交易或有持仓的人数
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    position_user_count = db.Column(db.Integer, nullable=False, default=0)  # 持仓人数
    deal_count = db.Column(db.Integer, nullable=False, default=0)
    market_maker_trade_usd = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    market_maker_fee_usd = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    cet_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    avg_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    avg_mm_fee_rate = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    avg_normal_fee_rate = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    normal_fee_rate = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    normal_trade_rate = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    cet_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    increase_trade_user = db.Column(
        db.Integer, nullable=False, index=False, default=0)
    position_amount_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    real_leverage = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    market_count = db.Column(db.Integer, nullable=False, default=0)
    api_user_count = db.Column(db.Integer, nullable=False, default=0)


class MonthlyPerpetualMarketReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('report_date', 'market',
                            name='report_date_market_unique'),
    )
    report_date = db.Column(db.Date, nullable=False)

    market = db.Column(db.String(32), nullable=False)
    deal_user_count = db.Column(
        db.Integer, nullable=False, index=False, default=0)
    deal_user_list = db.Column(
        db.MYSQL_MEDIUM_TEXT, nullable=False, index=False, default='')
    deal_count = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deal_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deal_usd_percent = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    buy_count = db.Column(db.Integer, nullable=False, index=False, default=0)
    buy_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    sell_count = db.Column(db.Integer, nullable=False, index=False, default=0)
    sell_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    position_amount_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    real_leverage = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 合约活跃人数：时间段内有交易或有持仓的人数
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    position_user_count = db.Column(db.Integer, nullable=False, default=0)  # 持仓人数


class MonthlyPerpetualInsuranceReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('report_date', 'asset',
                            name='report_date_asset_unique'),
    )

    report_date = db.Column(db.Date, nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    increase_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0)
    decrease_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0)
    total_balance = db.Column(db.MYSQL_DECIMAL_26_8, default=0)

    # 实际收入
    real_increase_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0)
    # 实际支出
    real_decrease_amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0)
    # 实际保险基金余额
    real_balance = db.Column(db.MYSQL_DECIMAL_26_8, default=0)
    # 划转数量
    transfer = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class MonthlySpotTradeReport(ModelBase):
    """
    现货全站交易月报
    """
    report_date = db.Column(db.Date, nullable=False, unique=True)

    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deal_user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False,
                               default='')
    deal_user_count = db.Column(db.Integer, nullable=False, default=0)
    deal_count = db.Column(db.Integer, nullable=False, default=0)
    market_maker_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                       default=0)
    market_maker_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                     default=0)
    cet_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    avg_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    avg_mm_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                default=0)
    avg_normal_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                    default=0)
    normal_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                default=0)
    normal_trade_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                  default=0)
    cet_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    cet_user_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    increase_trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    market_count = db.Column(db.Integer, nullable=False, default=0)
    api_user_count = db.Column(db.Integer, nullable=False, default=0)

    @classmethod
    def get_or_create(cls, report_date, auto_commit=False):
        record = cls.query.filter(cls.report_date == report_date).first()
        if not record:
            record = cls(report_date=report_date)
            if auto_commit:
                db.session.add(record)
                db.session.commit()
        return record


class MonthlySpotTradeCoinReport(ModelBase):
    """
    现货交易币种月报
    """
    __table_args__ = (
        db.UniqueConstraint('report_date', 'coin',
                            name='report_date_coin_unique'),
    )

    report_date = db.Column(db.Date, nullable=False)
    coin = db.Column(db.String(32), nullable=False)

    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                             default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                          default=0)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deal_user_count = db.Column(db.Integer, nullable=False, default=0)
    deal_user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False,
                               default='')
    deal_count = db.Column(db.Integer, nullable=False, default=0)
    taker_buy_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                 default=0)
    taker_buy_count = db.Column(db.Integer, nullable=False, default=0)
    taker_sell_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                  default=0)
    taker_sell_count = db.Column(db.Integer, nullable=False, default=0)
    normal_deal_volume_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通成交
    normal_deal_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通成交比例
    normal_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通手续费
    normal_fee_usd_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通手续费比例

    @classmethod
    def get_or_create(cls, report_date, coin, auto_commit=False):
        record = cls.query.filter(cls.report_date == report_date,
                                  cls.coin == coin).first()
        if not record:
            record = cls(report_date=report_date, coin=coin)
            if auto_commit:
                db.session.add(record)
                db.session.commit()
        return record


class MonthlySpotTradeMarketReport(ModelBase):
    """
    现货交易市场月报
    """
    __table_args__ = (db.UniqueConstraint('report_date', 'market',
                                          name='report_date_market_unique'),
                      )

    report_date = db.Column(db.Date, nullable=False)
    market = db.Column(db.String(32), nullable=False)

    stock_asset = db.Column(db.String(32), nullable=False)
    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                             default=0)
    trade_volume = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                             default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                          default=0)
    deal_usd_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deal_user_count = db.Column(db.Integer, nullable=False, default=0)
    deal_user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False,
                               default='')
    deal_count = db.Column(db.Integer, nullable=False, default=0)
    taker_buy_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                 default=0)
    taker_buy_count = db.Column(db.Integer, nullable=False, default=0)
    taker_sell_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                  default=0)
    taker_sell_count = db.Column(db.Integer, nullable=False, default=0)
    normal_deal_volume = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通成交
    normal_deal_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通成交比例
    normal_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通手续费
    normal_fee_usd_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通手续费比例

    @classmethod
    def get_or_create(cls, report_date, market, auto_commit=False):
        record = cls.query.filter(cls.report_date == report_date,
                                  cls.market == market).first()
        if not record:
            record = cls(report_date=report_date, market=market)
            if auto_commit:
                db.session.add(record)
                db.session.commit()
        return record


class MonthlySpotTradeAreaRateReport(ModelBase):
    """
    现货交易区占比月报
    TODO: 已废弃，待删除
    """
    report_date = db.Column(db.Date, nullable=False, unique=True)

    deal_count = db.Column(db.Integer, nullable=False, default=0)
    deal_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                          default=0)
    btc_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                        default=0)
    bch_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                        default=0)
    eth_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                        default=0)
    usdt_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                         default=0)
    usdc_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                         default=0)
    tusd_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                         default=0)
    pax_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                        default=0)
    cet_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                        default=0)

    @classmethod
    def get_or_create(cls, report_date, auto_commit=False):
        record = cls.query.filter(cls.report_date == report_date).first()
        if not record:
            record = cls(report_date=report_date)
            if auto_commit:
                db.session.add(record)
                db.session.commit()
        return record


class MonthlySpotTradeCoinRateReport(ModelBase):
    """
    现货交易币种占比月报
    TODO: 已废弃，待删除
    """
    __table_args__ = (
    db.UniqueConstraint('report_date', 'coin', name='report_date_coin_unique'),
    )

    report_date = db.Column(db.Date, nullable=False)
    coin = db.Column(db.String(32), nullable=False)

    deal_count = db.Column(db.Integer, nullable=False, default=0)
    deal_user_count = db.Column(db.Integer, nullable=False, default=0)
    deal_user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False,
                               default='')
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                          default=0)
    total_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                default=0)
    trade_usd_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                               default=0)
    btc_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                        default=0)
    bch_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                        default=0)
    eth_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                        default=0)
    usdt_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                         default=0)
    usdc_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                         default=0)
    tusd_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                         default=0)
    pax_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                        default=0)
    cet_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                        default=0)

    @classmethod
    def get_or_create(cls, report_date, coin, auto_commit=False):
        record = cls.query.filter(cls.report_date == report_date,
                                  cls.coin == coin).first()
        if not record:
            record = cls(report_date=report_date, coin=coin)
            if auto_commit:
                db.session.add(record)
                db.session.commit()
        return record


class MonthlyCoinTrade(ModelBase):
    """
    每月交易区统计表
    """

    report_date = db.Column(db.Date, nullable=False, index=True)

    trading_area = db.Column(db.String(32), nullable=False)
    deal_volume = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                            default=0)
    deal_volume_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                default=0, index=True)
    deal_user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False,
                               default='')
    deal_user_count = db.Column(db.Integer, nullable=False, default=0,
                                index=True)
    deal_count = db.Column(db.Integer, nullable=False, default=0, index=True)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    normal_deal_volume = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通成交
    normal_deal_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通成交比例
    normal_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通手续费
    normal_fee_usd_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 普通手续费比例
    market_count = db.Column(db.Integer, nullable=False, default=0)

    @classmethod
    def get_or_create(cls, report_date, trading_area, auto_commit=False):
        record = cls.query.filter(cls.trading_area == trading_area,
                                  cls.report_date == report_date).first()
        if not record:
            record = cls(trading_area=trading_area, report_date=report_date)
            if auto_commit:
                db.session.add(record)
                db.session.commit()
        return record


class MonthlyDepositWithdrawalReport(ModelBase):
    """
    每月充提统计表
    """
    __table_args__ = (
        db.UniqueConstraint('report_date', 'asset',
                            name='report_date_asset_unique'),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False, index=True)  # 为空字符串时 代表全站(全部币种的统计)

    # deposit_* 包括站内ViaBTC充值和链上充值数据, 但不包括站内转账
    deposit_count = db.Column(db.Integer, nullable=False)
    deposit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                               default=0)
    deposit_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deposit_user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False,
                                  default='')
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)

    withdrawal_count = db.Column(db.Integer, nullable=False)
    withdrawal_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                  default=0)
    withdrawal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                               default=0)
    withdrawal_user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False,
                                     default='')
    withdrawal_user_count = db.Column(db.Integer, nullable=False)
    withdrawal_user_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                    default=0)
    withdrawal_on_chain_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    # ViaBTC内部充值接口 相关统计字段
    local_deposit_count = db.Column(db.Integer, nullable=False, default=0)
    local_deposit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    local_deposit_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    local_deposit_user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default="")
    local_deposit_user_count = db.Column(db.Integer, nullable=False, default=0)

    @classmethod
    def get_or_create(cls, report_date, asset, auto_commit=False):
        record = cls.query.filter(cls.asset == asset,
                                  cls.report_date == report_date).first()
        if not record:
            record = cls(asset=asset, report_date=report_date)
            if auto_commit:
                db.session.add(record)
                db.session.commit()
        return record


class MonthlyChainDepositWithdrawalReport(ModelBase):
    """每月公链充提报表"""

    __table_args__ = (
        db.UniqueConstraint('report_date', 'chain', 'gas_asset',
                            name='report_date_chain_gas_asset_unique'),
    )   # chain可能存在多个gas_asset，因此月表的唯一约束比日报表增加gas_asset
    report_date = db.Column(db.Date, nullable=False, index=True)
    chain = db.Column(db.String(32), nullable=False, index=True)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)
    token_deposit_count = \
        db.Column(db.Integer, nullable=False, default=0)  # Token充值笔数
    main_asset_deposit_count = \
        db.Column(db.Integer, nullable=False, default=0)  # 原生充值笔数
    token_withdrawal_count = \
        db.Column(db.Integer, nullable=False, default=0)  # Token提现笔数
    main_asset_withdrawal_count = \
        db.Column(db.Integer, nullable=False, default=0)  # 原生提现笔数
    withdrawal_user_count = db.Column(db.Integer, nullable=False, default=0)
    gas_fee = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # usd计价的Gas费
    gas_asset = \
        db.Column(db.String(32), nullable=False, default='')  # gas费主币
    gas_asset_amount = db.Column(
        db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 链上主币gas费支出
    tx_count = db.Column(db.Integer, nullable=False, default=0)
    # 交易笔数-由我们发起的tx数量，包括充值钱包转热钱包、用户提现、合约地址生成、转冷钱包等
    withdrawal_user_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                    default=0)  # 提现费收入（USD）


class MonthlyChainAssetDepositWithdrawalReport(ModelBase):
    """每月公链币种充提报表"""

    __table_args__ = (
        db.UniqueConstraint('report_date', 'chain', 'asset',
                            name='report_date_chain_asset_unique'),
    )
    report_date = db.Column(db.Date, nullable=False, index=True)
    chain = db.Column(db.String(32), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_count = db.Column(db.Integer, nullable=False, default=0)  # 充值笔数
    withdrawal_count = db.Column(db.Integer, nullable=False, default=0)  # 提现笔数
    withdrawal_user_count = db.Column(db.Integer, nullable=False, default=0)
    gas_asset = db.Column(db.String(32), nullable=False, default='')  # gas费币种
    gas_asset_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # Gas费币数
    gas_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # usd计价的Gas费
    # 交易笔数-由我们发起的tx数量，包括充值钱包转热钱包、用户提现、合约地址生成、转冷钱包等
    tx_count = db.Column(db.Integer, nullable=False, default=0)
    withdrawal_user_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 提现费收入（USD）
    deposit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deposit_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    withdrawal_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    withdrawal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class MonthlyUserReport(ModelBase):
    report_date = db.Column(db.Date, nullable=False, unique=True)

    total_user = db.Column(db.Integer, nullable=False, default=0)

    user_bind_email = db.Column(db.Integer, nullable=False, default=0)
    user_email_verified = db.Column(db.Integer, nullable=False, default=0)
    user_bind_mobile = db.Column(db.Integer, nullable=False, default=0)
    user_bind_totp = db.Column(db.Integer, nullable=False, default=0)
    user_bind_2fa = db.Column(db.Integer, nullable=False, default=0)
    user_trade_password = db.Column(db.Integer, nullable=False, default=0)

    increase_user = db.Column(db.Integer, nullable=False, default=0)
    increase_user_bind_2fa = db.Column(db.Integer, nullable=False, default=0)
    increase_sub_user = db.Column(db.Integer, nullable=False, default=0)
    increase_cet_user = db.Column(db.Integer, nullable=False, default=0)

    total_refer_user = db.Column(db.Integer, nullable=False, default=0)
    increase_refer_user = db.Column(db.Integer, nullable=False, default=0)
    increase_trade_user = db.Column(db.Integer, nullable=False, default=0)

    sign_in_user = db.Column(db.Integer, nullable=False, default=0)
    deposit_user = db.Column(db.Integer, nullable=False, default=0)
    withdraw_user = db.Column(db.Integer, nullable=False, default=0)
    local_transfer_user = db.Column(db.Integer, nullable=False, default=0)
    trade_user = db.Column(db.Integer, nullable=False, default=0)
    active_user = db.Column(db.Integer, nullable=False, default=0)

    active_trade_user = db.Column(db.Integer, nullable=False, default=0)
    active_spot_user = db.Column(db.Integer, nullable=False, default=0)
    active_margin_user = db.Column(db.Integer, nullable=False, default=0)
    active_option_user = db.Column(db.Integer, nullable=False, default=0)
    active_future_user = db.Column(db.Integer, nullable=False, default=0)
    active_perpetual_user = db.Column(db.Integer, nullable=False, default=0)
    asset_user = db.Column(db.Integer, nullable=False, default=0)

    total_kyc = db.Column(db.Integer, nullable=False, default=0)
    increase_kyc = db.Column(db.Integer, nullable=False, default=0)
    amm_user = db.Column(db.Integer, nullable=False, default=0)
    exchange_user = db.Column(db.Integer, nullable=False, default=0)  # 兑换用户数
    api_user = db.Column(db.Integer, nullable=False, default=0)  # api成交用户数
    # 环比值
    total_user_ring_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deposit_user_ring_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    active_user_ring_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    active_trade_user_ring_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class MonthlyInnerTransferReport(ModelBase):
    """
    内部转账月报
    """
    report_date = db.Column(db.Date, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False, index=True)

    inner_transfer_user_count = db.Column(db.Integer, nullable=False,
                                          default=0)
    inner_transfer_count = db.Column(db.Integer, nullable=False, default=0)
    inner_transfer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    inner_transfer_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    give_red_packet_user_count = db.Column(db.Integer, nullable=False,
                                           default=0)
    give_red_packet_count = db.Column(db.Integer, nullable=False, default=0)
    give_red_packet_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    give_red_packet_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    receive_red_packet_user_count = db.Column(db.Integer, nullable=False,
                                              default=0)
    receive_red_packet_count = db.Column(db.Integer, nullable=False, default=0)
    receive_red_packet_amount = db.Column(db.MYSQL_DECIMAL_26_8,
                                          nullable=False)
    receive_red_packet_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    give_red_packet_new_user_count = db.Column(db.Integer, nullable=False, default=0)
    receive_red_packet_new_user_count = db.Column(db.Integer, nullable=False, default=0)
    new_register_user_count = db.Column(db.Integer, nullable=False, default=0)


class MonthlyReferReport(ModelBase):
    """
    返佣月报
    """
    report_date = db.Column(db.Date, nullable=False, unique=True)
    # refer邀请注册人数
    referrer_count = db.Column(db.Integer, nullable=False, default=0)
    # refer交易人数
    invitee_count = db.Column(db.Integer, nullable=False, default=0)
    # refer交易人数比例
    invitee_trade_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # Refer交易总额
    invitee_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # Refer贡献手续费
    invitee_all_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 返佣人数，当日收到返佣的人数
    inviter_count = db.Column(db.Integer, nullable=False, default=0)
    # 返佣CET数
    refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 普通返佣折合USD金额
    normal_refer_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 大使返佣USDT数
    ambassador_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 大使代理返佣USDT数
    ambassador_agent_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # broker经纪商返佣USDT数
    broker_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 返佣金额
    refer_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 返佣比例
    refer_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 全站手续费
    all_trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 总返佣比例
    total_refer_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    new_invitee_count = db.Column(db.Integer, nullable=False, default=0)  # 新增refer交易人数
    # refer总人数
    total_invitee = db.Column(db.Integer, nullable=False, default=0)
    # refer注册人数占比
    invitee_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 新增refer交易人数占比
    new_trade_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyReferralCodeAssetDetail(ModelBase):
    """ 用户邀请码的返佣发放, user_id和referral_id纬度的聚合, 月报 """

    class Type(Enum):
        REFERRAL = 'referral'
        AMBASSADOR = 'ambassador'

    date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    referral_id = db.Column(db.Integer, db.ForeignKey('referral.id'))
    type = db.Column(db.StringEnum(Type), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    perpetual_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyMarginReport(ModelBase):

    report_date = db.Column(db.Date, nullable=False, index=True)

    market_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    loan_user_count = db.Column(db.Integer, nullable=False, default=0)
    flat_user_count = db.Column(db.Integer, nullable=False, default=0)

    loan_order_count = db.Column(db.Integer, nullable=False, default=0)
    flat_order_count = db.Column(db.Integer, nullable=False, default=0)

    loan_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    flat_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    interest_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    loan_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    flat_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    increase_margin_user_count = db.Column(db.Integer, nullable=False,
                                          default=0)
    average_loan_balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    average_interest_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    real_leverage = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 实际杠杆倍数


class MonthlyMarginAssetReport(ModelBase):

    report_date = db.Column(db.Date, nullable=False, index=True)

    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    loan_user_count = db.Column(db.Integer, nullable=False, default=0)
    flat_user_count = db.Column(db.Integer, nullable=False, default=0)

    asset = db.Column(db.String(32), nullable=False, index=True)

    loan_order_count = db.Column(db.Integer, nullable=False, default=0)
    flat_order_count = db.Column(db.Integer, nullable=False, default=0)

    loan_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    flat_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    interest_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    loan_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    flat_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    # 平均借贷余额数量
    average_loan_balance_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 平均借贷余额市值
    average_loan_balance_usd = db.Column(db.MYSQL_DECIMAL_26_8,
                                            nullable=False, default=0)
    # 平均利率
    average_interest_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class MonthlyMarginMarketReport(ModelBase):

    report_date = db.Column(db.Date, nullable=False, index=True)

    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    loan_user_count = db.Column(db.Integer, nullable=False, default=0)
    flat_user_count = db.Column(db.Integer, nullable=False, default=0)

    market = db.Column(db.String(32), nullable=False, index=True)

    loan_order_count = db.Column(db.Integer, nullable=False, default=0)
    flat_order_count = db.Column(db.Integer, nullable=False, default=0)

    loan_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    flat_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    interest_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    loan_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    flat_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    average_loan_balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    average_interest_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    real_leverage = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 实际杠杆倍数


class MonthlyAmbassadorReport(ModelBase):

    report_date = db.Column(db.Date, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))   # 大使
    referral_count = db.Column(db.Integer, nullable=False)
    delta_referral_count = db.Column(db.Integer, nullable=False, default=0)  # 当月新增的推荐人数
    delta_referral_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 当月新用户交易量（USD）
    delta_referral_trade_count = db.Column(db.Integer, nullable=False, default=0)  # 当月新交易用户数
    asset = db.Column(db.String(32), nullable=False)
    referral_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    deal_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 被邀请人交易量
    deal_user_count = db.Column(db.Integer, nullable=False)
    deposit_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    trade_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    level = db.Column(db.StringEnum(Ambassador.Level), nullable=False)

    user = db.relationship(
        'User',
        backref=db.backref('ambassador_monthly_report', lazy='dynamic'))


class MonthlyCountryUserReport(ModelBase):
    report_date = db.Column(db.Date, nullable=False, index=True)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)  # 活跃用户数
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 交易用户数
    new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 注册用户数
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)  # 资产用户数
    total_user_count = db.Column(db.Integer, nullable=False, default=0)  # 总用户数
    increase_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 当月新增交易用户数
    ambassador_user_count = db.Column(db.Integer, nullable=False, default=0)  # 大使用户数
    ambassador_refer_new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 大使邀请的注册用户数
    country = db.Column(db.String(32), nullable=False, default=0, index=True)
    self_reg_new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 自然注册用户
    normal_refer_new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 普通邀请的注册用户数
    spot_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 现货用户数
    perpetual_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 合约用户数
    exchange_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 兑换用户数
    margin_user_count = db.Column(db.Integer, nullable=False, default=0)  # 杠杆用户数
    amm_user_count = db.Column(db.Integer, nullable=False, default=0)  # AMM用户数
    cet_new_user_count = db.Column(db.Integer, nullable=False, default=0)  # CET新增用户数
    balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='country 总资产')


class MonthlyAreaUserReport(ModelBase):
    report_date = db.Column(db.Date, nullable=False, index=True)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)  # 活跃用户数
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 交易用户数
    new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 注册用户数
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)  # 资产用户数
    total_user_count = db.Column(db.Integer, nullable=False, default=0)  # 总用户数
    increase_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 当月新增交易用户数
    ambassador_user_count = db.Column(db.Integer, nullable=False, default=0)  # 大使用户数
    ambassador_refer_new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 大使邀请的注册用户数
    area = db.Column(db.String(32), nullable=False, default=0, index=True)
    self_reg_new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 自然注册用户
    normal_refer_new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 普通邀请的注册用户数
    spot_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 现货用户数
    perpetual_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 合约用户数
    exchange_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 兑换用户数
    margin_user_count = db.Column(db.Integer, nullable=False, default=0)  # 杠杆用户数
    amm_user_count = db.Column(db.Integer, nullable=False, default=0)  # AMM用户数
    cet_new_user_count = db.Column(db.Integer, nullable=False, default=0)  # CET新增用户数
    balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='area 总资产')

class MonthlyCountryTradeReport(ModelBase):
    """交易国家分布月报表"""
    __table_args__ = (
        db.UniqueConstraint(
            "report_date", "country", name="report_date_country_unique"
        ),
    )

    report_date = db.Column(db.Date, nullable=False)
    country = db.Column(db.String(32), nullable=False)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 交易用户数
    spot_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 现货用户数
    perpetual_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 合约用户数
    exchange_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 兑换用户数
    spot_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                  default=0)  # 现货交易额
    perpetual_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                       default=0)  # 合约交易额    
    exchange_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                       default=0)  # 兑换交易额
    spot_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 现货手续费
    perpetual_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                     default=0)  # 合约手续费
    exchange_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                     default=0)  # 兑换手续费
    avg_spot_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                    default=0)  # 人均现货手续费
    avg_perpetual_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                         default=0)  # 人均合约手续费
    avg_exchange_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                         default=0)  # 兑换合约手续费
    invitee_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # Refer贡献手续费
    refer_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 收到返佣金额
    refer_expense_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 返佣支出金额
    refer_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 返佣比例

class MonthlyAreaTradeReport(ModelBase):
    """交易国家分布月报表"""
    __table_args__ = (
        db.UniqueConstraint(
            "report_date", "area", name="report_date_country_unique"
        ),
    )

    report_date = db.Column(db.Date, nullable=False)
    area = db.Column(db.String(32), nullable=False)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 交易用户数
    spot_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 现货用户数
    perpetual_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 合约用户数
    exchange_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 兑换用户数
    spot_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                  default=0)  # 现货交易额
    perpetual_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                       default=0)  # 合约交易额    
    exchange_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                       default=0)  # 兑换交易额
    spot_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 现货手续费
    perpetual_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                     default=0)  # 合约手续费
    exchange_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                     default=0)  # 兑换手续费
    avg_spot_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                    default=0)  # 人均现货手续费
    avg_perpetual_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                         default=0)  # 人均合约手续费
    avg_exchange_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                         default=0)  # 兑换合约手续费
    invitee_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # Refer贡献手续费
    refer_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 收到返佣金额
    refer_expense_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 返佣支出金额
    refer_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 返佣比例


class MonthlyLanguageTradeReport(ModelBase):
    """交易语言分布月报表"""
    __table_args__ = (
        db.UniqueConstraint(
            "report_date", "language", name="report_date_language_unique"
        ),
    )

    report_date = db.Column(db.Date, nullable=False)
    language = db.Column(db.String(32))
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 交易用户数
    spot_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 现货用户数
    perpetual_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 合约用户数
    exchange_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 兑换用户数
    spot_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                  default=0)  # 现货交易额
    perpetual_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                       default=0)  # 合约交易额    
    exchange_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                       default=0)  # 兑换交易额
    spot_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 现货手续费
    perpetual_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                     default=0)  # 合约手续费
    exchange_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                     default=0)  # 兑换手续费
    avg_spot_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                    default=0)  # 人均现货手续费
    avg_perpetual_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                         default=0)  # 人均合约手续费
    avg_exchange_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                         default=0)  # 兑换合约手续费
    invitee_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # Refer贡献手续费
    refer_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 收到返佣金额
    refer_expense_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 返佣支出金额
    refer_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 返佣比例

class MonthlyAmbassadorReferralReport(ModelBase):
    report_date = db.Column(db.Date, nullable=False, index=True)
    ambassador_agent_count = db.Column(db.Integer, nullable=False, default=0)
    ambassador_count = db.Column(db.Integer, nullable=False, default=0)
    new_ambassador_agent_count = db.Column(db.Integer, nullable=False, default=0)
    new_ambassador_count = db.Column(db.Integer, nullable=False, default=0)
    refer_count = db.Column(db.Integer, nullable=False, default=0)
    refer_deal_count = db.Column(db.Integer, nullable=False, default=0)
    refer_deal_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    refer_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    refer_ambassador_count = db.Column(db.Integer, nullable=False, default=0)
    ambassador_referral_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    refer_ambassador_agent_count = db.Column(db.Integer, nullable=False, default=0)
    ambassador_agent_referral_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    ambassador_referral_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    new_user_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 新增用户占比
    fee_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 净手续费占比
    only_agent_count = db.Column(db.Integer, nullable=False, default=0)  # 纯代理数量
    ambassador_refer_user_count = db.Column(db.Integer, nullable=False, default=0)  # 大使邀请总用户
    new_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 周期内当前在职大使推荐新增邀请交易用户
    new_trade_user_site_count = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 周期内全站新增推荐新增邀请交易用户

    trade_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # refer交易人数占比


class MonthlySpotMarginBurstReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint(
            "report_date", "market", name="report_date_market_unique"
        ),
    )
    report_date = db.Column(db.Date, nullable=False, index=True)
    base_asset = db.Column(db.String(32), nullable=False)
    quote_asset = db.Column(db.String(32), nullable=False)
    base_asset_burst_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    base_asset_burst_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    base_asset_fund_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    base_asset_fund_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 交易货币穿仓金额：有穿仓仓位的交易货币爆仓金额
    base_asset_cross_liq_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 交易货币穿仓市值
    base_asset_cross_liq_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    quote_asset_burst_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    quote_asset_burst_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    quote_asset_fund_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    quote_asset_fund_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 定价货币穿仓金额：有穿仓仓位的定价货币爆仓金额
    quote_asset_cross_liq_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 定价货币穿仓市值
    quote_asset_cross_liq_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 交易货币爆仓费
    base_asset_liq_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    base_asset_liq_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 定价货币爆仓费
    quote_asset_liq_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    quote_asset_liq_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    market = db.Column(db.String(32), nullable=False, default=0)
    user_count = db.Column(db.Integer, nullable=False, default=0)
    burst_count = db.Column(db.Integer, nullable=False, default=0)
    # 穿仓笔数：有穿仓仓位的爆仓笔数
    cross_liq_count = db.Column(db.Integer, nullable=False, default=0)
    user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')


class MonthlyPerpetualMarginBurstReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint(
            "report_date", "market", name="report_date_market_unique"
        ),
    )
    report_date = db.Column(db.Date, nullable=False, index=True)
    burst_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 爆仓量(单位：交易币种，eg：BTC)
    burst_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    interest_insurance_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    interest_insurance_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    liquidation_insurance_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    liquidation_insurance_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    market = db.Column(db.String(32), nullable=False, default=0)
    user_count = db.Column(db.Integer, nullable=False, default=0)
    burst_count = db.Column(db.Integer, nullable=False, default=0)
    user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    long_user_count = db.Column(db.Integer, nullable=False, default=0)
    long_user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    short_burst_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)   # 空单爆仓量
    long_burst_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)    # 多单爆仓量
    cross_liq_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 触发穿仓爆仓数量
    cross_liq_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    cross_liq_count = db.Column(db.Integer, nullable=False, default=0)
    auto_deleverage_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 触发自动减仓爆仓数量
    auto_deleverage_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    auto_deleverage_count = db.Column(db.Integer, nullable=False, default=0)
    long_burst_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 多单爆仓量(USD)
    short_burst_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 空单爆仓量(USD)
    long_burst_count = db.Column(db.Integer, nullable=False, default=0)              # 多单爆仓笔数
    short_burst_count = db.Column(db.Integer, nullable=False, default=0)             # 空单爆仓笔数


class MonthlyLiquidityReport(ModelBase):

    report_date = db.Column(db.Date, nullable=False, index=True)
    market = db.Column(db.String(32), nullable=False, index=True)
    liquidity_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    added_liquidity_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    removed_liquidity_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    added_base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    added_quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    removed_base_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    removed_quote_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    added_user_count = db.Column(db.Integer, nullable=False, default=0)
    removed_user_count = db.Column(db.Integer, nullable=False, default=0)
    added_count = db.Column(db.Integer, nullable=False, default=0)
    removed_count = db.Column(db.Integer, nullable=False, default=0)
    amm_user_deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                  default=0)  # AMM账号成交额


class MonthlyAmmMarketSummaryReport(ModelBase):

    monthly_liquidity_report_id = db.Column(db.Integer, db.ForeignKey('monthly_liquidity_report.id'), nullable=False, unique=True)
    liquidity_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    refund_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    market_count = db.Column(db.Integer, nullable=False, default=0)
    interest_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deal_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class MonthlyMakerCashBackReport(ModelBase):
    """ 现货maker返佣-月报 """

    __table_args__ = (
        db.UniqueConstraint(
            "report_date", "asset", name="report_date_asset_unique"
        ),
    )

    report_date = db.Column(db.Date, nullable=False)

    # 返佣币种
    asset = db.Column(db.String(32), nullable=False)
    # 返佣数量
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 返佣金额（USD）
    usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 参与返佣的交易额（USD）
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    # 返佣人数
    user_count = db.Column(db.Integer, nullable=False, default=0)
    user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default="")


class MonthlyLanguageUserReport(ModelBase):
    """ 报表-国家报表-语言分布月报 """

    __table_args__ = (
        db.UniqueConstraint(
            "report_date", "language", name="report_date_language_unique"
        ),
    )

    report_date = db.Column(db.Date, nullable=False)
    # 当月活跃用户
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    # 当月交易用户
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    # 当月注册用户
    new_user_count = db.Column(db.Integer, nullable=False, default=0)
    # 当月资产用户
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    # 当月总用户数
    total_user_count = db.Column(db.Integer, nullable=False, default=0)
    # 当月新增交易用户数
    increase_trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    # 当月大使用户数
    ambassador_user_count = db.Column(db.Integer, nullable=False, default=0)
    # 当月大使邀请的注册用户数
    ambassador_refer_new_user_count = db.Column(db.Integer, nullable=False, default=0)
    # Row(UserPreferenceModel.key == "language").value
    language = db.Column(db.String(64), nullable=False, default="", index=True)
    self_reg_new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 自然注册用户
    normal_refer_new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 普通邀请的注册用户数
    spot_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 现货用户数
    perpetual_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 合约用户数
    exchange_trade_user_count = db.Column(db.Integer, nullable=False, default=0)  # 兑换用户数
    margin_user_count = db.Column(db.Integer, nullable=False, default=0)  # 杠杆用户数
    amm_user_count = db.Column(db.Integer, nullable=False, default=0)  # AMM用户数
    cet_new_user_count = db.Column(db.Integer, nullable=False, default=0)  # CET新增用户数
    balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='lang 总资产')


class MonthlyAmbassadorReferralDetailReport(ModelBase):
    report_date = db.Column(db.Date, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    # 邀请人数
    refer_count = db.Column(db.Integer, nullable=False, default=0)
    # 被邀请人交易人数
    deal_count = db.Column(db.Integer, nullable=False, default=0)
    # 被邀请人充值人数
    deposit_count = db.Column(db.Integer, nullable=False, default=0)
    # 被邀请人币币交易量USD
    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 被邀请人币币手续费USD
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 被邀请人合约交易量USD
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 被邀请人合约手续费USD
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 大使返佣数量
    refer_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    user = db.relationship(
        'User', backref=db.backref('monthly_ambassador_referral_detail_report',
                                   lazy='dynamic'))


class MonthlyAmbassadorReferreeReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint(
            "report_date", "user_id", "referree_id", name="report_date_user_id_referree_id_unique"
        ),
    )
    report_date = db.Column(db.Date, nullable=False)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    referree_id = db.Column(db.Integer, nullable=False, index=True)
    # 被邀请人币币交易量USD
    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                               default=0)
    # 被邀请人币币手续费USD
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 被邀请人合约交易量USD
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                    default=0)
    # 被邀请人合约手续费USD
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                  default=0)
    # 大使返佣数量
    refer_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False,
                                   default=0)
    
    
class MonthlyUserReferralDetailReport(ModelBase):
    """ 普通用户返佣记录-月报 """
    report_date = db.Column(db.Date, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))
    # 邀请人数
    refer_count = db.Column(db.Integer, nullable=False, default=0)
    # 被邀请人交易人数
    deal_count = db.Column(db.Integer, nullable=False, default=0)
    # 被邀请人充值人数
    deposit_count = db.Column(db.Integer, nullable=False, default=0)
    # 被邀请人币币交易量USD
    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 被邀请人币币手续费USD
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 被邀请人合约交易量USD
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 被邀请人合约手续费USD
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 返佣CET数量
    refer_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    refer_total_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class MonthlyAmbassadorAgentReport(ModelBase):
    """ 大使代理-推荐月表 """

    __table_args__ = (db.UniqueConstraint("report_date", "user_id", name="report_date_user_id_unique"),)

    report_date = db.Column(db.Date, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"))  # 代理
    referral_ambassador_count = db.Column(db.Integer, nullable=False)  # 代理推荐的大使数
    delta_referral_ambassador_count = db.Column(db.Integer, nullable=False)  # 代理当月新增推荐的大使数
    referral_deal_ambassador_count = db.Column(db.Integer, nullable=False)  # 代理推荐的交易大使数（大使邀请的用户交易了）
    delta_referral_deal_ambassador_count = db.Column(db.Integer, nullable=False)  # 代理当月新增推荐的交易大使数（大使邀请的用户交易了）
    referral_user_count = db.Column(db.Integer, nullable=False)  # 代理推荐的大使，推荐的用户数
    delta_referral_user_count = db.Column(db.Integer, nullable=False)  # 代理当月新增推荐的大使，当月新增推荐的用户数
    deal_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 代理推荐的大使，推荐的用户，当月交易量（USD）
    delta_deal_amount = db.Column(db.MYSQL_DECIMAL_26_8,
                                  nullable=False)  # 代理当月新增推荐的大使，当月新增推荐的用户，当月累计交易量（USD）
    deal_user_count = db.Column(db.Integer, nullable=False)  # 代理推荐的大使，推荐的交易用户数
    delta_deal_user_count = db.Column(db.Integer, nullable=False)  # 代理当月新增推荐的大使，当月新增推荐的交易用户数
    asset = db.Column(db.String(32), nullable=False)  # 返佣币种
    # 返佣金额（不区分当月新增的），如果该用户即是代理又是商务，那他的返佣金额 = 他推荐大使的返佣 + 他下面大使代理的分成
    referral_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyReferTypeReport(ModelBase):

    class Type(Enum):
        REFERRAL = 'referral'
        AMBASSADOR = 'ambassador'
        AMBASSADOR_AGENT = 'ambassador_agent'

    report_date = db.Column(db.Date, nullable=False, index=True)
    type = db.Column(db.StringEnum(Type), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    trade_count = db.Column(db.Integer, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 交易总金额
    trade_spot_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)  # 现货交易额
    trade_perpetual_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 合约交易额
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    refer_count = db.Column(db.Integer, nullable=False, default=0)
    refer_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    refer_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 总返佣
    refer_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    new_trade_count = db.Column(db.Integer, nullable=False, default=0)  # 新增refer交易人数
    # 以下2个字段仅有普通邀请类型时使用
    referrer_count = db.Column(db.Integer, nullable=False, default=0, comment="邀请人(去重)")
    new_referrer_count = db.Column(db.Integer, nullable=False, default=0, comment="新增邀请人(去重)")
    invitee_count = db.Column(db.Integer, nullable=False, default=0, comment="普通refer注册用户数")

    total_invitee = db.Column(db.Integer, nullable=False, default=0)  # refer总人数
    invitee_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # refer注册人数占比
    trade_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # refer交易人数占比
    new_trade_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 新增refer交易人数占比
    type_user_count = db.Column(db.Integer, nullable=False, default=0)  # 有效大使的总数量 or 有效代理的总数量


class MonthlyNormalReferReport(ModelBase):
    """ 普通邀请月报, 以下数据只计算了普通邀请，去除大使邀请的数据 """
    report_date = db.Column(db.Date, nullable=False, unique=True, index=True, comment="统计日期")

    referrer_count = db.Column(db.Integer, nullable=False, default=0, comment="邀请人(去重)")
    new_referrer_count = db.Column(db.Integer, nullable=False, default=0, comment="新增邀请人(去重)")

    invitee_count = db.Column(db.Integer, nullable=False, default=0, comment="普通refer注册用户数")
    increase_user_count = db.Column(db.Integer, nullable=False, default=0, comment="总注册用户数")

    trade_invitee_count = db.Column(db.Integer, nullable=False, default=0, comment="普通refer交易用户数")
    trade_user_count = db.Column(db.Integer, nullable=False, default=0, comment="总交易用户数")

    new_trade_invitee_count = db.Column(db.Integer, nullable=False, default=0, comment="新增被邀请交易用户")
    increase_trade_user_count = db.Column(db.Integer, nullable=False, default=0, comment="总新增交易用户数")

    invitee_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment="refer交易额(USD)")
    site_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment="全站交易额(USD)")

    invitee_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment="被邀请交易手续费(USD)")
    site_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment="全站交易手续费(USD)")


class MonthlyAmbassadorAgentAssetReport(ModelBase):
    """大使维度-代理返佣汇总月表"""
    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)  # 大使
    agent_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False, index=True)  # 大使代理
    deal_user_count = db.Column(db.Integer, nullable=False)  # 代理推荐的大使，推荐的交易用户数
    # 交易手续费金额，用于计算返佣金额
    fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 返佣金额
    referral_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyMakerTradeReport(ModelBase):
    __table_args__ = (db.UniqueConstraint('report_date', 'system', 'maker_type',
                                          name='report_date_system_maker_type'),)

    class System(Enum):
        SPOT = 'spot'
        PERPETUAL = 'perpetual'

    class MakerType(Enum):
        ALL = '全部'
        INNER = '内部做市商'
        OUTER = '外部做市商'

    report_date = db.Column(db.Date, nullable=False)
    system = db.Column(db.StringEnum(System), nullable=False)
    maker_type = db.Column(db.StringEnum(MakerType), nullable=False, default=MakerType.OUTER)
    deal_count = db.Column(db.Integer, nullable=False)
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    deal_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    avg_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyMakerMarketTradeReport(ModelBase):
    __table_args__ = (db.UniqueConstraint('report_date', 'system', 'market',
                                          name='report_date_system_market'),)

    class System(Enum):
        SPOT = 'spot'
        PERPETUAL = 'perpetual'

    report_date = db.Column(db.Date, nullable=False)
    market = db.Column(db.String(32), nullable=False)
    system = db.Column(db.StringEnum(System), nullable=False)
    deal_count = db.Column(db.Integer, nullable=False)
    deal_user_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False,
                               default='')
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    deal_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    total_deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    total_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    avg_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlySiteIncomeBusinessReport(ModelBase):
    """业务收入报表"""
    report_date = db.Column(db.Date, nullable=False)
    # 收入
    spot_trade_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    exchange_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 兑换手续费
    perpetual_trade_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    withdraw_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    margin_interest = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    credit_interest = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    pledge_interest = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    p2p_order_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    staking_interest = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    sign_off_user_balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 注销账户资产
    pre_trading_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 预测市场的赎回、交割手续费
    cleaned_balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 小额账户回收余额
    wallet_asset_reward = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 利息币/质押收入
    fixed_exchange_profit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 一口价兑换-收入（收付款手续费 + 收付款兑换盈利）
    # 支出
    withdraw_pay_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    refer_pay = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    investment_pay = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    staking_pay = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    amm_trade_pay_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    maker_cashback_pay = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    fixed_exchange_loss = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 一口价兑换亏损（收付款兑换亏损）


class MonthlySpotDepthReport(ModelBase):

    report_date = db.Column(db.DATE, nullable=False, index=True)
    market = db.Column(db.String(32), nullable=False, index=True)
    depth1_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth2_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth3_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth4_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth5_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    bid_ask_delta = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class MonthlyPerpetualDepthReport(ModelBase):

    report_date = db.Column(db.DATE, nullable=False, index=True)
    market = db.Column(db.String(32), nullable=False, index=True)
    depth1_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth2_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth3_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth4_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth5_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    bid_ask_delta = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

class MonthlyThirdPerpetualDepthReport(ModelBase):
    __table_args__ = (
        db.Index('exchange_market', 'exchange', 'market'),
        db.Index('exchange_report_date', 'exchange', 'report_date'),
    )

    exchange = db.Column(db.String(16), nullable=False)
    report_date = db.Column(db.DATE, nullable=False)
    market = db.Column(db.String(32), nullable=False)
    depth1_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth2_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth3_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth4_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    depth5_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    bid_ask_delta = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class MonthlyMarginFundReport(ModelBase):
    __table_args__ = (
            db.UniqueConstraint('asset', 'report_date',
                                name='asset_report_date_unique'),
            )

    report_date = db.Column(db.Date, index=True, nullable=False)

    asset = db.Column(db.String(32), index=True, nullable=False)

    # 平台利息注入
    interest_fund = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 爆仓费注入
    liquidation_fund = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 穿仓分摊
    liquidation = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 变动
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 现在总额
    balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    # 划转数量
    transfer = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    real_balance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyPerpetualFundingFeeReport(ModelBase):
    report_date = db.Column(db.Date, nullable=False, index=True)
    market = db.Column(db.String(32), nullable=False, index=True)
    long_count = db.Column(db.Integer, nullable=False, default=0)  # 多头人数
    short_count = db.Column(db.Integer, nullable=False, default=0)  # 空头人数
    total_count = db.Column(db.Integer, nullable=False, default=0)
    total_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    total_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    net_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    net_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class MonthlyPerpetualPositionReport(ModelBase):
    """
    合约仓位分析月报
    TODO: 已废弃，待删除
    """
    class TimeRange(Enum):
        LESS_THAN_ONE_MINUTE = 60
        LESS_THAN_FIVE_MINUTES = 5 * 60
        LESS_THAN_FIFTEEN_MINUTES = 15 * 60
        LESS_THAN_ONE_HOUR = 60 * 60
        LESS_THAN_FOUR_HOURS = 4 * 60 * 60
        LESS_THAN_TWENTY_FOUR_HOURS = 24 * 60 * 60
        LESS_THAN_SEVENTY_TWO_HOURS = 72 * 60 * 60
        MORE_THAN_SEVENTY_TWO_HOURS = Decimal('inf')

    report_date = db.Column(db.Date, nullable=False, index=True)

    # 如果time_range是None, 该记录为合计的数据
    time_range = db.Column(db.Enum(TimeRange), nullable=False)
    total_trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    position_count = db.Column(db.Integer, nullable=False, default=0)
    position_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    # in seconds
    average_position_time = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    average_leverage = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    # 去除api, 做市商的交易用户
    excluded_trade_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)

    # 时间段内平仓的用户
    close_position_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)


class MonthlyMakerTradeDetailReport(ModelBase):

    __table_args__ = (db.UniqueConstraint('report_date', 'system', 'user_id',
                                          name='report_date_system_user_id'),)

    class System(Enum):
        SPOT = 'spot'
        PERPETUAL = 'perpetual'

    class MakerType(Enum):
        INNER = '内部做市商'
        OUTER = '外部做市商'

    report_date = db.Column(db.DATE, nullable=False)
    system = db.Column(db.StringEnum(System), nullable=False, default=System.SPOT)
    maker_type = db.Column(db.StringEnum(MakerType), nullable=False, default=MakerType.OUTER)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), index=True)
    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    trade_amount_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    taker_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    taker_amount_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    maker_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    maker_amount_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_amount_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    maker_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='maker手续费')
    taker_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='taker手续费')
    cashback_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='maker返佣金额')


    user = db.relationship(
        'User',
        backref=db.backref('monthly_maker_trade_detail_report', lazy='dynamic'),
        foreign_keys=user_id)


class MonthlyMakerTradeMarketDetailReport(ModelBase):

    __table_args__ = (db.UniqueConstraint('system', 'market', 'report_date', 'user_id',
                                          name='system_market_report_date_user_id'),)

    class System(Enum):
        SPOT = 'spot'
        PERPETUAL = 'perpetual'

    report_date = db.Column(db.DATE, nullable=False)
    system = db.Column(db.StringEnum(System), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    market = db.Column(db.String(32), nullable=False)
    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    total_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    taker_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    total_taker_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    maker_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    total_maker_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    total_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    is_amm = db.Column(db.Boolean, nullable=False, default=False, comment="是否 AMM 标志(仅币币市场")

    user = db.relationship(
        'User',
        backref=db.backref('monthly_maker_trade_market_detail_report', lazy='dynamic'),
        foreign_keys=user_id)


class MonthlyAssetExchangeAssetPairReport(ModelBase):
    """ 币种兑换-币对月报 """

    __table_args__ = (
        db.UniqueConstraint("source_asset", "target_asset", "report_date", name="source_target_report_date_unique"),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    source_asset = db.Column(db.String(32), nullable=False)
    target_asset = db.Column(db.String(32), nullable=False)
    deal_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 成交数
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 成交额
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 手续费usd
    user_count = db.Column(db.Integer, nullable=False, default=0)  # 用户数
    order_count = db.Column(db.Integer, nullable=False, default=0)  # 订单数
    avg_processing_time = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 平均成交时间
    user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 当月下单用户的bitmap


class MonthlyAssetExchangeSiteReport(ModelBase):
    """ 币种兑换-全站月报 """

    report_date = db.Column(db.Date, nullable=False, unique=True)
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 成交额
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 手续费usd
    user_count = db.Column(db.Integer, nullable=False, default=0)  # 下单用户数
    exchange_asset_count = db.Column(db.Integer, nullable=False, default=0)  # 兑换币种数量
    new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 新增下单用户数
    order_count = db.Column(db.Integer, nullable=False, default=0)  # 订单数
    avg_processing_time = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 平均成交时间
    user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 当月下单用户的bitmap
    history_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 历史所有用户的bitmap


class MonthlyAssetExchangeAssetReport(ModelBase):
    """ 兑换报表-币种月报 """

    __table_args__ = (
        db.UniqueConstraint("asset", "report_date", name="asset_report_date_unique"),
    )

    report_date = db.Column(db.Date, nullable=False)
    asset = db.Column(db.String(32), nullable=False, default='')  # '' 表示 ALL
    user_count = db.Column(db.Integer, nullable=False, default=0, comment='参与兑换人数')
    order_count = db.Column(db.Integer, nullable=False, default=0, comment='兑换笔数')
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='兑换市值 (USD)')
    source_user_count = db.Column(db.Integer, nullable=False, default=0, comment='source 人数')
    source_order_count = db.Column(db.Integer, nullable=False, default=0, comment='source 笔数')
    source_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='source 数量(ALL 折算成 USD)')
    target_user_count = db.Column(db.Integer, nullable=False, default=0, comment='target 人数')
    target_order_count = db.Column(db.Integer, nullable=False, default=0, comment='target 笔数')
    target_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='target 数量(ALL 折算成 USD)')


class MonthlySpotGridSiteReport(ModelBase):
    """ 现货网格月报 """

    report_date = db.Column(db.Date, nullable=False, unique=True)
    total_deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 成交额
    total_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 手续费usd
    grid_profit_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 网格利润usd
    total_sty_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 策略持仓总市值usd
    running_sty_count = db.Column(db.Integer, nullable=False, default=0)  # 运行策略数
    running_user_count = db.Column(db.Integer, nullable=False, default=0)  # 运行策略人数
    inc_sty_count = db.Column(db.Integer, nullable=False, default=0)  # 新增策略数
    inc_user_count = db.Column(db.Integer, nullable=False, default=0)  # 新增策略用户数
    new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 首次策略用户新增数
    deal_order_count = db.Column(db.Integer, nullable=False, default=0)  # 成交的订单笔数
    user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 当天下单用户的bitmap
    history_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 历史所有用户的bitmap


class MonthlyCopyTradingSiteReport(ModelBase):
    """ 跟单交易-全站月报 """

    report_date = db.Column(db.Date, nullable=False, unique=True)
    trader_user_count = db.Column(db.Integer, nullable=False, default=0)  # 带单总人数
    new_trader_user_count = db.Column(db.Integer, nullable=False, default=0)  # 新增交易员
    follower_user_count = db.Column(db.Integer, nullable=False, default=0)  # 跟单总人数
    new_follower_user_count = db.Column(db.Integer, nullable=False, default=0)  # 新增跟单员
    cur_follower_user_count = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 当前跟单人数
    cur_position_user_count = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 当前持仓人数
    deal_user_count = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 成交人数
    deal_count = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 成交笔数
    aum_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 带单总规模USD
    trader_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 带单交易额
    follower_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 跟单交易额
    profit_share_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 分润金额
    history_trader_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 历史带单人的bitmap，用于计算增量
    history_follower_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 历史跟单用户的bitmap


class MonthlyPledgeSiteReport(ModelBase):
    """ 借贷-全站月报 """

    __table_args__ = (
        db.UniqueConstraint('asset', 'report_date', name='asset_report_date_unique'),
        db.Index('report_date_asset_idx', 'report_date', 'asset'),
    )

    report_date = db.Column(db.Date, nullable=False)
    asset = db.Column(db.String(32), nullable=False)  # 空字符串表示全部币种
    total_loan_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 借币市值
    total_repay_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 还币市值
    total_interest_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 利息收入市值
    total_fund_income_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 基金收入市值
    avg_loan_balance_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 平均借贷余额市值
    avg_pledge_value_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 平均抵押市值
    avg_pledge_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)   # 平均质押率(平均抵押市值/平均借贷余额市值)
    unflat_user_count = db.Column(db.Integer, nullable=False, default=0)  # 有待还币种的用户数
    loan_user_count = db.Column(db.Integer, nullable=False, default=0)  # 新增借贷的用户数
    new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 首次借贷用户新增数
    repay_user_count = db.Column(db.Integer, nullable=False, default=0)  # 有成功还币的用户数
    unflat_loan_order_count = db.Column(db.Integer, nullable=False, default=0)
    new_loan_order_count = db.Column(db.Integer, nullable=False, default=0)
    repay_loan_order_count = db.Column(db.Integer, nullable=False, default=0)
    loan_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 当天借贷用户的bitmap
    history_loan_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 历史所有借贷用户的bitmap


class MonthlyPledgeSiteLiqReport(ModelBase):
    """ 借贷-全站强平月报 """

    __table_args__ = (
        db.UniqueConstraint('asset', 'report_date', name='asset_report_date_unique'),
        db.Index('report_date_asset_idx', 'report_date', 'asset'),
    )

    report_date = db.Column(db.Date, nullable=False)
    asset = db.Column(db.String(32), nullable=False)  # 空字符串表示全部币种
    liq_user_count = db.Column(db.Integer, nullable=False, default=0)  # 爆仓用户数
    liq_position_count = db.Column(db.Integer, nullable=False, default=0)  # 爆仓笔数
    arrears_user_count = db.Column(db.Integer, nullable=False, default=0)  # 穿仓用户数
    arrears_position_count = db.Column(db.Integer, nullable=False, default=0)  # 穿仓笔数
    total_liq_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 爆仓数目
    total_liq_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 爆仓市值
    total_liq_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 爆仓手续费市值
    total_cross_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 穿仓市值
    total_fund_repay_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 保险基金穿仓损失


class MonthlyPledgeAssetReport(ModelBase):
    """ 借贷-质押币月报 """

    __table_args__ = (
        db.UniqueConstraint('loan_asset', 'pledge_asset', 'report_date', name='loan_asset_pledge_asset_date_unique'),
        db.Index('loan_report_pledge_idx', 'loan_asset', 'report_date', 'pledge_asset'),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    loan_asset = db.Column(db.String(32), nullable=False)  # 该借币币种维度的统计，空字符串是全部借币币种的质押币统计
    pledge_asset = db.Column(db.String(32), nullable=False, index=True)  # 质押币种
    pledge_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 质押数
    pledge_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 质押市值
    new_pledge_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 新增质押数
    pledge_user_count = db.Column(db.Integer, nullable=False, default=0)  # 质押用户数


class MonthlyPledgeLiqAssetReport(ModelBase):
    """ 借贷-强平质押币月报 """

    __table_args__ = (
        db.UniqueConstraint('loan_asset', 'pledge_asset', 'report_date', name='loan_asset_pledge_asset_date_unique'),
        db.Index('loan_report_pledge_idx', 'loan_asset', 'report_date', 'pledge_asset'),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    loan_asset = db.Column(db.String(32), nullable=False)  # 该借币币种维度的统计，空字符串是全部借币币种的质押币统计
    pledge_asset = db.Column(db.String(32), nullable=False, index=True)  # 被强平的质押币种
    liq_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 强平卖出数
    liq_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 强平卖出市值
    liq_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 该质押币种收的强平手续费数目
    liq_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 该质押币种收的强平手续费市值
    liq_user_count = db.Column(db.Integer, nullable=False, default=0)  # 质押用户数


class MonthlyPaymentSiteReport(ModelBase):
    """收付款-全站月报"""

    report_date = db.Column(db.Date, nullable=False, unique=True)
    pay_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 付款市值
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 手续费市值
    exchange_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 总兑换市值
    exchange_profit_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 兑换盈利市值
    exchange_loss_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 兑换亏损市值
    avg_exchange_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)   # 平均兑换市值(总兑换市值/需要兑换的付款笔数)
    pay_count = db.Column(db.Integer, nullable=False, default=0)  # 周期内付款笔数
    exchange_count = db.Column(db.Integer, nullable=False, default=0)  # 需要兑换的付款笔数
    user_count = db.Column(db.Integer, nullable=False, default=0)  # 周期内用户数（收款人数+付款人数去重）
    new_user_count = db.Column(db.Integer, nullable=False, default=0)  # 周期内新增的收付款用户（之前未使用过收付款）
    user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 周期内使用过收付款的用户
    history_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 历史使用过收付款的用户，用于计算增量


class MonthlyPaymentAssetReport(ModelBase):
    """收付款-付款币种月报"""

    __table_args__ = (
        db.UniqueConstraint('pay_asset', 'report_date', name='pay_asset_report_date_unique'),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    pay_asset = db.Column(db.String(32), nullable=False)  # 该借币币种维度的统计，空字符串是全部借币币种的质押币统计
    pay_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 付款市值
    exchange_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 总兑换市值
    pay_user_count = db.Column(db.Integer, nullable=False, default=0)  # 周期内付款用户数（付款人数去重）
    pay_count = db.Column(db.Integer, nullable=False, default=0)  # 周期内付款笔数
    exchange_count = db.Column(db.Integer, nullable=False, default=0)  # 需要兑换的付款笔数
    avg_exchange_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)   # 平均兑换市值(总兑换市值/需要兑换的付款笔数)


class MonthlyAutoInvestSiteReport(ModelBase):
    """ 定投策略-全站月报 """

    report_date = db.Column(db.Date, nullable=False, unique=True)
    success_order_count = db.Column(db.Integer, nullable=False, default=0)
    fail_order_count = db.Column(db.Integer, nullable=False, default=0)
    manual_order_count = db.Column(db.Integer, nullable=False, default=0)
    total_plan_count = db.Column(db.Integer, nullable=False, default=0)
    user_plan_count = db.Column(db.Integer, nullable=False, default=0)
    user_order_count = db.Column(db.Integer, nullable=False, default=0)
    running_plan_count = db.Column(db.Integer, nullable=False, default=0)
    running_user_plan_count = db.Column(db.Integer, nullable=False, default=0)
    new_user_plan_count = db.Column(db.Integer, nullable=False, default=0)
    total_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    total_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class MonthlyCountryReachReport(ModelBase):
    """ 短信、邮件通知次数-月报（国家为维度） """

    __table_args__ = (
        db.UniqueConstraint("country", "report_date", name="country_report_date_unique"),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    country = db.Column(db.String(32), nullable=False)  # 空字符串表示全部国家
    email_send_count = db.Column(db.Integer, default=0)
    sms_send_count = db.Column(db.Integer, default=0)

    email_user_count = db.Column(db.Integer, default=0)
    sms_user_count = db.Column(db.Integer, default=0)


class MonthlyChannelReachReport(ModelBase):
    """ 短信渠道通知次数-日报（渠道为维度） """

    __table_args__ = (
        db.UniqueConstraint("channel", "report_date", name="channel_report_date_unique"),
    )

    report_date = db.Column(db.Date, nullable=False, index=True)
    channel = db.Column(db.String(32), nullable=False)  # 空字符串表示全部渠道
    sms_send_count = db.Column(db.Integer, default=0)
    sms_user_count = db.Column(db.Integer, default=0)


class MonthlyUserActiveRetainedReport(ModelBase):
    """用户活跃留存月报表"""

    __table_args__ = (
        db.UniqueConstraint('origin_type', 'report_date', name="origin_type_report_date_unique"),
    )

    class OriginType(Enum):
        ALL = "全部"
        NORMAL = "自然注册"
        REFERRAL = "普通推荐"
        AMBASSADOR = "大使推荐"

    report_date = db.Column(db.Date, nullable=False,  index=True)
    origin_type = db.Column(db.StringEnum(OriginType), nullable=False)
    # 注册人数
    region_count = db.Column(db.Integer, default=0)
    origin_user_list = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')


class MonthlyUserActiveRetainedDetail(ModelBase):
    """用户活跃度留存月报表详情"""
    monthly_report_id = db.Column(db.Integer, db.ForeignKey('monthly_user_active_retained_report.id'))
    report_date = db.Column(db.Date, nullable=False)
    retained_count = db.Column(db.Integer, default=0)


class MonthlyTradeUserRetainedReport(ModelBase):
    """用户交易留存月报表"""

    __table_args__ = (
        db.UniqueConstraint('trade_type', 'report_date', name="trade_type_report_date_unique"),
    )

    class TradeType(Enum):
        ALL = "全部"
        SPOT = "现货"
        PERPETUAL = "合约"

    report_date = db.Column(db.Date, nullable=False,  index=True)
    trade_type = db.Column(db.StringEnum(TradeType), nullable=False)
    trade_count = db.Column(db.Integer, default=0)
    origin_user_list = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')


class MonthlyTradeUserRetainedDetail(ModelBase):
    """用户活跃度留存月报表详情"""
    monthly_report_id = db.Column(db.Integer, db.ForeignKey('monthly_trade_user_retained_report.id'))
    report_date = db.Column(db.Date, nullable=False)
    retained_count = db.Column(db.Integer, default=0)


class LanguageAreaUserActiveRetainReport(ModelBase):
    """各语区活跃用户留存报表"""
    __table_args__ = (db.UniqueConstraint('report_date', 'language_area',
                            name='report_date_language_area'),)

    report_date = db.Column(db.Date, nullable=False)
    language_area = db.Column(db.String(32), nullable=False)
    user_count = db.Column(db.Integer, nullable=False, default=0)   # 该语区总用户数
    last_month_active_user_count = db.Column(
        db.Integer, nullable=False, default=0)    # 0-1个月活跃用户数
    in_three_month_active_user_count = db.Column(
        db.Integer, nullable=False, default=0)    # 1-3个月活跃用户数
    in_six_month_active_user_count = db.Column(
        db.Integer, nullable=False, default=0)    # 3-6个月活跃用户数
    over_six_month_active_user_count = db.Column(
        db.Integer, nullable=False, default=0)    # 6个月及以上活跃用户数


class CountryUserActiveRetainReport(ModelBase):
    """各国家活跃用户留存报表"""

    __table_args__ = (db.UniqueConstraint('report_date', 'country',
                                          name='report_date_country'),)

    report_date = db.Column(db.Date, nullable=False)
    country = db.Column(db.String(64), nullable=False)
    user_count = db.Column(db.Integer, nullable=False, default=0)  # 该国家总用户数
    last_month_active_user_count = db.Column(db.Integer, nullable=False,
                                             default=0)  # 0-1个月活跃用户数
    in_three_month_active_user_count = db.Column(db.Integer, nullable=False,
                                                 default=0)  # 1-3个月活跃用户数
    in_six_month_active_user_count = db.Column(db.Integer, nullable=False,
                                               default=0)  # 3-6个月活跃用户数
    over_six_month_active_user_count = db.Column(db.Integer, nullable=False,
                                                 default=0)  # 6个月及以上活跃用户数


class MonthlyLossUserReport(ModelBase):
    """用户流失月报"""

    class Type(Enum):
        TRADE = '交易'
        SYSTEM = '平台'

    report_date = db.Column(db.Date, nullable=False)

    type = db.Column(db.StringEnum(Type), nullable=False)
    user_count = db.Column(db.Integer, nullable=False, default=0)
     
    to_be_loss_users = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    to_be_loss_user_count = db.Column(db.Integer, nullable=False, default=0)
    new_to_be_loss_user_count = db.Column(db.Integer, nullable=False, default=0)
    to_be_loss_return_count = db.Column(db.Integer, nullable=False, default=0)
    to_be_loss_balance_user_count = db.Column(db.Integer, nullable=False, default=0)
    
    active_users = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    loss_users = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    loss_user_count = db.Column(db.Integer, nullable=False, default=0)
    new_loss_user_count = db.Column(db.Integer, nullable=False, default=0)
    loss_return_count = db.Column(db.Integer, nullable=False, default=0)
    loss_balance_user_count = db.Column(db.Integer, nullable=False, default=0)
    new_user_count = db.Column(db.Integer, nullable=False, default=0)
    net_new_user_count = db.Column(db.Integer, nullable=False, default=0)


class MonthlyAssetBusinessReport(ModelBase):
    """
    资产业务转换月报
    """
    __table_args__ = (
        db.UniqueConstraint('report_date', 'asset', name="report_date_asset_uniq"),
    )

    report_date = db.Column(db.Date, nullable=False)
    # empty string means all assets
    asset = db.Column(db.String(32), nullable=False)
    user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    total_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    margin_user_count = db.Column(db.Integer, nullable=False, default=0)
    investment_user_count = db.Column(db.Integer, nullable=False, default=0)
    staking_user_count = db.Column(db.Integer, nullable=False, default=0)
    amm_user_count = db.Column(db.Integer, nullable=False, default=0)
    increase_asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    up_threshold_increase_asset_user_count = db.Column(db.Integer, nullable=False,
                                                       default=0)
    increase_trade_user_count = db.Column(db.Integer, nullable=False, default=0)


class MonthlyAssetTradeUserReport(ModelBase):
    """币种交易用户报表"""
    __table_args__ = (
        db.UniqueConstraint('report_date', 'asset', name="report_date_asset_uniq"),
    )
    report_date = db.Column(db.Date, nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    increase_trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    history_trade_users = db.Column(db.MYSQL_MEDIUM_BLOB, default=b'', nullable=True)


class MonthlyBalanceReport(ModelBase):
    """
    资产月报
    """
    report_date = db.Column(db.Date, nullable=False, index=True)
    count_0 = db.Column(db.Integer, nullable=False, default=0)
    count_1 = db.Column(db.Integer, nullable=False, default=0)
    count_2 = db.Column(db.Integer, nullable=False, default=0)
    count_3 = db.Column(db.Integer, nullable=False, default=0)
    count_4 = db.Column(db.Integer, nullable=False, default=0)
    count_5 = db.Column(db.Integer, nullable=False, default=0)
    count_6 = db.Column(db.Integer, nullable=False, default=0)
    count_7 = db.Column(db.Integer, nullable=False, default=0)
    threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    asset = db.Column(db.String(32), nullable=False, index=True)
    total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    total_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # the json of the user id list
    user_id_list = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)  # 当日具体币种资产的用户列表存这里
    user_count = db.Column(db.Integer, nullable=False, default=0)
    user_id_bit_array = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')  # 当日全部资产的用户列表存这里
    # account_type: if set to None, then it's the summary record of all accounts
    account_type = db.Column(db.StringEnum(AccountBalanceType), nullable=True)
    history_asset_users = db.Column(db.MYSQL_MEDIUM_BLOB, default=b'', nullable=True)   # 历史有资产的用户列表（含当日）
    increase_asset_user_count = db.Column(db.Integer, nullable=True, default=0)    # 新增资产用户
    up_threshold_increase_asset_user_count = db.Column(db.Integer, nullable=True, default=0)   # 超过币种阈值的新增资产用户

    @classmethod
    def get_user_ids(cls, obj):
        # 需要 asset、user_id_list、user_id_bit_array 列
        if obj.asset:
            return json.loads(obj.user_id_list)
        else:
            return list(BitMap.deserialize(obj.user_id_bit_array))


class MonthlyAssetVisitReport(ModelBase):
    """
    资产用户关注月报
    """

    __table_args__ = (
        db.UniqueConstraint('report_date', 'asset', name="report_date_asset_uniq"),
    )

    report_date = db.Column(db.Date, nullable=False)
    # empty string means all assets
    asset = db.Column(db.String(32), nullable=False)
    favorite_count = db.Column(db.Integer, nullable=False, default=0)
    spot_follow_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_follow_count = db.Column(db.Integer, nullable=False, default=0)
    info_view_count = db.Column(db.Integer, nullable=False, default=0)
    spot_view_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_view_count = db.Column(db.Integer, nullable=False, default=0)


class MonthlyBrokerReport(ModelBase):
    """ 经纪商返佣-月报 """
    report_date = db.Column(db.Date, nullable=False, unique=True)
    broker_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    refer_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MonthlyPerpetualTransferReport(ModelBase):
    """合约划转月报"""
    report_date = db.Column(db.Date, nullable=False, unique=True)
    transfer_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    transfer_in_user_count = db.Column(db.Integer, nullable=False, default=0)
    transfer_in_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    transfer_out_user_count = db.Column(db.Integer, nullable=False, default=0)
    transfer_out_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 净转入市值：周期内转入市值-周期内转出市值
    transfer_net_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    transfer_user_bit_map = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)
    transfer_in_user_bit_map = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)
    transfer_out_user_bit_map = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)
    trade_user_bit_map = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)


class MonthlyPublicityShortLinkReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'short_link_id', 'report_date',
                            name="period_short_link_id_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    short_link_id = db.Column(db.Integer, nullable=False, index=True)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)  # 链上充值用户
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)

    country = db.Column(db.String(32), nullable=False, comment='国家', index=True)
    language_area = db.Column(db.String(32), nullable=False, comment='语区', index=True)
    category_id = db.Column(db.Integer, nullable=False, comment='渠道平台', index=True)
    business_segment = db.Column(db.String(128), nullable=False, comment='业务板块', index=True)
    publicity_channel_id = db.Column(db.Integer, nullable=False, comment='细分渠道', index=True)


class MonthlyPublicityChannelReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'publicity_channel_id', 'report_date',
                            name="period_publicity_channel_id_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    publicity_channel_id = db.Column(db.Integer, nullable=False)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)   # 链上充值用户
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)

class MonthlyAppPublicityChannelReport(ModelBase):

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False, index=True)
    period = db.Column(db.StringEnum(Period), nullable=False, index=True)
    channel = db.Column(db.String(256), nullable=True, index=True, comment="af渠道 + 其他")
    country_code = db.Column(db.String(3), index=True, comment="国家")
    language = db.Column(db.String(32), nullable=False, index=True, comment="语言")
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)   # 链上充值用户
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    platform = db.Column(db.StringEnum(ReportPlatform), nullable=False, comment="报告平台", index=True)


class MonthlyPublicityCountryReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'country', 'report_date',
                            name="period_country_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    country = db.Column(db.String(32), nullable=False)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)


class MonthlyPublicityLanguageAreaReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'language_area', 'report_date',
                            name="period_language_area_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    language_area = db.Column(db.String(32), nullable=False)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)


class MonthlyPublicityBusinessSegmentReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'business_segment', 'report_date',
                            name="period_business_segment_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    business_segment = db.Column(db.String(128), nullable=False)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)


class MonthlyPublicityCategoryReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'publicity_category_id', 'report_date',
                            name="period_publicity_category_id_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    publicity_category_id = db.Column(db.Integer, nullable=False)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)


class MonthlyStakingReport(ModelBase):
    """链上质押月报"""

    report_date = db.Column(db.Date, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False)
    staking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    system_staking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 总奖励
    reward_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 发给用户的奖励
    user_reward_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    system_reward_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    user_count = db.Column(db.Integer, nullable=False, default=0)
    reward_user_count = db.Column(db.Integer, nullable=False, default=0)
    new_reward_user_count = db.Column(db.Integer, nullable=False, default=0)
    new_user_count = db.Column(db.Integer, nullable=False, default=0)

    # 周期内用户提交的质押数量
    new_staking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 周期内用户提交的赎回数量
    new_unstaking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    # 净质押数量
    net_staking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    unstaking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    income_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0) # 一日年化收益率
    income_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    history_income_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    history_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')


class MonthlyStakingSiteReport(ModelBase):
    """ 全站质押理财月报 """
    report_date = db.Column(db.Date, nullable=False, index=True)
    user_count = db.Column(db.Integer, nullable=False, default=0)
    new_user_count = db.Column(db.Integer, nullable=False, default=0)
    new_reward_user_count = db.Column(db.Integer, nullable=False, default=0)

    staking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    system_staking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    # 周期内用户提交的质押数量
    new_staking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    # 周期内用户提交的赎回数量
    new_unstaking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    reward_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    user_reward_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    system_reward_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    # AAVE质押收益：包括USDT、USDC质押到AAVE产生的收入以及利息币的收入
    wallet_reward_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    
    # 净质押数量
    net_staking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    income_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0) # 一日年化收益率
    income_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    history_income_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    history_user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')


class MonthlyPerpetualProfitLossReport(ModelBase):
    """ 合约用户盈亏统计 """
    report_date = db.Column(db.Date, nullable=False, unique=True)
    user_count = db.Column(db.Integer, nullable=False, default=0, comment='用户数(ALL组)')
    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='成交额(ALL组)')
    profit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='总盈亏(ALL组)')
    pos_user_count = db.Column(db.Integer, nullable=False, default=0, comment='用户数(盈利组)')
    pos_deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='成交额(盈利组)')
    pos_profit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='总盈亏(盈利组)')
    neg_user_count = db.Column(db.Integer, nullable=False, default=0, comment='用户数(亏损组)')
    neg_deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='成交额(亏损组)')
    neg_profit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='总盈亏(亏损组)')
    expense_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='总成本(ALL组)')
    net_profit_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='净盈亏(ALL组)')


class MonthlyVipReport(ModelBase):
    """vip月报表"""
    report_date = db.Column(db.Date, nullable=False, unique=True)
    user_count = db.Column(db.Integer, nullable=False, default=0, comment='VIP总人数')
    new_count = db.Column(db.Integer, nullable=False, default=0, comment='VIP新增人数')
    vip_1_count = db.Column(db.Integer, nullable=False, default=0, comment='VIP1人数')
    vip_2_count = db.Column(db.Integer, nullable=False, default=0, comment='VIP2人数')
    vip_3_count = db.Column(db.Integer, nullable=False, default=0, comment='VIP3人数')
    vip_4_count = db.Column(db.Integer, nullable=False, default=0, comment='VIP4人数')
    vip_5_count = db.Column(db.Integer, nullable=False, default=0, comment='VIP5人数')
    cet_vip_count = db.Column(db.Integer, nullable=False, default=0, comment='CET持仓类VIP用户数')
    balance_vip_count = db.Column(db.Integer, nullable=False, default=0, comment='资产总值类VIP用户数')
    spot_vip_count = db.Column(db.Integer, nullable=False, default=0, comment='币币交易量类VIP用户数')
    perpetual_vip_count = db.Column(db.Integer, nullable=False, default=0, comment='合约交易量类VIP用户数')


class MonthlyBusinessAmbassadorReferralReport(ModelBase):
    """商务大使返佣月报"""
    report_date = db.Column(db.Date, nullable=False, index=True)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    refer_count = db.Column(db.Integer, nullable=False, default=0)  # 邀请人数
    deal_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人交易人数
    deposit_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人充值人数

    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人币币交易量USD
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人币币手续费USD
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人合约交易量USD
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人合约手续费USD
    refer_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 商务大使返佣数量

    # broker商务大使的字段，仅在商务相关页面展示
    bro_spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 作为Broker 被邀请人币币交易量USD
    bro_spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 作为Broker 被邀请人币币手续费USD
    bro_perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 作为Broker 被邀请人合约交易量USD
    bro_perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 作为Broker 被邀请人合约手续费USD
    bro_refer_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 作为Broker 返佣数量

    user = db.relationship(
        'User',
        backref=db.backref('monthly_business_ambassador_referral_report', lazy='dynamic'),
        foreign_keys=user_id
    )


class MonthlyBusinessAmbassadorDetailReferralReport(ModelBase):
    """商务大使返佣明细月表"""
    
    report_date = db.Column(db.Date, nullable=False, index=True)

    user_id = db.Column(db.Integer, nullable=False, index=True)
    referree_id = db.Column(db.Integer, nullable=False, index=True)

    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 币币交易量USD
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 币币手续费USD
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 合约交易量USD
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 合约手续费USD
    refer_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 商务大使返佣数量
        
    
class MonthlyTreeAmbassadorReferralReport(ModelBase):
    """Tree大使返佣|子代理返佣月报"""

    __table_args__ = (
        db.UniqueConstraint('report_date', 'user_id', name='report_date_user_id_unique'),
    )

    # 其他字段从 MonthlyBusinessAmbassadorReferralReport 取
    report_date = db.Column(db.Date, nullable=False)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    tree_height = db.Column(db.Integer, index=True, nullable=False)  # 用于搜索
    parent_refer_data = db.Column(db.MYSQL_JSON, nullable=False, default=[])  # 给上级的返佣金额


class MonthlyBusinessAgentReferralReport(ModelBase):
    """商务代理返佣月报"""
    report_date = db.Column(db.Date, nullable=False, index=True)

    bus_amb_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # 商务大使用户ID
    target_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)  # 商务代理用户ID

    refer_count = db.Column(db.Integer, nullable=False, default=0)  # 邀请人数
    deal_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人交易人数
    deposit_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人充值人数

    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人币币交易量USD
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人币币手续费USD
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人合约交易量USD
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人合约手续费USD
    refer_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 商务代理返佣数量

    bus_amb = db.relationship(
        'User',
        backref=db.backref('monthly_business_Agent_referral_report_bus_amb', lazy='dynamic'),
        foreign_keys=bus_amb_id
    )
    target_user = db.relationship(
        'User',
        backref=db.backref('monthly_business_Agent_referral_report_target_user', lazy='dynamic'),
        foreign_keys=target_user_id
    )


class MonthlyBusinessUserReferralReport(ModelBase):
    """商务返佣月报"""
    report_date = db.Column(db.Date, nullable=False, index=True)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    refer_ambassador_count = db.Column(db.Integer, nullable=False, default=0)  # 推荐大使人数
    tree_ambassador_count = db.Column(db.Integer, nullable=False, default=0)  # tree大使数目｜子代理人数
    refer_count = db.Column(db.Integer, nullable=False, default=0)  # 邀请人数
    deal_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人交易人数
    deposit_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人充值人数

    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人币币交易量USD
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人币币手续费USD
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人合约交易量USD
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人合约手续费USD
    total_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人总交易量USD
    total_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人总手续费USD
    refer_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 商务返佣数量

    user = db.relationship(
        'User',
        backref=db.backref('monthly_business_user_referral_report', lazy='dynamic'),
        foreign_keys=user_id
    )


class MonthlyBusinessTeamReferralReport(ModelBase):
    """商务组长返佣月报"""
    report_date = db.Column(db.Date, nullable=False, index=True)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    refer_ambassador_count = db.Column(db.Integer, nullable=False, default=0)  # 推荐大使人数
    tree_ambassador_count = db.Column(db.Integer, nullable=False, default=0)  # tree大使数目｜子代理人数
    refer_count = db.Column(db.Integer, nullable=False, default=0)  # 邀请人数
    deal_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人交易人数
    deposit_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人充值人数

    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人币币交易量USD
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人币币手续费USD
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人合约交易量USD
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人合约手续费USD
    total_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人总交易量USD
    total_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人总手续费USD
    refer_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 商务返佣数量

    user = db.relationship(
        'User',
        backref=db.backref('monthly_business_team_referral_report', lazy='dynamic'),
        foreign_keys=user_id
    )


class MonthlyBusinessPrReferralReport(ModelBase):
    """商务PR返佣月报"""
    report_date = db.Column(db.Date, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)

    refer_ambassador_count = db.Column(db.Integer, nullable=False, default=0)  # 推荐大使人数
    tree_ambassador_count = db.Column(db.Integer, nullable=False, default=0)  # tree大使数目｜子代理人数
    refer_count = db.Column(db.Integer, nullable=False, default=0)  # 邀请人数
    deal_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人交易人数
    deposit_count = db.Column(db.Integer, nullable=False, default=0)  # 被邀请人充值人数

    spot_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人币币交易量USD
    spot_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人币币手续费USD
    perpetual_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人合约交易量USD
    perpetual_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人合约手续费USD
    total_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人总交易量USD
    total_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 被邀请人总手续费USD
    refer_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 商务返佣数量


class MonthlyBusinessAmbassadorTotalReferralReport(ModelBase):
    """商务大使月表"""
    report_date = db.Column(db.Date, nullable=False)

    bus_user_count = db.Column(db.Integer, nullable=False, default=0)  # 商务数量
    bus_amb_count = db.Column(db.Integer, nullable=False, default=0)  # 商务大使数量
    new_bus_amb_count = db.Column(db.Integer, nullable=False, default=0)  # 新增商务大使数量
    refer_count = db.Column(db.Integer, nullable=False, default=0)  # 大使邀请总用户数
    new_refer_count = db.Column(db.Integer, nullable=False, default=0)  # 大使邀请新用户数
    deal_count = db.Column(db.Integer, nullable=False, default=0)  # 大使邀请交易用户数
    new_deal_count = db.Column(db.Integer, nullable=False, default=0)  # 大使邀请新增交易用户数

    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 交易总金额
    deal_spot_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 现货交易额
    deal_perpetual_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 合约交易额
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 手续费
    fee_spot_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 现货手续费
    fee_perpetual_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 合约手续费

    refer_amb_count = db.Column(db.Integer, nullable=False, default=0)  # 收到返佣大使数
    refer_amb_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 大使返佣金额
    refer_agent_count = db.Column(db.Integer, nullable=False, default=0)  # 收到返佣代理数
    refer_agent_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 代理返佣金额
    average_refer_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 大使平均返佣比例 = 大使返佣金额 / 大使邀请用户手续费

    refer_bus_count = db.Column(db.Integer, nullable=False, default=0)  # 收到返佣商务
    refer_bus_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 商务返佣金额

    invitee_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # refer注册人数占比
    trade_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # refer交易人数占比
    new_trade_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 新增refer交易人数占比
    team_id = db.Column(db.Integer, nullable=False, index=True, default=0)    # 团队ID(统计全部时设为0)

class MonthlyP2pTradeReport(ModelBase):
    """P2P交易月报"""

    class KeyType(Enum):
        ALL = 'all'
        ASSET = 'asset'
        FIAT = 'fiat'

    report_date = db.Column(db.Date, nullable=False, index=True)
    key_type = db.Column(db.StringEnum(KeyType), nullable=False)
    key = db.Column(db.String(64), index=True)  # ALL 为 null  ASSET 为 具体的币种  FIAT 为 具体法币
    put_user_count = db.Column(db.Integer, nullable=False, default=0, comment='下单人数')
    deal_user_count = db.Column(db.Integer, nullable=False, default=0, comment='成交人数')
    new_user_count = db.Column(db.Integer, nullable=False, default=0, comment='下单新用户数')
    active_merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='活跃商家数')
    deal_merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='成交商家数')
    put_order_count = db.Column(db.Integer, nullable=False, default=0, comment='下单笔数')
    accept_order_count = db.Column(db.Integer, nullable=False, default=0, comment='接单笔数')
    deal_order_count = db.Column(db.Integer, nullable=False, default=0, comment='成交笔数')
    appeal_order_count = db.Column(db.Integer, nullable=False, default=0, comment='申诉笔数')
    deal_volume_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='成交市值USD')
    deal_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='成交数量')


class MonthlyMerchantReport(ModelBase):
    """p2p商家日报"""
    report_date = db.Column(db.Date, nullable=False, unique=True)
    merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='商家总数量')
    new_merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='新增商家数量')
    active_merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='活跃商家数量')
    deal_merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='成交商家数量')
    fiat_amount = db.Column(db.Integer, nullable=False, default=0, comment='法币数量')
    adv_count = db.Column(db.Integer, nullable=False, default=0, comment='广告数量')
    pay_channel_count = db.Column(db.Integer, nullable=False, default=0, comment='支付方式数量')


class MonthlyOperatingExpensesReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('report_date', 'type',
                            name="report_date_type_unique"),
    )

    class Type(Enum):
        ALL = 'ALL'
        COUPON = '卡券支出'
        TRADE_RANK = '交易赛支出'
        AIRDROP = '空投支出'
        DIBS = 'Dibs支出'
        ACTIVITY = '活动支出'
        AMBASSADOR = '大使支出'
        ACCIDENT = '事故支出'
        BUSINESS = '商务支出'
        AMBASSADOR_PACKAGE = '大使激励'

    report_date = db.Column(db.Date, nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    expenses_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)       # 支出金额


class MonthlyInformationReadReport(ModelBase):
    """学院, 洞见, 博客阅读量月报"""
    __table_args__ = (
        db.UniqueConstraint('lang', 'report_date', name='lang_report_date'),
    )
    lang = db.Column(db.StringEnum(Language))  # None时表示所有语言累计
    report_date = db.Column(db.Date, nullable=False)
    academy_read = db.Column(db.Integer, nullable=False)
    insight_read = db.Column(db.Integer, nullable=False)
    insight_rss_read = db.Column(db.Integer, nullable=False)
    blog_read = db.Column(db.Integer, nullable=False)


class MonthlyInformationPublishReport(ModelBase):
    """学院, 洞见, 博客内容发布量月报"""
    __table_args__ = (
        db.UniqueConstraint('lang', 'report_date', name='lang_report_date'),
    )
    lang = db.Column(db.StringEnum(Language))  # None时表示所有语言累计
    report_date = db.Column(db.Date, nullable=False)
    academy = db.Column(db.Integer, nullable=False)
    insight = db.Column(db.Integer, nullable=False)
    blog = db.Column(db.Integer, nullable=False)


class MonthlyOnchainSiteTradeReport(ModelBase):
    """链上交易月报-全站"""

    report_date = db.Column(db.Date, nullable=False, unique=True)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='全站成交额(USD)')
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='手续费收入(USD)')
    gas_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='gas费(USD)')
    trade_token_count = db.Column(db.Integer, nullable=False, default=0, comment='成交token数')
    trade_user_count = db.Column(db.Integer, nullable=False, default=0, comment='成交人数')


class MonthlyOnchainTokenTradeReport(ModelBase):
    """链上交易月报-Token"""

    __table_args__ = (
        db.UniqueConstraint('report_date', 'token_id', name='report_date_token_id_unique'),
    )

    report_date = db.Column(db.Date, nullable=False)
    token_id = db.Column(db.Integer, nullable=False, comment='OnchainToken.id')
    buy_user_count = db.Column(db.Integer, nullable=False, default=0, comment='买入人数')
    buy_order_count = db.Column(db.Integer, nullable=False, default=0, comment='买入笔数')
    buy_amount = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False, default='0', comment='买入数量')
    sell_user_count = db.Column(db.Integer, nullable=False, default=0, comment='卖出人数')
    sell_order_count = db.Column(db.Integer, nullable=False, default=0, comment='卖出笔数')
    sell_amount = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False, default='0', comment='卖出数量')
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='平台佣金')
