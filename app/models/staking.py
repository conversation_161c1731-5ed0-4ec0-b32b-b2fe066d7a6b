# -*- coding: utf-8 -*-

from enum import Enum
from app.common.constants import Language
from app.models import ModelBase, db


class StakingAccount(ModelBase):

    class Status(Enum):
        OPEN = "open"
        CLOSED = "closed"

    ACCOUNT_ID = 40000

    account_id = db.Column(db.Integer, nullable=False)
    asset = db.Column(db.String(32), nullable=False, unique=True)
    status = db.Column(db.StringEnum(Status), nullable=False)
    is_display = db.Column(db.<PERSON><PERSON>, nullable=False, default=False)

class StakingHistory(ModelBase):

    class Type(Enum):
        STAKE = "stake"
        UNSTAKE = "unstake"

    class Status(Enum):
        CREATED = "created"
        QUEUED = "queued"
        DEDUCTED = "deducted" # 不使用，兼容保留
        FAILED = "failed"
        FINISHED = "finished"
    
    class TransferStatus(Enum):
        CREATED = "created"
        DEDUCTED = "deducted"
        FAILED = "failed"
        FINISHED = "finished"

    __table_args__ = (
        db.Index("idx_user_id_status_type", "user_id", "status", "type"),
        db.Index("idx_created_at", "created_at"),
        db.Index("idx_finished_at", "finished_at"),
    )

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    asset = db.Column(db.String(32), nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    finished_at = db.Column(db.MYSQL_DATETIME_6)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    transfer_status = db.Column(db.StringEnum(TransferStatus), nullable=False, default=TransferStatus.CREATED)


class StakingRewardHistory(ModelBase):

    class Status(Enum):
        CREATED = "created"
        FAILED = "failed"
        FINISHED = "finished"

    __table_args__ = (
        db.Index("idx_reward_at_asset_status", "reward_at", "asset", "status"),
        db.UniqueConstraint('user_id', 'reward_at', 'asset', name="user_id_reward_at_asset_uniq"),
    )

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    reward_at = db.Column(db.MYSQL_DATETIME_6, nullable=False) # 奖励时间整点
    asset = db.Column(db.String(32), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class StakingSystemSummary(ModelBase):

    __table_args__ = (
        db.Index("idx_report_at_asset", "report_at", "asset"),
    )

    asset = db.Column(db.String(32), nullable=False)
    report_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    staking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

class StakingUserSummary(ModelBase):

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    asset = db.Column(db.String(32), nullable=False, index=True)

    # 赎回中的数量
    unstaking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    # 质押中的数量
    staking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    daily_reward = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    daily_reward_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    total_reward = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    total_reward_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    last_reward_at = db.Column(db.MYSQL_DATETIME_6)


class StakingPool(ModelBase):
    asset = db.Column(db.String(32), nullable=False, unique=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    unstaking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class StakingSyncRecord(ModelBase):

    class Status(Enum):
        CREATED = "处理中"
        FINISHED = "已完成"
    
    report_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class StakingIntroduction(ModelBase):

    asset = db.Column(db.String(32), nullable=False)
    language = db.Column(db.StringEnum(Language), nullable=False)
    content = db.Column(db.Text, nullable=False)


class StakingIncomeRecord(ModelBase):

    __table_args__ = (
        db.UniqueConstraint('report_date', 'asset', 'type', name='report_date_asset_type_uniq'),
    )

    class Type(Enum):
        INCOME = "income"
        PAYMENT = "payment"
    
    report_date = db.Column(db.Date, nullable=False) # 收入/支出发生的日期
    settle_date = db.Column(db.Date, nullable=False) # 结算日期
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)