# -*- coding: utf-8 -*-
import datetime
import json
from collections import defaultdict
from decimal import Decimal
from enum import IntEnum

from sqlalchemy import func
from webargs import fields
from pyroaring import BitMap

from app import Language
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import DateField, PageField, LimitField, EnumField
from app.business.depth import DepthLadder
from app.caches import PerpetualCoinTypeCache, PerpetualMarketCache, PerpetualOfflineMarketCache
from app.common import ReportType, ADMIN_EXPORT_LIMIT
from app.exceptions import InvalidArgument
from app.models import DailyPerpetualBalanceReport, \
    DailyPerpetualMarginBurstReport, MonthlyPerpetualMarginBurstReport, \
    DailyMakerTradeReport, MonthlyMakerTradeReport, \
    DailyMakerMarketTradeReport, MonthlyMakerMarketTradeReport, DailyPerpetualDepthReport, MonthlyPerpetualDepthReport, \
    DailyPerpetualFundingFeeReport, MonthlyPerpetualFundingFeeReport, DailyPerpetualPositionReport, \
    MonthlyPerpetualPositionReport, \
    DailyPerpetualTradeReport, MonthlyPerpetualTradeReport, DailyThirdPerpetualDepthReport, \
    MonthlyThirdPerpetualDepthReport, DailyPerpetualTransferReport, MonthlyPerpetualTransferReport, \
    DailyPerpetualProfitLossReport, MonthlyPerpetualProfitLossReport, \
    QuarterlyPerpetualProfitLossReport
from app.schedules.statistics.realtime_asset import THIRD_CALCULATORS
from app.utils import current_timestamp, timestamp_to_date, amount_to_str, quantize_amount, export_xlsx, format_percent
from app.utils.helper import Struct

ns = Namespace('Report - Perpetual')


@ns.route('/balance')
@respond_with_code
class DailyPerpetualBalanceResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        query_date=DateField(to_date=True),
        asset=fields.String(),
    ))
    def get(cls, **kwargs):
        """报表-资产报表-合约"""
        yesterday = timestamp_to_date(current_timestamp() - 86400)
        query_date = kwargs.get('query_date') or yesterday

        query = DailyPerpetualBalanceReport.query.filter(
            DailyPerpetualBalanceReport.report_date == query_date,
        )

        if kwargs.get('asset'):
            query = query.filter(
                DailyPerpetualBalanceReport.asset == kwargs['asset'])

        records = []

        for record in query.all():
            if record.amount > 0:
                tmp = record.to_dict()
                tmp['amount'] = amount_to_str(tmp['amount'], 2)
                records.append(tmp)

        last_update_time = DailyPerpetualBalanceReport.query.order_by(
            DailyPerpetualBalanceReport.report_date.desc()).first().report_date

        return dict(
            records=records,
            levels=DailyPerpetualBalanceReport.LEVEL_TABLE,
            asset_list=sorted(PerpetualCoinTypeCache().read_aside()),
            last_update_time=last_update_time,
        )


@ns.route('/asset-balance')
@respond_with_code
class DailyPerpetualAssetBalanceResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField,
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """报表-资产报表-合约-币种详情"""
        yesterday = timestamp_to_date(current_timestamp() - 86400)
        asset = kwargs.get('asset')
        page, limit = kwargs['page'], kwargs['limit']

        end_date = kwargs.get('end_date') or yesterday
        query = DailyPerpetualBalanceReport.query
        query = query.filter(
            DailyPerpetualBalanceReport.report_date <= end_date,
            DailyPerpetualBalanceReport.amount > 0,
        )
        if kwargs.get('start_date'):
            query = query.filter(
                DailyPerpetualBalanceReport.report_date >= kwargs[
                    'start_date'])
        if asset:
            query = query.filter(DailyPerpetualBalanceReport.asset == asset)
        records = query.order_by(
            DailyPerpetualBalanceReport.report_date.desc()
        ).paginate(page, limit)

        format_records = []

        for record in records.items:
            tmp = record.to_dict()
            tmp['amount'] = amount_to_str(tmp['amount'], 2)
            format_records.append(tmp)

        return dict(
            records=format_records,
            total=records.total,
            levels=DailyPerpetualBalanceReport.LEVEL_TABLE,
            asset_list=sorted(PerpetualCoinTypeCache().read_aside()),
        )


@ns.route('/liquidation')
@respond_with_code
class MarginLiquidationResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        market=fields.String,
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        sort_name=EnumField(
            [
                "report_date",
                "burst_usd",
                "user_count",
                "interest_insurance_amount",
                "liquidation_insurance_amount",
            ], missing='report_date'
        ),
    ))
    def get(cls, **kwargs):
        """报表-合约报表-爆仓-市场"""

        page = kwargs['page']
        limit = kwargs['limit']
        sort_name = kwargs['sort_name']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyPerpetualMarginBurstReport
        else:
            model = MonthlyPerpetualMarginBurstReport
        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date <= end_date)
        if market := kwargs.get('market'):
            query = query.filter(model.market == market)
        report_data = [i.to_dict() for i in query.all()]
        total = len(report_data)

        if sort_name in ['interest_insurance_amount', 'liquidation_insurance_amount']:
            # 老板的需求，将BTC，ETH，USDT分开处理排序
            btc_market = 'BTCUSD'
            eth_market = 'ETHUSD'
            not_usd_market = {btc_market, eth_market}
            btc_sort_list = [i for i in report_data if i['market'] == btc_market]
            eth_sort_list = [i for i in report_data if i['market'] == eth_market]
            usdt_sort_list = [i for i in report_data if i['market'] not in not_usd_market]
            btc_sort_list = sorted(btc_sort_list, key=lambda x: x[sort_name], reverse=True)
            eth_sort_list = sorted(eth_sort_list, key=lambda x: x[sort_name], reverse=True)
            usdt_sort_list = sorted(usdt_sort_list, key=lambda x: x[sort_name], reverse=True)
            report_data = btc_sort_list + eth_sort_list + usdt_sort_list
        else:
            report_data = sorted(report_data, key=lambda x: x[sort_name], reverse=True)
        result = report_data[(page - 1) * limit: page * limit]
        records = []
        online_market_list = set(PerpetualMarketCache().get_market_list())
        for record in result:
            _cache = PerpetualMarketCache if record['market'] in online_market_list else PerpetualOfflineMarketCache
            record['interest_insurance_rate'] = amount_to_str(
                record['interest_insurance_usd'] / (record['burst_usd']-record['cross_liq_usd']) * 100
                if (record['burst_usd']-record['cross_liq_usd']) else Decimal(), 4) + "%"
            record['liquidation_insurance_usd_rate'] = amount_to_str(
                record['liquidation_insurance_usd'] / record['cross_liq_usd'] * 100 if record[
                    'cross_liq_usd'] else Decimal(), 2) + "%"
            record['cross_liq_usd_rate'] = amount_to_str(
                record['cross_liq_usd'] / record['burst_usd'] * 100 if record[
                    'burst_usd'] else Decimal(), 2) + "%"
            record['cross_liq_count_rate'] = amount_to_str(
                record['cross_liq_count'] / record['burst_count'] * 100 if record[
                    'burst_count'] else Decimal(), 2) + "%"
            record['auto_deleverage_usd_rate'] = amount_to_str(
                record['auto_deleverage_usd'] / record['burst_usd'] * 100 if record[
                    'burst_usd'] else Decimal(), 2) + "%"
            record['auto_deleverage_count_rate'] = amount_to_str(
                record['auto_deleverage_count'] / record['burst_count'] * 100 if record[
                    'burst_count'] else Decimal(), 2) + "%"
            record['report_date'] = record['report_date'].strftime('%Y-%m-%d')
            record['balance_asset'] = _cache.get_balance_asset(record['market'])
            record['amount_asset'] = _cache.get_amount_asset(record['market'])
            record['long_burst_usd_rate'] = format_percent(record['long_burst_usd'] / record['burst_usd']) if record[
                'burst_usd'] else '0%'
            record['long_burst_usd'] = amount_to_str(record['long_burst_usd'], 2)
            record['long_user_count_rate'] = format_percent(record['long_user_count'] / record['user_count']) if record[
                'user_count'] else '0%'
            record['long_burst_count_rate'] = format_percent(record['long_burst_count'] / record['burst_count']) if record[
                'burst_count'] else '0%'
            record['long_burst_count'] = amount_to_str(record['long_burst_count'], 2)
            records.append(record)

        return dict(
            records=records,
            total=total,
            page=page,
            market_list=PerpetualMarketCache().get_market_list())


@ns.route('/liquidation-summary')
@respond_with_code
class MarginLiquidationSummaryResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-合约报表-全站爆仓聚合统计"""

        page = kwargs['page']
        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyPerpetualMarginBurstReport
        else:
            model = MonthlyPerpetualMarginBurstReport
        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date <= end_date)

        query = query.group_by(
            model.report_date,
        ).with_entities(
            model.report_date,
            func.sum(model.burst_usd).label('burst_usd'),
            func.sum(model.interest_insurance_usd).label('interest_insurance_usd'),
            func.sum(model.liquidation_insurance_usd).label('liquidation_insurance_usd'),
            func.sum(model.user_count).label('user_count'),
            func.sum(model.burst_count).label('burst_count'),
            func.sum(model.cross_liq_usd).label('cross_liq_usd'),
            func.sum(model.cross_liq_count).label('cross_liq_count'),
            func.sum(model.auto_deleverage_usd).label('auto_deleverage_usd'),
            func.sum(model.auto_deleverage_count).label('auto_deleverage_count'),
            func.sum(model.long_burst_usd).label('long_burst_usd'),
            func.sum(model.long_burst_count).label('long_burst_count'),
        )

        pagination = query.paginate(page, limit, error_out=False)
        pagination_result = pagination.items
        user_count_query = model.query.filter(model.report_date.in_([i.report_date for i in pagination_result])).all()

        user_set_date_map = defaultdict(set)
        long_user_set_date_map = defaultdict(set)
        for item in user_count_query:
            user_set_date_map[item.report_date].update(json.loads(item.user_list))
            if item.long_user_list:
                long_user_set_date_map[item.report_date].update(json.loads(item.long_user_list))
        records = []

        for item in pagination_result:
            record = dict(
                burst_usd=quantize_amount(item.burst_usd, 2),
                cross_liq_usd=quantize_amount(item.cross_liq_usd, 2),
                auto_deleverage_usd=quantize_amount(item.auto_deleverage_usd, 2),
                interest_insurance_usd=quantize_amount(item.interest_insurance_usd, 2),
                interest_insurance_rate=amount_to_str(
                    item.interest_insurance_usd / (item.burst_usd-item.cross_liq_usd) * 100
                    if (item.burst_usd-item.cross_liq_usd) else Decimal(), 2) + "%",
                liquidation_insurance_usd_rate=amount_to_str(
                    item.liquidation_insurance_usd / item.cross_liq_usd * 100 if item.cross_liq_usd else Decimal(), 2) + "%",
                cross_liq_usd_rate=amount_to_str(
                    item.cross_liq_usd / item.burst_usd * 100 if item.burst_usd else Decimal(),
                    2) + "%",
                cross_liq_count_rate=amount_to_str(
                    item.cross_liq_count / item.burst_count * 100 if item.burst_count else Decimal(),
                    2) + "%",
                auto_deleverage_usd_rate=amount_to_str(
                    item.auto_deleverage_usd / item.burst_usd * 100 if item.burst_usd else Decimal(),
                    2) + "%",
                auto_deleverage_count_rate=amount_to_str(
                    item.auto_deleverage_count / item.burst_count * 100 if item.burst_count else Decimal(),
                    2) + "%",
                liquidation_insurance_usd=quantize_amount(item.liquidation_insurance_usd, 2),
                market='全部',
                report_date=item.report_date.strftime('%Y-%m-%d'),
                user_count=len(user_set_date_map[item.report_date]),
                burst_count=item.burst_count,
                cross_liq_count=item.cross_liq_count,
                auto_deleverage_count=item.auto_deleverage_count,
                long_burst_usd=quantize_amount(item.long_burst_usd, 2),
                long_burst_usd_rate=amount_to_str(
                    item.long_burst_usd / item.burst_usd * 100 if item.burst_usd else Decimal(),
                    2) + "%",
                long_user_count=len(long_user_set_date_map[item.report_date]),
                long_user_count_rate=amount_to_str(
                    len(long_user_set_date_map[item.report_date]) / len(
                        user_set_date_map[item.report_date]) * 100 if len(
                        user_set_date_map[item.report_date]) else Decimal(),
                    2) + "%",
                long_burst_count = amount_to_str(item.long_burst_count, 2),
                long_burst_count_rate = format_percent(item.long_burst_count / item.burst_count) if item.burst_count else '0%',
            )
            records.append(record)

        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
            market_list=PerpetualMarketCache().get_market_list()
        )


@ns.route('/maker-deals')
@respond_with_code
class DailySpotMakerDealsResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "deal_count", Language.ZH_HANS_CN: "成交用户数"},
        {"field": "deal_usd", Language.ZH_HANS_CN: "成交市值(USD)"},
        {"field": "deal_percent", Language.ZH_HANS_CN: "成交市值全站占比"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "手续费市值（USD）"},
        {"field": "fee_percent", Language.ZH_HANS_CN: "手续费市值全站占比"},
        {"field": "avg_fee_rate", Language.ZH_HANS_CN: "平均费率"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        maker_type=fields.String(required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """报表-做市报表-合约"""
        if kwargs['maker_type'] not in ['ALL', 'INNER', 'OUTER']:
            raise InvalidArgument

        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyMakerTradeReport
        else:
            model = MonthlyMakerTradeReport
        query = model.query.filter(
            model.system == model.System.PERPETUAL,
            model.maker_type == kwargs['maker_type'],
        ).order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)

        total = 0
        page = 1
        if kwargs['export']:
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            pagination = query.paginate(kwargs['page'], limit, error_out=False)
            items = pagination.items
            total = pagination.total
            page = pagination.page
        if kwargs['export']:
            return export_xlsx(
                filename='perpetual_maker_report',
                data_list=cls.get_export_data(items),
                export_headers=cls.export_headers
            )
        return dict(
            records=items,
            extra=dict(maker_types=model.MakerType),
            total=total,
            page=page,
        )

    @classmethod
    def get_export_data(cls, records):
        export_data = []
        for row in records:
            record = row.to_dict()
            record['deal_usd'] = amount_to_str(record['deal_usd'], 2)
            record['fee_usd'] = amount_to_str(record['fee_usd'], 2)
            record['deal_percent'] = format_percent(record['deal_percent'], 4)
            record['fee_percent'] = format_percent(record['fee_percent'], 4)
            record['avg_fee_rate'] = format_percent(record['avg_fee_rate'], 4)

            export_data.append(record)
        return export_data


@ns.route('/maker-market-detail')
@respond_with_code
class DailySpotMakerMarketDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True, missing=''),
        order=fields.String(missing='deal_usd'),
        trading_area=fields.String(missing=''),
        report_type=EnumField(ReportType, required=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-做市报表-合约交易市场详情"""

        order = kwargs['order']
        report_date = kwargs['report_date']
        page = kwargs['page']
        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyMakerMarketTradeReport
        else:
            model = MonthlyMakerMarketTradeReport

        query = model.query.filter(model.system == model.System.PERPETUAL)
        if not report_date:
            last_item = query.order_by(
                model.report_date.desc()).limit(1).first()
            report_date = last_item.report_date.strftime(
                '%Y-%m-%d') if last_item else None
        if report_date:
            if kwargs['report_type'] == ReportType.MONTHLY:
                report_date = datetime.date(report_date.year, report_date.month, 1)
            query = query.filter(
                model.report_date == report_date)

        if order == 'deal_usd':
            query = query.order_by(model.deal_usd.desc())
        elif order == 'fee_usd':
            query = query.order_by(
                model.fee_usd.desc())
        elif order == 'deal_count':
            query = query.order_by(
                model.deal_count.desc())
        query = query.filter(
            model.report_date == report_date)

        total_market_deal_usd, total_market_fee_usd, total_deal_user_set = Decimal(), Decimal(), set()
        total_deal_usd, total_fee_usd = Decimal(), Decimal()

        for item in query.all():
            total_market_deal_usd += item.deal_usd
            total_deal_usd += item.total_deal_usd
            total_market_fee_usd += item.fee_usd
            total_fee_usd += item.total_fee_usd
            total_deal_user_set |= set(json.loads(item.deal_user_list))
        records = [
            {
                'report_date': '合计',
                'market': 'ALL',
                'deal_count': len(total_deal_user_set),
                'deal_usd': total_market_deal_usd,
                'fee_usd': total_market_fee_usd,
                'deal_percent': total_market_deal_usd/total_deal_usd if total_deal_usd else 0,
                'fee_percent': total_market_fee_usd/total_fee_usd if total_fee_usd else 0,
                'avg_fee_rate': total_market_fee_usd/total_market_deal_usd if total_market_deal_usd else 0,
                'taker_sell_amount': '-',
            }
        ]

        pagination = query.paginate(page, limit, error_out=False)
        for item in pagination.items:
            report_date = item.report_date.strftime('%Y-%m-%d') \
                if kwargs['report_type'] == ReportType.DAILY else item.report_date.strftime('%Y-%m')
            record = {
                'report_date': report_date,
                'market': item.market,
                'deal_count': item.deal_count,
                'deal_usd': item.deal_usd,
                'deal_percent': item.deal_percent,
                'fee_usd': item.fee_usd,
                'fee_percent': item.fee_percent,
                'avg_fee_rate': item.avg_fee_rate,
            }
            records.append(record)

        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
        )


@ns.route('/depth')
@respond_with_code
class DailyPerpetualDepthResource(Resource):

    ALL_MARKETS = 'ALL'

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "depth1", Language.ZH_HANS_CN: "±0.2%深度"},
        {"field": "depth2", Language.ZH_HANS_CN: "±0.5%深度"},
        {"field": "depth3", Language.ZH_HANS_CN: "±1%深度"},
        {"field": "depth4", Language.ZH_HANS_CN: "±2%深度"},
        {"field": "depth5", Language.ZH_HANS_CN: "±5%深度"},
    )
    exchanges = list(THIRD_CALCULATORS.keys()) + ['CoinEx']

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        market=fields.String(missing=ALL_MARKETS),
        exchange=fields.String(missing='CoinEx'),
        export=fields.Boolean(missing=False),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-合约交易-深度报表"""
        records = []
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyPerpetualDepthReport
            if kwargs['exchange'] in list(THIRD_CALCULATORS.keys()):
                model = DailyThirdPerpetualDepthReport
        else:
            model = MonthlyPerpetualDepthReport
            if kwargs['exchange'] in list(THIRD_CALCULATORS.keys()):
                model = MonthlyThirdPerpetualDepthReport

        query = cls.get_query_by(kwargs, model)
        if kwargs.get('export'):
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
            return export_xlsx(
                filename='depth_report',
                data_list=cls.get_export_data(items),
                export_headers=cls.export_headers
            )
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        for obj in pagination.items:
            records.append(
                dict(
                    report_date=obj.report_date.strftime('%Y-%m-%d'),
                    market=obj.market,
                    depth1=amount_to_str(obj.depth1_usd, 2),
                    depth2=amount_to_str(obj.depth2_usd, 2),
                    depth3=amount_to_str(obj.depth3_usd, 2),
                    depth4=amount_to_str(obj.depth4_usd, 2),
                    depth5=amount_to_str(obj.depth5_usd, 2),
                )
            )

        return dict(
            records=cls.format_records(records),
            exchanges=cls.exchanges,
            market_list=PerpetualMarketCache().get_market_list(),
            total=pagination.total,
        )

    @classmethod
    def get_query_by(cls, kwargs, model):
        # todo：校验日期关系

        if kwargs['market'] != cls.ALL_MARKETS:
            query = model.query.filter(model.market == kwargs['market'])
        if kwargs.get('start_date'):
            query = query.filter(model.report_date >= kwargs['start_date'])
        if kwargs.get('end_date'):
            query = query.filter(model.report_date <= kwargs['end_date'])
        if kwargs.get('exchange') in list(THIRD_CALCULATORS.keys()):
            query = query.filter(model.exchange == kwargs['exchange'].lower())
        
        if kwargs['market'] == cls.ALL_MARKETS:
            query = query.group_by(model.report_date).with_entities(
                # add a market column here, value: "ALL"
                
                model.report_date,
                func.sum(model.depth1_usd).label('depth1_usd'),
                func.sum(model.depth2_usd).label('depth2_usd'),
                func.sum(model.depth3_usd).label('depth3_usd'),
                func.sum(model.depth4_usd).label('depth4_usd'),
                func.sum(model.depth5_usd).label('depth5_usd'),
            )

        return query.order_by(model.report_date.desc())

    @classmethod
    def get_export_data(cls, records):
        export_data = []
        for row in records:
            record = row.to_dict()
            record['depth1'] = amount_to_str(record['depth1_usd'], 2)
            record['depth2'] = amount_to_str(record['depth2_usd'], 2)
            record['depth3'] = amount_to_str(record['depth3_usd'], 2)
            record['depth4'] = amount_to_str(record['depth4_usd'], 2)
            record['depth5'] = amount_to_str(record['depth5_usd'], 2)

            export_data.append(record)
        return cls.format_records(export_data)

    @classmethod
    def format_records(cls, records):
        stage_list = ['depth1', 'depth2', 'depth3', 'depth4', 'depth5']
        for r in records:
            for i, stage in enumerate(stage_list):
                if stage == stage_list[-1]:
                    continue
                k = stage
                v = r[k]

                equal_to_later_values = True
                j = i + 1
                for idx in range(j, len(stage_list)):
                    k1 = stage_list[idx]
                    v1 = r[k1]
                    if v != v1:
                        equal_to_later_values = False
                        break
                if not equal_to_later_values:
                    continue

                for idx in range(j, len(stage_list)):
                    _k = stage_list[idx]
                    r[_k] = '/'
                break
        return records


@ns.route('/depth-detail')
@respond_with_code
class DailySpotDepthDetailResource(Resource):
    export_headers = DailyPerpetualDepthResource.export_headers
    get_export_data = DailyPerpetualDepthResource.get_export_data
    format_format_records = DailyPerpetualDepthResource.format_records
    exchanges = DailyPerpetualDepthResource.exchanges

    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True, allow_none=True),
        order=fields.Integer(missing=1),
        report_type=EnumField(ReportType, required=True),
        exchange=fields.String(missing='CoinEx'),
        export=fields.Boolean(missing=False),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-合约交易-深度详情"""
        records = []
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyPerpetualDepthReport
            if kwargs['exchange'] in list(THIRD_CALCULATORS.keys()):
                model = DailyThirdPerpetualDepthReport
        else:
            model = MonthlyPerpetualDepthReport
            if kwargs['exchange'] in list(THIRD_CALCULATORS.keys()):
                model = MonthlyThirdPerpetualDepthReport
        query = cls.get_query_by(kwargs, model)
        if kwargs.get('export'):
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
            return export_xlsx(
                filename='depth_detail__report',
                data_list=cls.get_export_data(items),
                export_headers=cls.export_headers
            )
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        for obj in pagination.items:
            records.append(
                dict(
                    report_date=obj.report_date.strftime('%Y-%m-%d'),
                    market=obj.market,
                    depth1=amount_to_str(obj.depth1_usd, 2),
                    depth2=amount_to_str(obj.depth2_usd, 2),
                    depth3=amount_to_str(obj.depth3_usd, 2),
                    depth4=amount_to_str(obj.depth4_usd, 2),
                    depth5=amount_to_str(obj.depth5_usd, 2),
                )
            )
        return dict(
            records=cls.format_format_records(records),
            exchanges=cls.exchanges,
            total=pagination.total,
            page=pagination.page,
        )

    @classmethod
    def get_query_by(cls, kwargs, model):
        query = model.query
        order_field = cls.get_order_field(kwargs['order'], model)
        if order_field:
            query = query.order_by(order_field.desc())

        report_date = kwargs.get('report_date')
        if not report_date:
            obj = query.order_by(model.report_date.desc()).limit(1).first()
            if obj:
                report_date = obj.report_date
        if report_date:
            query = query.filter(model.report_date == report_date)
        if kwargs.get('exchange') in list(THIRD_CALCULATORS.keys()):
            query = query.filter(model.exchange == kwargs['exchange'].lower())
        return query

    @classmethod
    def get_order_field(cls, depth: int, model):
        mapping = {
            DepthLadder.depth1.value: model.depth1_usd,
            DepthLadder.depth2.value: model.depth2_usd,
            DepthLadder.depth3.value: model.depth3_usd,
            DepthLadder.depth4.value: model.depth4_usd,
            DepthLadder.depth5.value: model.depth5_usd,
        }
        return mapping.get(depth)


@ns.route('/funding-fee')
@respond_with_code
class FundingFeeResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        report_type=EnumField(ReportType, required=True),
        market=fields.String,
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-合约交易-资金费用"""
        params = Struct(**kwargs)
        if params.report_type == ReportType.DAILY:
            model = DailyPerpetualFundingFeeReport
        else:
            model = MonthlyPerpetualFundingFeeReport
        query = cls.get_query_by(params, model)
        pagination = query.paginate(params.page, params.limit, error_out=False)

        records = []
        for obj in pagination.items:
            row_dict = obj.to_dict()
            row_dict['report_date'] = row_dict['report_date'].strftime('%Y-%m-%d')
            row_dict['total_fee_rate'] = format_percent(row_dict['total_fee_rate'], 4)
            row_dict['net_fee_rate'] = format_percent(row_dict['net_fee_rate'], 4)
            records.append(row_dict)
        return dict(
            records=records,
            market_list=PerpetualMarketCache().get_market_list(),
            total=pagination.total,
        )

    @classmethod
    def get_query_by(cls, params, model):
        query = model.query.order_by(model.report_date.desc())
        if params.start_date:
            query = query.filter(model.report_date >= params.start_date)
        if params.end_date:
            query = query.filter(model.report_date <= params.end_date)
        if params.market:
            query = query.filter(model.market == params.market)
        else:
            query = query.filter(model.market == '')

        return query


@ns.route('/funding-fee-detail')
@respond_with_code
class FundingFeeDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True, required=True),
        report_type=EnumField(ReportType, required=True),
        order=fields.String(missing='total_usd'),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-合约交易-资金费用市场详情"""
        params = Struct(**kwargs)
        if params.report_type == ReportType.DAILY:
            model = DailyPerpetualFundingFeeReport
        else:
            model = MonthlyPerpetualFundingFeeReport
        query = cls.get_query_by(params, model)
        pagination = query.paginate(params.page, params.limit, error_out=False)

        total_record = model.query.filter(model.market == '', model.report_date == params.report_date).first()
        if total_record:
            total_row_dict = total_record.to_dict()
            total_row_dict['report_date'] = total_row_dict['report_date'].strftime('%Y-%m-%d')
            total_row_dict['market'] = 'ALL'
            total_row_dict['total_fee_rate'] = format_percent(total_row_dict['total_fee_rate'], 4)
            total_row_dict['net_fee_rate'] = format_percent(total_row_dict['net_fee_rate'], 4)
            records = [total_row_dict]
        else:
            records = []
        for obj in pagination.items:
            row_dict = obj.to_dict()
            row_dict['report_date'] = row_dict['report_date'].strftime('%Y-%m-%d')
            row_dict['total_fee_rate'] = format_percent(row_dict['total_fee_rate'], 4)
            row_dict['net_fee_rate'] = format_percent(row_dict['net_fee_rate'], 4)
            records.append(
                row_dict
            )
        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
        )

    @classmethod
    def get_query_by(cls, params, model):
        query = model.query
        order_field = cls.get_order_field(params.order, model)
        if order_field:
            query = query.order_by(order_field.desc())

        query = query.filter(model.report_date == params.report_date)
        query = query.filter(model.market != '')

        return query

    @classmethod
    def get_order_field(cls, order: str, model):
        mapping = {
            'long_count': model.long_count,
            'short_count': model.short_count,
            'total_count': model.total_count,
            'total_usd': model.total_usd,
            'total_fee_rate': model.total_fee_rate,
            'net_usd': model.net_usd,
            'net_fee_rate': model.net_fee_rate,
        }
        return mapping.get(order)


def get_time_display_str(ts):
    from app.utils import amount_to_str

    if ts < 60:
        ts = amount_to_str(ts, 2)
        return f'{ts}s'
    elif ts < 3600:
        m = ts // 60
        s = ts % 60
        s = amount_to_str(s, 2)
        m = int(m)
        return f'{m}m {s}s'
    else:
        h = ts // 3600
        m = (ts - h * 3600) // 60
        s = ts % 60
        s = amount_to_str(s, 2)
        h = int(h)
        m = int(m)
        return f'{h}h {m}m {s}s'


time_range_display_map = {
    'LESS_THAN_ONE_MINUTE': 'X <= 1m',
    'LESS_THAN_FIVE_MINUTES': '1m < X <= 5m',
    'LESS_THAN_FIFTEEN_MINUTES': '5m < X <= 15m',
    'LESS_THAN_ONE_HOUR': '15m < X <= 1H',
    'LESS_THAN_FOUR_HOURS': '1H < X <= 4H',
    'LESS_THAN_TWENTY_FOUR_HOURS': '4H < X <= 24H',
    'LESS_THAN_SEVENTY_TWO_HOURS': '24H < X <= 72H',
    'MORE_THAN_SEVENTY_TWO_HOURS': 'X > 72H'
}


@ns.route('/position-analysis')
@respond_with_code
class PerpetualPositionResource(Resource):

    class DataType(IntEnum):
        STATISTICS = 1
        RECORDS = 2

    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        report_type=EnumField(ReportType, required=True),
        time_range=fields.String,
        data_type=EnumField(DataType, missing=DataType.RECORDS),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-合约报表-仓位分析"""
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyPerpetualPositionReport
        else:
            model = MonthlyPerpetualPositionReport
        query = model.query
        start_date, end_date = kwargs.get('start_date'), kwargs.get('end_date')
        if start_date:
            query = query.filter(model.report_date >= start_date)
        if end_date:
            query = query.filter(model.report_date <= end_date)
        query = query.order_by(model.report_date.desc())
        if kwargs['data_type'] == cls.DataType.STATISTICS:
            if kwargs['report_type'] == ReportType.DAILY:
                trade_model = DailyPerpetualTradeReport
            else:
                trade_model = MonthlyPerpetualTradeReport
            trade_query = trade_model.query
            if start_date:
                trade_query = trade_query.filter(trade_model.report_date >= start_date)
            if end_date:
                trade_query = trade_query.filter(trade_model.report_date <= end_date)
            trade_records = trade_query.with_entities(trade_model.deal_user_list).all()
            all_records = query.with_entities(
                model.excluded_trade_user_bitmap,
                model.close_position_user_bitmap,
            ).all()
            all_trade_user_set, excluded_user_set, close_position_user_set = set(), set(), set()
            for record in trade_records:
                all_trade_user_set |= set(json.loads(record.deal_user_list))
            for record in all_records:
                excluded_user_set |= set(BitMap.deserialize(record.excluded_trade_user_bitmap))
                close_position_user_set |= set(BitMap.deserialize(record.close_position_user_bitmap))
            return dict(
                total_trade_user_count=len(all_trade_user_set - excluded_user_set),
                close_position_user_count=len(close_position_user_set)
            )

        page, limit = kwargs['page'], kwargs['limit']
        sum_query = query.with_entities(
            model.report_date,
            func.sum(model.position_count).label('position_count'),
            func.sum(model.trade_amount).label('trade_amount'),
            func.sum(model.position_count * model.average_position_time).label('position_time'),
            func.sum(model.position_count * model.average_leverage).label('leverage'),
        ).group_by(model.report_date)
        total_data_map = dict()
        for item in sum_query:
            total_data_map[item.report_date] = item
        if _range:= kwargs.get('time_range'):
            time_range = getattr(model.TimeRange, _range)
            query = query.filter(model.time_range == time_range)
            sum_query = sum_query.filter(model.time_range == time_range)

        all_position_count = all_trade_amount = all_position_time = all_leverage = 0
        for item in sum_query:
            all_position_count += item.position_count
            all_trade_amount += item.trade_amount
            all_position_time += item.position_time
            all_leverage += item.leverage

        pagination = query.paginate(page, limit, error_out=False)
        total, records, page_count, has_next = \
            pagination.total, pagination.items, pagination.pages, pagination.has_next

        res = []
        sum_record = dict(
            position_count=all_position_count,
            position_percent='--',
            user_count='--',
            user_percent='--',
            trade_amount=all_trade_amount,
            trade_amount_percent='--',
            average_position_count='--',
            average_position_time_raw=all_position_time / all_position_count if all_position_count else 0,
            average_position_time=get_time_display_str(
                all_position_time / all_position_count if all_position_count else 0),
            average_leverage=amount_to_str(
                all_leverage / all_position_count if all_position_count else 0, 2),
            average_trade_amount=amount_to_str(
                all_trade_amount / all_position_count if all_position_count else 0, 2),
        )
        for record in records:
            total_data = total_data_map[record.report_date]
            res.append(dict(
                report_date=record.report_date,
                time_range=record.time_range.name,
                position_count=record.position_count,
                position_percent=format_percent(
                    record.position_count / total_data.position_count if total_data.position_count else 0, 2),
                user_count=record.position_user_count,
                user_percent=format_percent(
                    record.position_user_count / record.total_trade_user_count if record.total_trade_user_count else 0, 2),
                trade_amount=record.trade_amount,
                trade_amount_percent=format_percent(
                    record.trade_amount / total_data.trade_amount if total_data.trade_amount else 0, 2),
                average_position_count=amount_to_str(
                    record.position_count / record.position_user_count if record.position_user_count else 0, 2),
                average_position_time_raw=record.average_position_time,
                average_position_time=get_time_display_str(record.average_position_time),
                average_leverage=record.average_leverage,
                average_trade_amount=amount_to_str(
                    record.trade_amount / record.position_count if record.position_count else 0, 2)
            ))
        return dict(
            data=res,
            sum_record=sum_record,
            time_ranges=time_range_display_map,
            total=total,
            total_page=page_count,
            has_next=has_next,
        )


@ns.route('/transfer')
@respond_with_code
class PerpetualTransferReportResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "transfer_user_count", Language.ZH_HANS_CN: "划转人数"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "交易人数"},
        {"field": "transfer_in_user_count", Language.ZH_HANS_CN: "转入人数"},
        {"field": "transfer_in_usd", Language.ZH_HANS_CN: "转入市值（USD）"},
        {"field": "transfer_out_user_count", Language.ZH_HANS_CN: "转出人数"},
        {"field": "transfer_out_usd", Language.ZH_HANS_CN: "转出市值（USD）"},
        {"field": "transfer_net_usd", Language.ZH_HANS_CN: "净转入市值（USD）"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            report_type=EnumField(ReportType, required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            page=PageField(unlimited=True),
            limit=LimitField(missing=50),
            export=fields.Boolean(missing=False),
        )
    )
    def get(cls, **kwargs):
        """报表-合约报表-合约划转报表"""
        page = kwargs["page"]
        limit = kwargs["limit"]
        export = kwargs["export"]
        if kwargs["report_type"] == ReportType.DAILY:
            model = DailyPerpetualTransferReport
        else:
            model = MonthlyPerpetualTransferReport

        query = model.query.order_by(model.report_date.desc())
        if end_date := kwargs.get("end_date"):
            query = query.filter(model.report_date <= end_date)
        if start_date := kwargs.get("start_date"):
            query = query.filter(model.report_date >= start_date)

        query = query.with_entities(
            model.report_date,
            model.transfer_user_count,
            model.trade_user_count,
            model.transfer_in_user_count,
            model.transfer_in_usd,
            model.transfer_out_user_count,
            model.transfer_out_usd,
            model.transfer_net_usd,
        )

        total = 0
        if export:
            rows = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            pagination = query.paginate(page, limit, error_out=False)
            rows = pagination.items
            total = pagination.total
        items = []
        for i in rows:
            items.append(
                {
                    "report_date": i.report_date,
                    "transfer_user_count": i.transfer_user_count,
                    "trade_user_count": i.trade_user_count,
                    "transfer_in_user_count": i.transfer_in_user_count,
                    "transfer_in_usd": i.transfer_in_usd,
                    "transfer_out_user_count": i.transfer_out_user_count,
                    "transfer_out_usd": i.transfer_out_usd,
                    "transfer_net_usd": i.transfer_net_usd,
                }
            )

        if export:
            for i in items:
                i["report_date"] = i["report_date"].strftime("%Y-%m-%d")
            return export_xlsx(
                filename='perpetual_transfer_report',
                data_list=items,
                export_headers=cls.export_headers,
            )
        return dict(
            items=items,
            total=total,
        )


@ns.route('/profit-loss')
@respond_with_code
class PerpetualProfitLossResource(Resource):
    model_mapping = {
        'DAILY': DailyPerpetualProfitLossReport,
        'MONTHLY': MonthlyPerpetualProfitLossReport,
        'QUARTERLY': QuarterlyPerpetualProfitLossReport,
    }

    @classmethod
    @ns.use_kwargs(dict(
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        report_type=fields.String(missing='DAILY'),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-合约报表-合约盈亏统计"""
        model = cls.model_mapping[kwargs['report_type']]
        query = model.query
        start_date, end_date = kwargs.get('start_date'), kwargs.get('end_date')
        if start_date:
            query = query.filter(model.report_date >= start_date)
        if end_date:
            query = query.filter(model.report_date <= end_date)
        query = query.order_by(model.report_date.desc())
        page, limit = kwargs['page'], kwargs['limit']
        pagination = query.paginate(page, limit, error_out=False)
        total, records, page_count, has_next = pagination.total, pagination.items, pagination.pages, pagination.has_next
        ret = []
        for record in records:
            record_dict = record.to_dict()
            record_dict['deal_usd'] = amount_to_str(record.deal_usd, 2)
            record_dict['profit'] = amount_to_str(record.profit, 2)
            record_dict['pos_deal_usd'] = amount_to_str(record.pos_deal_usd, 2)
            record_dict['pos_profit'] = amount_to_str(record.pos_profit, 2)
            record_dict['neg_deal_usd'] = amount_to_str(record.neg_deal_usd, 2)
            record_dict['neg_profit'] = amount_to_str(record.neg_profit, 2)
            record_dict['expense_usd'] = amount_to_str(record.expense_usd, 2)
            record_dict['net_profit_usd'] = amount_to_str(record.net_profit_usd, 2)
            ret.append(record_dict)
        return dict(
            data=ret,
            total=total,
            total_page=page_count,
            has_next=has_next,
        )