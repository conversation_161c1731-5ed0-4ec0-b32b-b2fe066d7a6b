# -*- coding: utf-8 -*-
import datetime
import json
from flask import g
from flask_sqlalchemy import Pagination
from sqlalchemy import func, desc, literal
from webargs import fields

from app import Language
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import <PERSON>umField, PageField, LimitField, DateField
from app.assets import list_all_assets, list_all_chains, chain_to_assets
from app.business.amm import LiquidityService
from app.business.depth import Depth<PERSON>adder
from app.caches import MarketCache, AmmMarketCache
from app.common import ReportType, ADMIN_EXPORT_LIMIT, PrecisionEnum
from app.exceptions import InvalidArgument
from app.models import DailySpotTradeReport, DailyCoinTrade, Decimal, \
    DailySpotTradeMarketReport, Market, DailySpotTradeCoinReport, \
    DailySpotBalanceReport, DailyDepositWithdrawalReport, \
    DailyInnerTransferReport, MonthlyInnerTransferReport, \
    MonthlySpotTradeReport, MonthlyCoinTrade, MonthlySpotTradeMarketReport, \
    MonthlySpotTradeCoinReport, \
    MonthlyDepositWithdrawalReport, DailyLiquidityReport, \
    MonthlyLiquidityReport, DailyMakerTradeReport, \
    MonthlyMakerTradeReport, DailyMakerMarketTradeReport, \
    MonthlyMakerMarketTradeReport, DailySpotDepthReport, MonthlySpotDepthReport, \
    DailyChainDepositWithdrawalReport, MonthlyChainDepositWithdrawalReport, DailyChainAssetDepositWithdrawalReport, \
    MonthlyChainAssetDepositWithdrawalReport
from app.schedules.reports.admin_async_download import async_download_spot_coin_report, \
    async_download_deposit_withdrawal_report, async_download_inner_transfer_report
from app.utils import amount_to_str, current_timestamp, timestamp_to_date, \
    quantize_amount, export_xlsx, format_percent
from app.utils.date_ import datetime_to_time
from app.utils.format import safe_div

ns = Namespace('Report - Spot')


def _format_trading_area_usd_rate(trade_area_usd, total_usd):
    rate = trade_area_usd / total_usd if total_usd else Decimal()
    return '{}/{}%'.format(amount_to_str(trade_area_usd, 2), amount_to_str(rate * 100, 2)) 


@ns.route('/deals')
@respond_with_code
class DailySpotDealsResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "market_count", Language.ZH_HANS_CN: "市场数"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "全站成交额(USD)"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "手续费收入(USD)"},
        {"field": "deal_user_count", Language.ZH_HANS_CN: "成交人数"},
        {"field": "deal_count", Language.ZH_HANS_CN: "成交笔数"},
        {"field": "single_deal_amount", Language.ZH_HANS_CN: "单笔金额"},
        {"field": "increase_trade_user_count", Language.ZH_HANS_CN: "新增交易人数"},
        {"field": "api_user_count", Language.ZH_HANS_CN: "API用户数"},
        {"field": "avg_fee_rate", Language.ZH_HANS_CN: "平均费率"},
        {"field": "avg_mm_fee_rate", Language.ZH_HANS_CN: "做市商平均费率"},
        {"field": "avg_normal_fee_rate", Language.ZH_HANS_CN: "普通用户平均费率"},
        {"field": "normal_fee_rate", Language.ZH_HANS_CN: "普通手续费比例"},
        {"field": "normal_trade_rate", Language.ZH_HANS_CN: "普通成交比例"},
        {"field": "cet_fee_rate", Language.ZH_HANS_CN: "CET收入比例"},
        {"field": "cet_user_rate", Language.ZH_HANS_CN: "CET用户比例"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """报表-现货交易-全站交易"""

        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailySpotTradeReport
        else:
            model = MonthlySpotTradeReport
        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)

        total = 0
        page = 1
        if kwargs['export']:
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            pagination = query.paginate(kwargs['page'], limit, error_out=False)
            items = pagination.items
            total = pagination.total
            page = pagination.page
        records = []
        for item in items:
            report_date = item.report_date.strftime('%Y-%m-%d')
            record = {
                'report_date': report_date,
                'trade_usd': amount_to_str(item.trade_usd, 2),
                'increase_trade_user_count': item.increase_trade_user_count,
                'fee_usd': amount_to_str(item.fee_usd, 2),
                'deal_user_count': item.deal_user_count,
                'deal_count': item.deal_count,
                'avg_fee_rate': amount_to_str(item.avg_fee_rate * 100, 4) + '%',
                'avg_mm_fee_rate': amount_to_str(item.avg_mm_fee_rate * 100,
                                               4) + '%',
                'avg_normal_fee_rate': amount_to_str(
                    item.avg_normal_fee_rate * 100, 4) + '%',
                'normal_fee_rate': amount_to_str(item.normal_fee_rate * 100,
                                               4) + '%',
                'normal_trade_rate': amount_to_str(item.normal_trade_rate * 100,
                                                 4) + '%',
                'cet_fee_rate': amount_to_str(item.cet_fee_rate * 100, 4) + '%',
                'cet_user_rate': amount_to_str(item.cet_user_rate * 100, 4) + '%',
                'market_count': item.market_count,
                'api_user_count': item.api_user_count,
                'single_deal_amount': amount_to_str(safe_div(item.trade_usd, item.deal_count), 2),
            }
            records.append(record)

        if kwargs['export']:
            return export_xlsx(
                filename='spot_site_report',
                data_list=records,
                export_headers=cls.export_headers
            )

        return dict(
            records=records,
            total=total,
            page=page,
        )


@ns.route('/deal-detail')
@respond_with_code
class DailySpotDealDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True),
        order=fields.String(missing='deal_volume_usd'),
    ))
    def get(cls, **kwargs):
        """报表-现货交易-交易区详情"""

        report_date = kwargs.get('report_date')
        order = kwargs['order']

        query = DailyCoinTrade.query
        if not report_date:
            last_item = query.order_by(
                DailyCoinTrade.report_date.desc()).limit(1).first()
            report_date = last_item.report_date if last_item else None
        if report_date:
            query = query.filter(DailyCoinTrade.report_date == report_date)

        if order == 'deal_volume_usd':
            query = query.order_by(DailyCoinTrade.deal_volume_usd.desc())
        elif order == 'deal_user_count':
            query = query.order_by(DailyCoinTrade.deal_user_count.desc())
        elif order == 'deal_count':
            query = query.order_by(DailyCoinTrade.deal_count.desc())
        raw_records = query.filter(
            DailyCoinTrade.report_date == report_date).all()

        total_volume_usd, total_deal_user_set, total_deal_count = Decimal(), set(), 0
        for item in raw_records:
            total_volume_usd += item.deal_volume_usd
            total_deal_user_set |= set(json.loads(item.deal_user_list))
            total_deal_count += int(item.deal_count)
        records = [
            {
                'report_date': '合计',
                'trading_area': 'ALL',
                'deal_volume': '-',
                'deal_volume_usd': amount_to_str(total_volume_usd, 2),
                'deal_user_count': len(total_deal_user_set),
                'deal_count': total_deal_count,
                'deal_volume_usd_rate': '100.00%'
            }
        ]
        for item in raw_records:
            deal_volume_usd_rate = item.deal_volume_usd / total_volume_usd if total_volume_usd else Decimal()
            record = {
                'report_date': item.report_date.strftime('%Y-%m-%d'),
                'trading_area': item.trading_area,
                'deal_volume': '{} {}'.format(amount_to_str(item.deal_volume, 2),
                                              item.trading_area),
                'deal_volume_usd': amount_to_str(item.deal_volume_usd, 2),
                'deal_user_count': item.deal_user_count,
                'deal_count': item.deal_count,
                'deal_volume_usd_rate': '{}%'.format(
                    amount_to_str(deal_volume_usd_rate * 100, 2))
            }
            records.append(record)

        return dict(
            records=records,
        )


@ns.route('/blocks')
@respond_with_code
class DailySpotBlocksResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        order=fields.String(missing='report_date'),
        trading_area=fields.String(missing='BTC'),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-现货交易-交易区"""

        order = kwargs['order']
        trading_area = kwargs['trading_area']
        page = kwargs['page']
        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyCoinTrade
        else:
            model = MonthlyCoinTrade

        query = model.query
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)

        if order == 'report_date':
            query = query.order_by(model.report_date.desc())
        elif order == 'deal_volume_usd':
            query = query.order_by(model.deal_volume_usd.desc())
        elif order == 'deal_user_count':
            query = query.order_by(model.deal_user_count.desc())
        elif order == 'deal_count':
            query = query.order_by(model.deal_count.desc())
        query = query.filter(model.trading_area == trading_area)

        records = []
        if start_date or end_date:
            total_volume_usd, total_volume, total_fee_usd, total_deal_user_set, total_deal_count\
                = Decimal(), Decimal(), Decimal(), set(), 0
            total_normal_deal_volume = Decimal()
            total_normal_fee_usd = Decimal()
            for item in query.all():
                total_volume_usd += item.deal_volume_usd
                total_volume += item.deal_volume
                total_deal_user_set |= set(json.loads(item.deal_user_list))
                total_deal_count += int(item.deal_count)
                total_fee_usd += item.fee_usd
                total_normal_deal_volume += item.normal_deal_volume
                total_normal_fee_usd += item.normal_fee_usd
            total_normal_deal_rate = '0%'
            if total_volume:
                total_normal_deal_rate = format_percent(total_normal_deal_volume / (total_volume * 2), 2)
            total_normal_fee_usd_rate = '0%'
            if total_fee_usd:
                total_normal_fee_usd_rate = format_percent(total_normal_fee_usd / total_fee_usd, 2)
            records.append(
                {
                    'report_date': '合计',
                    'report_date_link': '',
                    'trading_area': trading_area,
                    'deal_volume': '{} {}'.format(amount_to_str(total_volume, 2),
                                                  trading_area),
                    'deal_volume_usd': amount_to_str(total_volume_usd, 2),
                    'deal_user_count': len(total_deal_user_set),
                    'deal_count': total_deal_count,
                    'fee_usd': amount_to_str(total_fee_usd, 2),
                    'normal_deal_rate': total_normal_deal_rate,
                    'normal_fee_usd_rate': total_normal_fee_usd_rate,
                    'single_deal_amount': amount_to_str(safe_div(total_volume_usd, total_deal_count), 2),
                }
            )
        pagination = query.paginate(page, limit, error_out=False)
        for item in pagination.items:
            report_date = item.report_date.strftime('%Y-%m-%d')
            record = {
                'report_date': report_date,
                'trading_area': item.trading_area,
                'deal_volume': '{} {}'.format(amount_to_str(item.deal_volume, 2),
                                              item.trading_area),
                'deal_volume_usd': amount_to_str(item.deal_volume_usd, 2),
                'normal_deal_rate': format_percent(item.normal_deal_rate, 2),
                'normal_fee_usd_rate': format_percent(item.normal_fee_usd_rate, 2),
                'deal_user_count': item.deal_user_count,
                'deal_count': item.deal_count,
                'fee_usd': amount_to_str(item.fee_usd, 2),
                'market_count': item.market_count,
                'single_deal_amount': amount_to_str(safe_div(item.deal_volume_usd, item.deal_count), 2),
            }
            records.append(record)

        return dict(
            records=records,
            total=pagination.total,
        )
    
    
@ns.route('/block-detail')
@respond_with_code
class DailySpotBlockDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True),
        order=fields.String(missing='deal_volume_usd'),
    ))
    def get(cls, **kwargs):
        """报表-现货交易-交易区详情"""

        order = kwargs['order']
        report_date = kwargs['report_date']

        query = DailyCoinTrade.query
        if report_date:
            report_date = report_date
        else:
            last_item = query.order_by(
                DailyCoinTrade.report_date.desc()).limit(1).first()
            report_date = last_item.report_date if last_item else None
        if report_date:
            query = query.filter(DailyCoinTrade.report_date == report_date)

        if order == 'deal_volume_usd':
            query = query.order_by(DailyCoinTrade.deal_volume_usd.desc())
        elif order == 'deal_user_count':
            query = query.order_by(DailyCoinTrade.deal_user_count.desc())
        elif order == 'deal_count':
            query = query.order_by(DailyCoinTrade.deal_count.desc())
        raw_records = query.filter(
            DailyCoinTrade.report_date == report_date).all()

        total_volume_usd, total_deal_user_set, total_deal_count = Decimal(), set(), 0
        for item in raw_records:
            total_volume_usd += item.deal_volume_usd
            total_deal_user_set |= set(json.loads(item.deal_user_list))
            total_deal_count += int(item.deal_count)
        records = [
            {
                'report_date': '合计',
                'trading_area': 'ALL',
                'deal_volume': '-',
                'deal_volume_usd': amount_to_str(total_volume_usd, 2),
                'deal_user_count': len(total_deal_user_set),
                'deal_count': total_deal_count,
                'deal_volume_usd_rate': '100.00%'
            }
        ]
        for item in raw_records:
            deal_volume_usd_rate = item.deal_volume_usd / total_volume_usd if total_volume_usd else Decimal()
            record = {
                'report_date': item.report_date.strftime('%Y-%m-%d'),
                'trading_area': item.trading_area,
                'deal_volume': '{} {}'.format(amount_to_str(item.deal_volume, 2),
                                              item.trading_area),
                'deal_volume_usd': amount_to_str(item.deal_volume_usd, 2),
                'deal_user_count': item.deal_user_count,
                'deal_count': item.deal_count,
                'deal_volume_usd_rate': '{}%'.format(
                    amount_to_str(deal_volume_usd_rate * 100, 2))
            }
            records.append(record)

        return dict(
            records=records
        )


@ns.route('/markets')
@respond_with_code
class DailySpotMarketsResource(Resource):

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "stock_asset", Language.ZH_HANS_CN: "币种"},
        {"field": "market", Language.ZH_HANS_CN: "市场"},
        {"field": "trading_area", Language.ZH_HANS_CN: "交易区"},
        {"field": "trade_amount", Language.ZH_HANS_CN: "成交量"},
        {"field": "trade_volume", Language.ZH_HANS_CN: "成交额"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "成交市值(USD)"},
        {"field": "deal_usd_percent", Language.ZH_HANS_CN: "成交市值占比"},
        {"field": "normal_deal_rate", Language.ZH_HANS_CN: "普通成交比例"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "手续费市值(USD)"},
        {"field": "normal_fee_usd_rate", Language.ZH_HANS_CN: "普通手续费比例"},
        {"field": "deal_user_count", Language.ZH_HANS_CN: "成交人数"},
        {"field": "deal_count", Language.ZH_HANS_CN: "成交笔数"},
        {"field": "single_deal_amount", Language.ZH_HANS_CN: "单笔金额"},
        {"field": "taker_buy_amount", Language.ZH_HANS_CN: "主动买入量"},
        {"field": "taker_buy_count", Language.ZH_HANS_CN: "主动买入笔数"},
        {"field": "taker_sell_amount", Language.ZH_HANS_CN: "主动卖出量"},
        {"field": "taker_sell_count", Language.ZH_HANS_CN: "主动卖出笔数"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        order=fields.String(missing='report_date'),
        market=fields.String(missing='BTCUSDT'),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-现货交易-交易市场"""

        order = kwargs['order']
        market = kwargs['market']
        page = kwargs['page']
        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailySpotTradeMarketReport
        else:
            model = MonthlySpotTradeMarketReport

        query = model.query
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)

        if order == 'report_date':
            query = query.order_by(
                model.report_date.desc())
        elif order == 'deal_volume_usd':
            query = query.order_by(model.trade_usd.desc())
        elif order == 'deal_user_count':
            query = query.order_by(
                model.deal_user_count.desc())
        elif order == 'deal_count':
            query = query.order_by(
                model.deal_count.desc())
        query = query.filter(model.market == market)

        markets_detail = MarketCache.online_markets_detail()

        stock_asset = markets_detail[market]['base_asset']
        quote_asset = markets_detail[market]['quote_asset']

        records = []
        if start_date or end_date:
            total_trade_amount, total_trade_volume, total_trade_usd, total_fee_usd, \
                total_deal_user_set, total_deal_count = Decimal(), Decimal(), Decimal(), Decimal(), set(), 0
            total_taker_buy_amount, total_taker_buy_count, total_taker_sell_amount, total_taker_sell_count \
                = Decimal(), 0, Decimal(), 0
            total_normal_deal_volume = Decimal()
            total_normal_fee_usd = Decimal()
            for item in query.all():
                total_trade_amount += item.trade_amount
                total_trade_volume += item.trade_volume
                total_trade_usd += item.trade_usd
                total_fee_usd += item.fee_usd
                total_deal_user_set |= set(json.loads(item.deal_user_list))
                total_deal_count += int(item.deal_count)
                total_taker_buy_amount += item.taker_buy_amount
                total_taker_buy_count += int(item.taker_buy_count)
                total_taker_sell_amount += item.taker_sell_amount
                total_taker_sell_count += int(item.taker_sell_count)
                total_normal_deal_volume += item.normal_deal_volume
                total_normal_fee_usd += item.normal_fee_usd
            total_normal_deal_rate = '0%'
            if total_trade_volume:
                total_normal_deal_rate = format_percent(total_normal_deal_volume / (total_trade_volume * 2), 2)
            total_normal_fee_usd_rate = '0%'
            if total_fee_usd:
                total_normal_fee_usd_rate = format_percent(total_normal_fee_usd / total_fee_usd, 2)
            records.append(
                {
                    'report_date': '合计',
                    'report_date_link': '',
                    'coin': stock_asset,
                    'market': market,
                    "trading_area": quote_asset,
                    'trade_usd': amount_to_str(total_trade_usd, 2),
                    'fee_usd': amount_to_str(total_fee_usd, 2),
                    'trade_amount': '{} {}'.format(
                        amount_to_str(total_trade_amount, 2), stock_asset),
                    'trade_volume': '{} {}'.format(
                        amount_to_str(total_trade_volume, 2), quote_asset),
                    'deal_user_count': len(total_deal_user_set),
                    'deal_usd_percent': '1',
                    'deal_count': total_deal_count,
                    'taker_buy_amount': '{} {}'.format(
                        amount_to_str(total_taker_buy_amount, 2), stock_asset),
                    'taker_buy_count': total_taker_buy_count,
                    'taker_sell_amount': '{} {}'.format(
                        amount_to_str(total_taker_sell_amount, 2), stock_asset),
                    'taker_sell_count': total_taker_sell_count,
                    'normal_deal_rate': total_normal_deal_rate,
                    'normal_fee_usd_rate': total_normal_fee_usd_rate,
                    'single_deal_amount': amount_to_str(safe_div(total_trade_usd, total_deal_count), 2),
                }
            )

        if kwargs['export']:
            items = list(query.limit(ADMIN_EXPORT_LIMIT).all())
        else:
            pagination = query.paginate(page, limit, error_out=False)
            items = list(pagination.items)
            total = pagination.total
            page = pagination.page

        if kwargs['export']:
            data_list = [i.to_dict() for i in items]
            for item in data_list:
                item["trading_area"] = markets_detail[item["market"]]["quote_asset"]
                item["normal_deal_rate"] = format_percent(item["normal_deal_rate"], 2)
                item["normal_fee_usd_rate"] = format_percent(item["normal_fee_usd_rate"], 2)
                item["deal_usd_percent"] = format_percent(item["deal_usd_percent"], 2)
                item["single_deal_amount"] = amount_to_str(safe_div(item["trade_usd"], item["deal_count"]))
            return export_xlsx(
                filename='spot_market_report',
                data_list=data_list,
                export_headers=cls.export_headers
            )


        for item in items:
            report_date = item.report_date.strftime('%Y-%m-%d') \
                if kwargs['report_type'] == ReportType.DAILY else item.report_date.strftime('%Y-%m')
            quote_asset = markets_detail[item.market]['quote_asset']
            record = {
                'report_date': report_date,
                'coin': item.stock_asset,
                'market': item.market,
                "trading_area": quote_asset,
                'trade_usd': amount_to_str(item.trade_usd, 2),
                'fee_usd': amount_to_str(item.fee_usd, 2),
                'trade_amount': '{} {}'.format(
                    amount_to_str(item.trade_amount, 2), item.stock_asset),
                'trade_volume': '{} {}'.format(
                    amount_to_str(item.trade_volume, 2), quote_asset),
                'deal_user_count': item.deal_user_count,
                'deal_usd_percent': item.deal_usd_percent,
                'deal_count': item.deal_count,
                'taker_buy_amount': '{} {}'.format(
                    amount_to_str(item.taker_buy_amount, 2), item.stock_asset),
                'taker_buy_count': item.taker_buy_count,
                'taker_sell_amount': '{} {}'.format(
                    amount_to_str(item.taker_sell_amount, 2), item.stock_asset),
                'taker_sell_count': item.taker_sell_count,
                'normal_deal_rate': format_percent(item.normal_deal_rate, 2),
                'normal_fee_usd_rate': format_percent(item.normal_fee_usd_rate, 2),
                'single_deal_amount': amount_to_str(safe_div(item.trade_usd, item.deal_count), 2),
            }
            records.append(record)
        return dict(
            records=records,
            market_list=sorted(markets_detail.keys()),
            total=total,
        )


@ns.route('/market-detail')
@respond_with_code
class DailySpotMarketDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True, missing=''),
        order=fields.String(missing='deal_volume_usd'),
        trading_area=fields.String(missing=''),
        report_type=EnumField(ReportType, required=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-现货交易-交易市场详情"""

        order = kwargs['order']
        report_date = kwargs['report_date']
        trading_area = kwargs['trading_area']
        page = kwargs['page']
        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailySpotTradeMarketReport
        else:
            model = MonthlySpotTradeMarketReport

        query = model.query
        if not report_date:
            last_item = query.order_by(
                model.report_date.desc()).limit(1).first()
            report_date = last_item.report_date.strftime(
                '%Y-%m-%d') if last_item else None
        if report_date:
            if kwargs['report_type'] == ReportType.MONTHLY:
                report_date = datetime.date(report_date.year, report_date.month, 1)
            query = query.filter(
                model.report_date == report_date)

        if trading_area:

            market_info = MarketCache.online_markets_detail()
            markets = [r['name'] for r in market_info.values()
                       if r['trading_area'].value == trading_area]
            query = query.filter(
                model.market.in_(markets))

        if order == 'deal_volume_usd':
            query = query.order_by(model.trade_usd.desc())
        elif order == 'deal_user_count':
            query = query.order_by(
                model.deal_user_count.desc())
        elif order == 'deal_count':
            query = query.order_by(
                model.deal_count.desc())
        query = query.filter(
            model.report_date == report_date)

        total_trade_usd, total_deal_user_set, total_deal_count = Decimal(), set(), 0
        total_taker_buy_count, total_taker_sell_count = 0, 0
        total_fee_usd = Decimal()
        for item in query.all():
            total_trade_usd += item.trade_usd
            total_fee_usd += item.fee_usd
            total_deal_user_set |= set(json.loads(item.deal_user_list))
            total_deal_count += int(item.deal_count)
            total_taker_buy_count += int(item.taker_buy_count)
            total_taker_sell_count += int(item.taker_sell_count)
        records = [
            {
                'report_date': '合计',
                'coin': 'ALL',
                'market': 'ALL',
                'trade_usd': amount_to_str(total_trade_usd, 2),
                'deal_usd_percent': '1',
                'fee_usd': amount_to_str(total_fee_usd, 2),
                'trade_amount': '-',
                'deal_user_count': len(total_deal_user_set),
                'deal_count': total_deal_count,
                'taker_buy_amount': '-',
                'taker_buy_count': total_taker_buy_count,
                'taker_sell_amount': '-',
                'taker_sell_count': total_taker_sell_count,
                'single_deal_amount': amount_to_str(safe_div(total_trade_usd, total_deal_count), 2),
            }
        ]

        pagination = query.paginate(page, limit, error_out=False)
        for item in pagination.items:
            report_date = item.report_date.strftime('%Y-%m-%d') \
                if kwargs['report_type'] == ReportType.DAILY else item.report_date.strftime('%Y-%m')
            record = {
                'report_date': report_date,
                'coin': item.stock_asset,
                'market': item.market,
                'trade_amount': '{} {}'.format(
                    amount_to_str(item.trade_amount, 2), item.stock_asset),
                'trade_usd': amount_to_str(item.trade_usd, 2),
                'deal_user_count': item.deal_user_count,
                'deal_usd_percent': item.deal_usd_percent,
                'fee_usd': amount_to_str(item.fee_usd, PrecisionEnum.CASH_PLACES),
                'deal_count': item.deal_count,
                'taker_buy_amount': '{} {}'.format(
                    amount_to_str(item.taker_buy_amount, 2), item.stock_asset),
                'taker_buy_count': item.taker_buy_count,
                'taker_sell_amount': '{} {}'.format(
                    amount_to_str(item.taker_sell_amount, 2), item.stock_asset),
                'taker_sell_count': item.taker_sell_count,
                'single_deal_amount': amount_to_str(safe_div(item.trade_usd, item.deal_count), 2),
            }
            records.append(record)

        return dict(
            records=records,
            trading_areas=Market.TradingArea,
            total=pagination.total,
            page=pagination.page,
        )


@ns.route('/coin')
@respond_with_code
class DailySpotCoinResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "coin", Language.ZH_HANS_CN: "币种"},
        {"field": "trade_amount", Language.ZH_HANS_CN: "成交量"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "成交市值(USD)"},
        {"field": "normal_deal_rate", Language.ZH_HANS_CN: "普通成交比例"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "手续费市值(USD)"},
        {"field": "normal_fee_usd_rate", Language.ZH_HANS_CN: "普通手续费比例"},
        {"field": "deal_user_count", Language.ZH_HANS_CN: "成交人数"},
        {"field": "deal_count", Language.ZH_HANS_CN: "成交笔数"},
        {"field": "single_deal_amount", Language.ZH_HANS_CN: "单笔金额"},
        {"field": "taker_buy_amount", Language.ZH_HANS_CN: "主动买入量"},
        {"field": "taker_buy_count", Language.ZH_HANS_CN: "主动买入笔数"},
        {"field": "taker_sell_amount", Language.ZH_HANS_CN: "主动卖出量"},
        {"field": "taker_sell_count", Language.ZH_HANS_CN: "主动卖出笔数"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        order=fields.String(missing='report_date'),
        coin=fields.String(missing='BTC'),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-现货交易-交易币种"""

        order = kwargs['order']
        coin = kwargs['coin']
        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailySpotTradeCoinReport
        else:
            model = MonthlySpotTradeCoinReport

        query = model.query

        if order == 'report_date':
            query = query.order_by(model.report_date.desc())
        elif order == 'deal_volume_usd':
            query = query.order_by(model.trade_usd.desc())
        elif order == 'deal_user_count':
            query = query.order_by(
                model.deal_user_count.desc())
        elif order == 'deal_count':
            query = query.order_by(model.deal_count.desc())

        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)

        query = query.filter(model.coin == coin)
        records = []
        if start_date or end_date:
            total_trade_amount, total_trade_usd, total_fee_usd, total_deal_user_set, total_deal_count \
                = Decimal(), Decimal(), Decimal(), set(), 0
            total_taker_buy_amount, total_taker_buy_count, total_taker_sell_amount, total_taker_sell_count \
                = Decimal(), 0, Decimal(), 0
            total_normal_deal_volume_usd = Decimal()
            total_normal_fee_usd = Decimal()
            for item in query.all():
                total_trade_amount += item.trade_amount
                total_trade_usd += item.trade_usd
                total_fee_usd += item.fee_usd
                total_deal_user_set |= set(json.loads(item.deal_user_list))
                total_deal_count += int(item.deal_count)
                total_taker_buy_amount += item.taker_buy_amount
                total_taker_buy_count += int(item.taker_buy_count)
                total_taker_sell_amount += item.taker_sell_amount
                total_taker_sell_count += int(item.taker_sell_count)
                total_normal_deal_volume_usd += item.normal_deal_volume_usd
                total_normal_fee_usd += item.normal_fee_usd
            total_normal_deal_rate = '0%'
            if total_trade_usd:
                total_normal_deal_rate = format_percent(total_normal_deal_volume_usd / (total_trade_usd * 2), 2)
            total_normal_fee_usd_rate = '0%'
            if total_fee_usd:
                total_normal_fee_usd_rate = format_percent(total_normal_fee_usd / total_fee_usd, 2)
            records.append(
                {
                    'report_date': '合计',
                    'report_date_link': '',
                    'coin': coin,
                    'trade_usd': amount_to_str(total_trade_usd, 2),
                    'fee_usd': amount_to_str(total_fee_usd, 2),
                    'trade_amount': '{} {}'.format(
                        amount_to_str(total_trade_amount, 2), coin),
                    'deal_user_count': len(total_deal_user_set),
                    'deal_count': total_deal_count,
                    'taker_buy_amount': '{} {}'.format(
                        amount_to_str(total_taker_buy_amount, 2), coin),
                    'taker_buy_count': total_taker_buy_count,
                    'taker_sell_amount': '{} {}'.format(
                        amount_to_str(total_taker_sell_amount, 2), coin),
                    'taker_sell_count': total_taker_sell_count,
                    'normal_deal_rate': total_normal_deal_rate,
                    'normal_fee_usd_rate': total_normal_fee_usd_rate,
                    'single_deal_amount': amount_to_str(safe_div(total_trade_usd, total_deal_count), 2),
                }
            )
        total = 0
        if kwargs['export']:
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            pagination = query.paginate(kwargs['page'], limit, error_out=False)
            items = pagination.items
            total = pagination.total
        for item in items:
            report_date = item.report_date.strftime('%Y-%m-%d') \
                if kwargs['report_type'] == ReportType.DAILY else item.report_date.strftime('%Y-%m')
            record = {
                'report_date': report_date,
                'coin': item.coin,
                'trade_usd': amount_to_str(item.trade_usd, 2),
                'fee_usd': amount_to_str(item.fee_usd, 2),
                'trade_amount': '{} {}'.format(
                    amount_to_str(item.trade_amount, 2), item.coin),
                'deal_user_count': item.deal_user_count,
                'deal_count': item.deal_count,
                'taker_buy_amount': '{} {}'.format(
                    amount_to_str(item.taker_buy_amount, 2), item.coin),
                'taker_buy_count': item.taker_buy_count,
                'taker_sell_amount': '{} {}'.format(
                    amount_to_str(item.taker_sell_amount, 2), item.coin),
                'taker_sell_count': item.taker_sell_count,
                'normal_deal_rate': format_percent(item.normal_deal_rate, 2),
                'normal_fee_usd_rate': format_percent(item.normal_fee_usd_rate, 2),
                'single_deal_amount': amount_to_str(safe_div(item.trade_usd, item.deal_count), 2),
            }
            records.append(record)

        if kwargs['export']:
            return export_xlsx(
                filename='spot_coin',
                data_list=cls.get_export_data(records),
                export_headers=cls.export_headers
            )

        return dict(
            records=records,
            coin_list=list_all_assets(),
            total=total,
        )

    @classmethod
    def get_export_data(cls, records):
        return records


@ns.route('/coin-async-download')
@respond_with_code
class DailySpotCoinAsyncDownloadResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_time=DateField(required=True),
        end_time=DateField(required=True),
    ))
    def get(cls, **kwargs):
        """报表-现货交易-交易币种-异步下载"""
        start_date = kwargs['start_time'].date()
        end_date = kwargs['end_time'].date()
        if start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if (end_date - start_date).days > 180:
            raise InvalidArgument(message='时间范围不能超过 180天')

        async_download_spot_coin_report.delay(
            email=g.user.email,
            is_daily=kwargs['report_type'] is ReportType.DAILY,
            start_ts=datetime_to_time(start_date),
            end_ts=datetime_to_time(end_date),
        )

    
@ns.route('/coin-detail')
@respond_with_code
class DailySpotCoinDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True, missing=''),
        order=fields.String(missing='deal_volume_usd'),
        report_type=EnumField(ReportType, required=True),
    ))
    def get(cls, **kwargs):
        """报表-现货交易-交易币种"""

        order = kwargs['order']
        report_date = kwargs['report_date']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailySpotTradeCoinReport
        else:
            model = MonthlySpotTradeCoinReport

        query = model.query
        if not report_date:
            last_item = query.order_by(
                model.report_date.desc()).limit(1).first()
            report_date = last_item.report_date.strftime(
                '%Y-%m-%d') if last_item else None
        if report_date:
            if kwargs['report_type'] == ReportType.MONTHLY:
                report_date = datetime.date(report_date.year, report_date.month, 1)
            query = query.filter(
                model.report_date == report_date)

        if order == 'deal_volume_usd':
            query = query.order_by(model.trade_usd.desc())
        elif order == 'deal_user_count':
            query = query.order_by(
                model.deal_user_count.desc())
        elif order == 'deal_count':
            query = query.order_by(model.deal_count.desc())
        elif order == 'fee_usd':
            query = query.order_by(model.fee_usd.desc())
        query = query.filter(
            model.report_date == report_date)

        total_fee_usd, total_trade_usd, total_deal_user_set, total_deal_count = Decimal(), Decimal(), set(), 0
        total_taker_buy_count, total_taker_sell_count = 0, 0
        records = []
        for item in query.all():
            total_trade_usd += item.trade_usd
            total_fee_usd += item.fee_usd
            total_deal_user_set |= set(json.loads(item.deal_user_list))
            total_deal_count += int(item.deal_count)
            total_taker_buy_count += int(item.taker_buy_count)
            total_taker_sell_count += int(item.taker_sell_count)
            report_date = item.report_date.strftime('%Y-%m-%d') \
                if kwargs['report_type'] == ReportType.DAILY else item.report_date.strftime('%Y-%m')
            record = {
                'report_date': report_date,
                'coin': item.coin,
                'trade_amount': '{} {}'.format(
                    amount_to_str(item.trade_amount, 2), item.coin),
                'trade_usd': amount_to_str(item.trade_usd, 2),
                'fee_usd': amount_to_str(item.fee_usd, 2),
                'deal_user_count': item.deal_user_count,
                'deal_count': item.deal_count,
                'taker_buy_amount': '{} {}'.format(
                    amount_to_str(item.taker_buy_amount, 2), item.coin),
                'taker_buy_count': item.taker_buy_count,
                'taker_sell_amount': '{} {}'.format(
                    amount_to_str(item.taker_sell_amount, 2), item.coin),
                'taker_sell_count': item.taker_sell_count,
                'single_deal_amount': amount_to_str(safe_div(item.trade_usd, item.deal_count), 2),
            }
            records.append(record)
        total_record = {
            'report_date': '合计',
            'coin': 'ALL',
            'trade_usd': amount_to_str(total_trade_usd, 2),
            'fee_usd': amount_to_str(total_fee_usd, 2),
            'trade_amount': '-',
            'deal_user_count': len(total_deal_user_set),
            'deal_count': total_deal_count,
            'taker_buy_amount': '-',
            'taker_buy_count': total_taker_buy_count,
            'taker_sell_amount': '-',
            'taker_sell_count': total_taker_sell_count,
            'single_deal_amount': amount_to_str(safe_div(total_trade_usd, total_deal_count), 2),
        }
        records.insert(0, total_record)

        return dict(
            records=records
        )


@ns.route('/balance')
@respond_with_code
class DailySpotBalanceResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        query_date=DateField(to_date=True),
        asset=fields.String(),
    ))
    def get(cls, **kwargs):
        """报表-资产报表-币币"""
        yesterday = timestamp_to_date(current_timestamp() - 86400)
        query_date = kwargs.get('query_date') or yesterday

        query = DailySpotBalanceReport.query.filter(
            DailySpotBalanceReport.report_date == query_date,
        )

        if kwargs.get('asset'):
            query = query.filter(
                DailySpotBalanceReport.asset == kwargs['asset'])

        last_update_time = DailySpotBalanceReport.query.order_by(
            DailySpotBalanceReport.report_date.desc()).first().report_date

        records = []

        for record in query.all():
            tmp = record.to_dict()
            tmp['amount'] = amount_to_str(tmp['amount'], 2)
            records.append(tmp)

        return dict(
            records=records,
            levels=DailySpotBalanceReport.LEVEL_TABLE,
            asset_list=list_all_assets(),
            last_update_time=last_update_time,
        )


@ns.route('/asset-balance')
@respond_with_code
class DailySpotAssetBalanceResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField,
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """报表-资产报表-币币-币种详情"""
        yesterday = timestamp_to_date(current_timestamp() - 86400)
        asset = kwargs.get('asset')
        page, limit = kwargs['page'], kwargs['limit']

        end_date = kwargs.get('end_date') or yesterday
        query = DailySpotBalanceReport.query
        query = query.filter(
            DailySpotBalanceReport.report_date <= end_date)
        if kwargs.get('start_date'):
            query = query.filter(
                DailySpotBalanceReport.report_date >= kwargs[
                    'start_date'])
        if asset:
            query = query.filter(DailySpotBalanceReport.asset == asset)
        records = query.order_by(
            DailySpotBalanceReport.report_date.desc()
        ).paginate(page, limit)

        format_records = []

        for record in records.items:
            tmp = record.to_dict()
            tmp['amount'] = amount_to_str(tmp['amount'], 2)
            format_records.append(tmp)

        return dict(
            records=format_records,
            total=records.total,
            levels=DailySpotBalanceReport.LEVEL_TABLE,
            asset_list=list_all_assets(),
        )


@ns.route('/deposit-withdrawal')
@respond_with_code
class DepositWithdrawalResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            report_type=EnumField(ReportType, enum_by_value=True, required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """报表-现货交易-充提报表"""
        asset = kwargs.get('asset')
        if not asset:
            # 空字符串: 全站数据
            asset = ""
        page, limit = kwargs['page'], kwargs['limit']
        report_type = kwargs['report_type']
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")

        if report_type == ReportType.DAILY:
            report_model = DailyDepositWithdrawalReport
        else:
            report_model = MonthlyDepositWithdrawalReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)

        query = report_model.query.filter(report_model.asset == asset)
        if start_date:
            query = query.filter(report_model.report_date >= start_date)
        if end_date:
            query = query.filter(report_model.report_date <= end_date)
        records = query.order_by(report_model.report_date.desc()).paginate(page, limit)

        return dict(
            items=records.items,
            assets=list_all_assets(),
            total=records.total,
        )


@ns.route('/deposit-withdrawal-async-download')
@respond_with_code
class DepositWithdrawalAsyncDownloadResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            start_time=DateField(required=True),
            end_time=DateField(required=True),
            report_type=EnumField(ReportType, enum_by_value=True, required=True),
        )
    )
    def get(cls, **kwargs):
        """报表-现货交易-充提报表-异步下载"""
        start_date = kwargs['start_time'].date()
        end_date = kwargs['end_time'].date()
        if start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if (end_date - start_date).days > 180:
            raise InvalidArgument(message='时间范围不能超过 180天')

        async_download_deposit_withdrawal_report.delay(
            email=g.user.email,
            is_daily=kwargs['report_type'] is ReportType.DAILY,
            is_viabtc=False,
            start_ts=datetime_to_time(start_date),
            end_ts=datetime_to_time(end_date),
        )


@ns.route('/deposit-withdrawal-local-async-download')
@respond_with_code
class DepositWithdrawalLocalAsyncDownloadResource(DepositWithdrawalAsyncDownloadResource):

    @classmethod
    @ns.use_kwargs(
        dict(
            start_time=DateField(required=True),
            end_time=DateField(required=True),
            report_type=EnumField(ReportType, enum_by_value=True, required=True),
        )
    )
    def get(cls, **kwargs):
        """报表-现货交易-Viabtc充值报表-币种-异步下载"""
        start_date = kwargs['start_time'].date()
        end_date = kwargs['end_time'].date()
        if start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if (end_date - start_date).days > 180:
            raise InvalidArgument(message='时间范围不能超过 180天')

        async_download_deposit_withdrawal_report.delay(
            email=g.user.email,
            is_daily=kwargs['report_type'] is ReportType.DAILY,
            is_viabtc=True,
            start_ts=datetime_to_time(start_date),
            end_ts=datetime_to_time(end_date),
        )


@ns.route('/chain-deposit-withdrawal')
@respond_with_code
class ChainDepositWithdrawalResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            chain=fields.String(missing=''),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            report_type=EnumField(ReportType, enum_by_value=True, required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """报表-现货交易-公链充提报表"""
        chain = kwargs['chain']
        page, limit = kwargs['page'], kwargs['limit']
        report_type = kwargs['report_type']
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")

        if report_type == ReportType.DAILY:
            report_model = DailyChainDepositWithdrawalReport
        else:
            report_model = MonthlyChainDepositWithdrawalReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)

        query = report_model.query.filter(report_model.chain == chain)
        if start_date:
            query = query.filter(report_model.report_date >= start_date)
        if end_date:
            query = query.filter(report_model.report_date <= end_date)
        records = query.order_by(report_model.report_date.desc())\
            .paginate(page, limit)
        items = _fmt(records.items)

        return dict(
            items=items,
            chains=list_all_chains(),
            total=records.total,
        )


def _fmt(items: list[DailyChainDepositWithdrawalReport]):
    res = []
    for item in items:
        item_dic = item.to_dict()
        token_deposit_count = item.token_deposit_count
        token_withdrawal_count = item.token_withdrawal_count
        main_asset_deposit_count = item.main_asset_deposit_count
        main_asset_withdrawal_count = item.main_asset_withdrawal_count
        deposit_count = token_deposit_count + main_asset_deposit_count
        withdrawal_count =\
            token_withdrawal_count + main_asset_withdrawal_count
        token_deposit_percent =\
            token_deposit_count / deposit_count if deposit_count else 0
        main_asset_deposit_percent =\
            main_asset_deposit_count / deposit_count if deposit_count else 0
        token_withdrawal_percent = \
            token_withdrawal_count / withdrawal_count if withdrawal_count else 0
        main_asset_withdrawal_percent = \
            main_asset_withdrawal_count / withdrawal_count if withdrawal_count else 0
        deposit_detail = \
            'token笔数:{},占比:{:.2%};原生笔数:{},占比:{:.2%}'.format(
                token_deposit_count, token_deposit_percent,
                main_asset_deposit_count, main_asset_deposit_percent)
        withdrawal_detail = \
            'token笔数:{},占比:{:.2%};原生笔数:{},占比:{:.2%}'.format(
                token_withdrawal_count, token_withdrawal_percent,
                main_asset_withdrawal_count, main_asset_withdrawal_percent)

        item_dic['deposit_count'] = deposit_count
        item_dic['withdrawal_count'] = withdrawal_count
        item_dic['deposit_detail'] = deposit_detail
        item_dic['withdrawal_detail'] = withdrawal_detail
        item_dic['gas_asset_amount_display'] =\
            '{}{}'.format(amount_to_str(item.gas_asset_amount), item.gas_asset
                          ) if item.gas_asset_amount and item.gas_asset else '0'
        res.append(item_dic)
    return res


@ns.route('/deposit-withdrawal-chain-detail')
@respond_with_code
class DepositWithdrawalChainDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True),
        report_type=EnumField(['daily', 'monthly'], missing='daily'),
        sort_name=EnumField(
            ['deposit_user_count', 'withdrawal_user_count', 'deposit_count',
             'withdrawal_count', 'gas_fee', 'tx_count', 'withdrawal_user_fee'
             ], missing='tx_count'),
        page=PageField,
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """报表-现货交易-充提报表-公链详情"""
        yesterday = timestamp_to_date(current_timestamp() - 86400)
        page, limit = kwargs['page'], kwargs['limit']
        report_type = kwargs['report_type']
        sort_name = kwargs['sort_name']
        report_date = kwargs.get('report_date') or yesterday

        query_model = DailyChainDepositWithdrawalReport \
            if report_type == 'daily' else MonthlyChainDepositWithdrawalReport
        query = query_model.query
        query = query.filter(
            query_model.report_date == report_date,
            query_model.chain != '',
        )
        if sort_name not in [
            'deposit_count',
            'withdrawal_count',
        ]:
            records = query.order_by(desc(sort_name)).paginate(page, limit)
        else:
            if sort_name == 'deposit_count':
                records = query.order_by(
                    (query_model.token_deposit_count + query_model.main_asset_deposit_count).desc()
                ).paginate(page, limit)
            else:
                records = query.order_by(
                    (query_model.token_withdrawal_count + query_model.main_asset_withdrawal_count).desc()
                ).paginate(page, limit)
        return dict(
            items=_fmt(records.items),
            chains=list_all_chains(),
            total=records.total,
        )


@ns.route('/chain-asset-deposit-withdrawal')
@respond_with_code
class ChainAssetDepositWithdrawalResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            report_type=EnumField(ReportType, enum_by_value=True, required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """报表-充提报表-链上充提币种报表"""
        asset, chain = kwargs['asset'].split("-")
        page, limit = kwargs['page'], kwargs['limit']
        report_type = kwargs['report_type']
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")

        if report_type == ReportType.DAILY:
            report_model = DailyChainAssetDepositWithdrawalReport
        else:
            report_model = MonthlyChainAssetDepositWithdrawalReport

        query = report_model.query.filter(
            report_model.chain == chain,
            report_model.asset == asset
        )
        if start_date and end_date and start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if start_date:
            query = query.filter(report_model.report_date >= start_date)
        if end_date:
            query = query.filter(report_model.report_date <= end_date)
        records = query.order_by(report_model.report_date.desc()).paginate(page, limit)
        items = cls._fmt(records.items)
        return dict(
            items=items,
            asset_list=cls.get_assets(),
            total=records.total,
        )

    @classmethod
    def _fmt(cls, items: list):
        res = []
        for item in items:
            item_dic = item.to_dict()
            item_dic['asset'] = f'{item_dic["asset"]}-{item_dic["chain"]}'
            item_dic['deposit_usd'] = amount_to_str(item_dic['deposit_usd'], 2)
            item_dic['withdrawal_usd'] = amount_to_str(item_dic['withdrawal_usd'], 2)
            item_dic['gas_fee'] = amount_to_str(item_dic['gas_fee'], 2)
            item_dic['withdrawal_user_fee'] = amount_to_str(item_dic['withdrawal_user_fee'], 2)

            res.append(item_dic)
        return res

    @classmethod
    def get_assets(cls):
        res = []
        for chain, assets in chain_to_assets().items():
            for asset in assets:
                res.append(f'{asset}-{chain}')
        return res


@ns.route('/deposit-withdrawal-chain-asset-detail')
@respond_with_code
class DepositWithdrawalChainAssetDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True, required=True),
        report_type=EnumField(['daily', 'monthly'], missing='daily'),
        sort_name=EnumField(
            ['deposit_user_count', 'withdrawal_user_count', 'deposit_count',
             'withdrawal_count', 'gas_fee', 'tx_count', 'withdrawal_user_fee'
             ], missing='tx_count'),
        page=PageField,
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """报表-充提报表-链上充提币种报表-币种详情"""
        page, limit = kwargs['page'], kwargs['limit']
        report_date = kwargs['report_date']
        report_type = kwargs['report_type']
        sort_name = kwargs['sort_name']

        model = DailyChainAssetDepositWithdrawalReport if report_type == 'daily' else MonthlyChainAssetDepositWithdrawalReport
        if report_type == 'monthly':
            report_date = datetime.date(report_date.year, report_date.month, 1)
        query = model.query.filter(
            model.report_date == report_date,
        )
        records = query.order_by(desc(sort_name)).paginate(page, limit)

        return dict(
            items=cls._fmt(records.items),
            total=records.total,
        )

    @classmethod
    def _fmt(cls, items: list):
        res = []
        for item in items:
            item_dic = item.to_dict()
            item_dic['asset'] = f'{item_dic["asset"]}-{item_dic["chain"]}'
            item_dic['deposit_usd'] = amount_to_str(item_dic['deposit_usd'], 2)
            item_dic['withdrawal_usd'] = amount_to_str(item_dic['withdrawal_usd'], 2)
            item_dic['gas_fee'] = amount_to_str(item_dic['gas_fee'], 2)
            item_dic['withdrawal_user_fee'] = amount_to_str(item_dic['withdrawal_user_fee'], 2)
            res.append(item_dic)
        return res


@ns.route('/deposit-withdrawal-asset-detail')
@respond_with_code
class DepositWithdrawalAssetDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        asset=fields.String(),
        report_date=DateField(to_date=True),
        report_type=EnumField(['daily', 'monthly'], missing='daily'),
        sort_name=EnumField(
            ['deposit_user_count', 'withdrawal_user_count', 'deposit_usd',
             'withdrawal_usd', 'net_retain_usd', 'local_deposit_user_count',
             'local_deposit_usd'
             ], missing='net_retain_usd'),
        page=PageField,
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """报表-现货交易-充提报表-币种详情"""
        yesterday = timestamp_to_date(current_timestamp() - 86400)
        page, limit = kwargs['page'], kwargs['limit']
        report_type = kwargs['report_type']
        sort_name = kwargs['sort_name']
        report_date = kwargs.get('report_date') or yesterday

        query_model = DailyDepositWithdrawalReport if report_type == 'daily' else MonthlyDepositWithdrawalReport
        query = query_model.query
        query = query.filter(
            query_model.report_date == report_date,
            query_model.asset != '',
        )
        if sort_name == 'net_retain_usd':
            query = query.order_by(desc(query_model.deposit_usd-query_model.withdrawal_usd))
        else:
            query = query.order_by(desc(sort_name))

        records = query.paginate(page, limit)

        return dict(
            items=records.items,
            assets=list_all_assets(),
            total=records.total,
        )


@ns.route('/inner-transfer')
@respond_with_code
class InnerTransferResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(),
            report_type=EnumField(ReportType, required=True),
            start_date=DateField(to_date=True),
            end_date=DateField(to_date=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """报表-内部转账"""
        page, limit = kwargs["page"], kwargs["limit"]
        start_date, end_date = kwargs.get("start_date"), kwargs.get("end_date")
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyInnerTransferReport
        else:
            model = MonthlyInnerTransferReport
            if start_date:
                start_date = start_date.replace(day=1)
            if end_date:
                end_date = end_date.replace(day=1)

        query = model.query
        if start_date:
            query = query.filter(model.report_date >= start_date)
        if end_date:
            query = query.filter(model.report_date <= end_date)
        if (asset := kwargs.get("asset")) is not None:
            # 空字符串时为全站数据
            query = query.filter(model.asset == asset)
        records = query.order_by(model.report_date.desc()).paginate(page, limit)

        return dict(
            records=records.items,
            assets=list_all_assets(),
            total=records.total,
        )


@ns.route('/inner-transfer-async-download')
@respond_with_code
class InnerTransferAsyncDownloadResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_time=DateField(required=True),
        end_time=DateField(required=True),
    ))
    def get(cls, **kwargs):
        """报表-内部转账-异步下载"""
        start_date = kwargs['start_time'].date()
        end_date = kwargs['end_time'].date()
        if start_date > end_date:
            raise InvalidArgument(message='开始时间需小于结束时间')
        if (end_date - start_date).days > 180:
            raise InvalidArgument(message='时间范围不能超过 180天')

        async_download_inner_transfer_report.delay(
            email=g.user.email,
            is_daily=kwargs['report_type'] is ReportType.DAILY,
            start_ts=datetime_to_time(start_date),
            end_ts=datetime_to_time(end_date),
        )


@ns.route('/inner-asset-transfer')
@respond_with_code
class InnerAssetTransferResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        sort_name=fields.String(missing='report_date'),
        report_date=DateField(to_date=True, required=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        report_type=EnumField(ReportType, required=True),
    ))
    def get(cls, **kwargs):
        """报表-币种内部转账"""
        sort_name = kwargs['sort_name']
        report_date = kwargs['report_date']
        page = kwargs['page']
        limit = kwargs['limit']
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyInnerTransferReport
        else:
            report_date = report_date.replace(day=1)
            model = MonthlyInnerTransferReport
        query = model.query.filter(model.asset != '')
        query = query.filter(model.report_date == report_date)
        records = query.order_by(desc(sort_name)).paginate(page, limit)

        return dict(
            records=records.items,
            page=records.page,
            total=records.total,
            assets=list_all_assets(),
        )


@ns.route('/amm-market')
@respond_with_code
class AmmMarketResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(),
        report_date=DateField(to_date=True),
        page=PageField(unlimited=True, missing=1),
        limit=LimitField(missing=50),
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
    ))
    def get(cls, **kwargs):
        """
        报表-现货报表-AMM市场
        """

        page, limit = kwargs['page'], kwargs['limit']
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyLiquidityReport
        else:
            model = MonthlyLiquidityReport
        query = model.query
        if report_date := kwargs.get('report_date'):
            query = query.filter(
                model.report_date == report_date
            )
        if market := kwargs.get('market'):
            query = query.filter(
                model.market == market
            )
        total_amount = query.with_entities(
            func.sum(model.added_liquidity_usd).label('added_liquidity_usd'),
            func.sum(model.removed_liquidity_usd).label('removed_liquidity_usd'),
            func.sum(model.added_user_count).label('added_user_count'),
            func.sum(model.removed_user_count).label(
                'removed_user_count'),
            func.sum(model.added_count).label('added_count'),
            func.sum(model.removed_count).label(
                'removed_count'),
        ).first()
        # ALL总和记录
        total_amount_record = dict(
            added_liquidity_usd=quantize_amount(total_amount.added_liquidity_usd or Decimal(), 2),
            removed_liquidity_usd=quantize_amount(total_amount.removed_liquidity_usd or Decimal(), 2),
            added_user_count=total_amount.added_user_count or 0,
            removed_user_count=total_amount.removed_user_count or 0,
            added_count=total_amount.added_count or 0,
            removed_count=total_amount.removed_count or 0,
            market='所有',
            all=True
        )
        total_amount_record['increased_usd'] = \
            total_amount_record['added_liquidity_usd'] - total_amount_record['removed_liquidity_usd']
        query = query.order_by(model.id.desc())
        records = query.paginate(page, limit, error_out=False)
        markets = AmmMarketCache.list_amm_markets()
        market_asset_map = dict()
        for market in markets:
            srv = LiquidityService(market)
            market_asset_map[market] = srv.market['base_asset'], srv.market['quote_asset']
        res = []
        for record in records.items:
            record = record.to_dict()
            record['base_asset'], record['quote_asset'] = market_asset_map[record['market']]
            record['increased_usd'] = \
                record['added_liquidity_usd'] - record['removed_liquidity_usd']
            record['increased_usd'] = quantize_amount(record['increased_usd'], 2)
            record['increased_base_amount'] = \
                record['added_base_amount'] - record['removed_base_amount']
            record['increased_quote_amount'] = \
                record['added_quote_amount'] - record['removed_quote_amount']
            record['added_liquidity_usd'] = quantize_amount(
                record['added_liquidity_usd'], 2)
            record['removed_liquidity_usd'] = quantize_amount(
                record['removed_liquidity_usd'], 2)

            res.append(record)
        return dict(
            total=records.total,
            items=[total_amount_record, ] + res,
            report_type=ReportType,
            markets=markets
        )


@ns.route('/maker-deals')
@respond_with_code
class DailySpotMakerDealsResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "deal_count", Language.ZH_HANS_CN: "成交用户数"},
        {"field": "deal_usd", Language.ZH_HANS_CN: "成交市值(USD)"},
        {"field": "deal_percent", Language.ZH_HANS_CN: "成交市值全站占比"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "手续费市值（USD）"},
        {"field": "fee_percent", Language.ZH_HANS_CN: "手续费市值全站占比"},
        {"field": "avg_fee_rate", Language.ZH_HANS_CN: "平均费率"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        maker_type=fields.String(required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """报表-做市报表-现货"""
        if kwargs['maker_type'] not in ['ALL', 'INNER', 'OUTER']:
            raise InvalidArgument

        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyMakerTradeReport
        else:
            model = MonthlyMakerTradeReport
        query = model.query.filter(
            model.system == model.System.SPOT,
            model.maker_type == kwargs['maker_type'],
        ).order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)

        total = 0
        page = 1
        if kwargs['export']:
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            pagination = query.paginate(kwargs['page'], limit, error_out=False)
            items = pagination.items
            total = pagination.total
            page = pagination.page
        if kwargs['export']:
            return export_xlsx(
                filename='spot_maker_report',
                data_list=cls.get_export_data(items),
                export_headers=cls.export_headers
            )
        return dict(
            records=items,
            extra=dict(maker_types=model.MakerType),
            total=total,
            page=page,
        )

    @classmethod
    def get_export_data(cls, records):
        export_data = []
        for row in records:
            record = row.to_dict()
            record['deal_usd'] = amount_to_str(record['deal_usd'], 2)
            record['fee_usd'] = amount_to_str(record['fee_usd'], 2)
            record['deal_percent'] = format_percent(record['deal_percent'], 4)
            record['fee_percent'] = format_percent(record['fee_percent'], 4)
            record['avg_fee_rate'] = format_percent(record['avg_fee_rate'], 4)

            export_data.append(record)
        return export_data


@ns.route('/maker-market-detail')
@respond_with_code
class DailySpotMakerMarketDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True, missing=''),
        order=fields.String(missing='deal_usd'),
        trading_area=fields.String(missing=''),
        report_type=EnumField(ReportType, required=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-做市报表-现货交易市场详情"""

        order = kwargs['order']
        report_date = kwargs['report_date']
        page = kwargs['page']
        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyMakerMarketTradeReport
        else:
            model = MonthlyMakerMarketTradeReport

        query = model.query.filter(model.system == model.System.SPOT)
        if not report_date:
            last_item = query.order_by(
                model.report_date.desc()).limit(1).first()
            report_date = last_item.report_date.strftime(
                '%Y-%m-%d') if last_item else None
        if report_date:
            if kwargs['report_type'] == ReportType.MONTHLY:
                report_date = datetime.date(report_date.year, report_date.month, 1)
            query = query.filter(
                model.report_date == report_date)

        if order == 'deal_usd':
            query = query.order_by(model.deal_usd.desc())
        elif order == 'fee_usd':
            query = query.order_by(
                model.fee_usd.desc())
        elif order == 'deal_count':
            query = query.order_by(
                model.deal_count.desc())
        query = query.filter(
            model.report_date == report_date)

        total_market_deal_usd, total_market_fee_usd, total_deal_user_set = Decimal(), Decimal(), set()
        total_deal_usd, total_fee_usd = Decimal(), Decimal()

        for item in query.all():
            total_market_deal_usd += item.deal_usd
            total_deal_usd += item.total_deal_usd
            total_market_fee_usd += item.fee_usd
            total_fee_usd += item.total_fee_usd
            total_deal_user_set |= set(json.loads(item.deal_user_list))
        records = [
            {
                'report_date': '合计',
                'market': 'ALL',
                'deal_count': len(total_deal_user_set),
                'deal_usd': total_market_deal_usd,
                'fee_usd': quantize_amount(total_market_fee_usd, PrecisionEnum.CASH_PLACES),
                'deal_percent': total_market_deal_usd/total_deal_usd if total_deal_usd else 0,
                'fee_percent': total_market_fee_usd/total_fee_usd if total_fee_usd else 0,
                'avg_fee_rate': total_market_fee_usd/total_market_deal_usd if total_market_deal_usd else 0,
                'taker_sell_amount': '-',
            }
        ]

        pagination = query.paginate(page, limit, error_out=False)
        for item in pagination.items:
            report_date = item.report_date.strftime('%Y-%m-%d') \
                if kwargs['report_type'] == ReportType.DAILY else item.report_date.strftime('%Y-%m')
            record = {
                'report_date': report_date,
                'market': item.market,
                'deal_count': item.deal_count,
                'deal_usd': item.deal_usd,
                'deal_percent': item.deal_percent,
                'fee_usd': quantize_amount(item.fee_usd, PrecisionEnum.CASH_PLACES),
                'fee_percent': item.fee_percent,
                'avg_fee_rate': item.avg_fee_rate,
            }
            records.append(record)

        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
        )


@ns.route('/depth')
@respond_with_code
class DailySpotDepthResource(Resource):

    ALL_MARKETS = 'ALL'

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, required=True),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        market=fields.String(missing=ALL_MARKETS),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-现货交易-深度报表"""
        records = []
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailySpotDepthReport
        else:
            model = MonthlySpotDepthReport
        if kwargs["market"] != cls.ALL_MARKETS:
            query = cls.get_query_by(kwargs, model)
            pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        else:
            pagination = cls.get_pagination(kwargs, model)

        for obj in pagination.items:
            records.append(
                dict(
                    report_date=obj.report_date.strftime('%Y-%m-%d'),
                    market=obj.market,
                    depth1=amount_to_str(obj.depth1_usd, 2),
                    depth2=amount_to_str(obj.depth2_usd, 2),
                    depth3=amount_to_str(obj.depth3_usd, 2),
                    depth4=amount_to_str(obj.depth4_usd, 2),
                    depth5=amount_to_str(obj.depth5_usd, 2),
                    bid_ask_delta=amount_to_str(obj.bid_ask_delta, 8) if kwargs['market'] != cls.ALL_MARKETS else '0',
                )
            )
        return dict(
            records=records,
            market_list=[cls.ALL_MARKETS, ] + sorted(MarketCache.list_online_markets()),
            total=pagination.total,
        )

    @classmethod
    def get_pagination(cls, kwargs, model):
        query = model.query
        start_date = kwargs.get('start_date')
        end_date = kwargs.get('end_date')
        page, limit = kwargs['page'], kwargs['limit']
        total = query.with_entities(func.count(model.report_date.distinct())).scalar() or 0
        if total > 0:
            last_report_date_obj = query.order_by(model.report_date.desc()).first()
            latest_report_date = last_report_date_obj.report_date
            first_report_date_obj = query.order_by(model.report_date.asc()).first()
            first_report_date = first_report_date_obj.report_date

            final_range = [min(latest_report_date, end_date or latest_report_date),
                           max(first_report_date, start_date or first_report_date)]
            all_dates = [
                final_range[0] - datetime.timedelta(days=v)
                for v in range(0, (final_range[0] - final_range[1]).days+1)
            ]
            build_dates = all_dates[(page - 1) * limit:page * limit]
            query = query.filter(model.report_date.in_(build_dates)).group_by(
                model.report_date).with_entities(
                model.report_date,
                literal('ALL').label('market'),
                func.sum(model.depth1_usd).label('depth1_usd'),
                func.sum(model.depth2_usd).label('depth2_usd'),
                func.sum(model.depth3_usd).label('depth3_usd'),
                func.sum(model.depth4_usd).label('depth4_usd'),
                func.sum(model.depth5_usd).label('depth5_usd'),
            ).order_by(model.report_date.desc())
            return Pagination(None, page, limit, len(all_dates), query.all())
        else:
            return Pagination(None, page, limit, total, [])

    @classmethod
    def get_query_by(cls, kwargs, model):
        # todo：校验日期关系
        if kwargs['market'] != cls.ALL_MARKETS:
            query = model.query.filter(model.market == kwargs['market'])
        else:
            query = model.query
        if kwargs.get('start_date'):
            query = query.filter(model.report_date >= kwargs['start_date'])
        if kwargs.get('end_date'):
            query = query.filter(model.report_date <= kwargs['end_date'])
        
        if kwargs['market'] == cls.ALL_MARKETS:
            query = query.group_by(model.report_date).with_entities(
                model.report_date,
                literal('ALL').label('market'),
                func.sum(model.depth1_usd).label('depth1_usd'),
                func.sum(model.depth2_usd).label('depth2_usd'),
                func.sum(model.depth3_usd).label('depth3_usd'),
                func.sum(model.depth4_usd).label('depth4_usd'),
                func.sum(model.depth5_usd).label('depth5_usd'),
            )
        return query.order_by(model.report_date.desc())


@ns.route('/depth-detail')
@respond_with_code
class DailySpotDepthDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True, allow_none=True),
        order=fields.Integer(missing=1),
        report_type=EnumField(ReportType, required=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-现货交易-深度详情"""
        records = []
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailySpotDepthReport
        else:
            model = MonthlySpotDepthReport
        query = cls.get_query_by(kwargs, model)
        
        all_record = query.with_entities(
            func.sum(model.depth1_usd).label('depth1_usd'),
            func.sum(model.depth2_usd).label('depth2_usd'),
            func.sum(model.depth3_usd).label('depth3_usd'),
            func.sum(model.depth4_usd).label('depth4_usd'),
            func.sum(model.depth5_usd).label('depth5_usd'),
        ).first()
        pagination = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        for obj in pagination.items:
            records.append(
                dict(
                    report_date=obj.report_date.strftime('%Y-%m-%d'),
                    market=obj.market,
                    depth1=amount_to_str(obj.depth1_usd, 2),
                    depth2=amount_to_str(obj.depth2_usd, 2),
                    depth3=amount_to_str(obj.depth3_usd, 2),
                    depth4=amount_to_str(obj.depth4_usd, 2),
                    depth5=amount_to_str(obj.depth5_usd, 2),
                    bid_ask_delta=amount_to_str(obj.bid_ask_delta, 8)
                )
            )
        
        report_date = None
        if records:
            report_date = records[0]['report_date']
        all_record = dict(
            report_date=report_date,
            market='ALL',
            depth1=amount_to_str(all_record.depth1_usd, 2),
            depth2=amount_to_str(all_record.depth2_usd, 2),
            depth3=amount_to_str(all_record.depth3_usd, 2),
            depth4=amount_to_str(all_record.depth4_usd, 2),
            depth5=amount_to_str(all_record.depth5_usd, 2),
            bid_ask_delta='0',
        )
        records.insert(0, all_record)
        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
        )

    @classmethod
    def get_query_by(cls, kwargs, model):
        query = model.query
        order_field = cls.get_order_field(kwargs['order'], model)
        if order_field:
            query = query.order_by(order_field.desc())

        report_date = kwargs.get('report_date')
        if not report_date:
            obj = query.order_by(model.report_date.desc()).limit(1).first()
            if obj:
                report_date = obj.report_date
        if report_date:
            query = query.filter(model.report_date == report_date)

        return query

    @classmethod
    def get_order_field(cls, depth: int, model):
        mapping = {
            DepthLadder.depth1.value: model.depth1_usd,
            DepthLadder.depth2.value: model.depth2_usd,
            DepthLadder.depth3.value: model.depth3_usd,
            DepthLadder.depth4.value: model.depth4_usd,
            DepthLadder.depth5.value: model.depth5_usd,
        }
        return mapping.get(depth)
