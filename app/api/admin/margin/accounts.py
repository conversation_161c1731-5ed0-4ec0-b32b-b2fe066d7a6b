# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal
from typing import Optional
from webargs import fields

from flask import g

from app.api.common import Resource, Namespace, respond_with_code
from app.api.common import fields as common_fields
from app.api.common.fields import PositiveDecimal<PERSON>ield
from app.business.margin.helper import TRANSFER_RATE_DICT
from app.business.market import MarketOfflineHelper
from app.exceptions import InvalidArgument
from app.models import (
    MarginIndex, MarginAccount, MarginLiquidationRate, db, MarginAssetRule, UserMarginAccountRule,
    User, UserSpecialConfigChangeLog
)
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectSpot, \
    OPNamespaceObjectUser
from app.caches import MarginAccountNameCache, MarginAccountIdCache, MarketCache
from app.business import ServerClient, get_special_conf_create_operators
from app.utils.helper import Struct

ns = Namespace('Margin - Accounts')


@ns.route('')
@respond_with_code
class AccountListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(),
        status=fields.String(),
        page=fields.Int(missing=1),
        limit=fields.Int(missing=100),
    ))
    def get(cls, **kwargs):
        """杠杆-市场配置"""
        name = kwargs.get('name', '').upper()
        status = kwargs.get('status', '').lower()

        index_configs = MarginIndex.query.filter(MarginIndex.status == MarginIndex.StatusType.OPEN)
        market_type_set = [
            v.market_name for v in index_configs if not MarginAccount.query.filter(MarginAccount.name == v.market_name).first()
        ]

        all_market_type_set = [v.name for v in MarginAccount.query.all()]

        operator_type_set = [{"name": "<", "value": MarginLiquidationRate.OPERATOR_LT}]
        data = MarginAccount.query
        if name and name in all_market_type_set:
            data = data.filter(MarginAccount.name == name)
        if status:
            data = data.filter(MarginAccount.status == status)
        records = data.paginate(kwargs['page'], kwargs['limit']).items
        liquid_rate_data = MarginLiquidationRate.query.filter(
            MarginLiquidationRate.account_id.in_([account.id for account in records])
        ).all()
        liquid_rate_account_id_map = defaultdict(list)
        for liquid_rate in liquid_rate_data:
            liquid_rate_account_id_map[liquid_rate.account_id].append(liquid_rate.to_dict())

        final_result = []
        for record in records:
            format_record = record.to_dict()
            format_record['details'] = liquid_rate_account_id_map[format_record['id']]
            final_result.append(format_record)

        result = dict(
            market_type_set=market_type_set,
            all_market_type_set=all_market_type_set,
            status_dict={MarginAccount.StatusType.OPEN.value: "开启",
                         MarginAccount.StatusType.CLOSE.value: "关闭"},
            page=kwargs['page'],
            count=data.count(),
            market_type=name,
            operator_type_set=operator_type_set,
            records=final_result,
            support_leverage=sorted(list(TRANSFER_RATE_DICT.keys())),
        )

        return result

    @classmethod
    @ns.use_kwargs(dict(
        details=fields.List(fields.Dict),
        status=fields.String(required=True),
        name=fields.String(required=True),
        leverage=fields.Int(required=True),
        order_period=fields.Int(required=True),
        increase_period=fields.Int(required=True),
        warning_rate=fields.Decimal(required=True),
        base_precision=fields.Decimal(required=True),
        max_liquidation_rate=fields.Decimal(required=True),
        max_precision=fields.Decimal(required=True),
        increase_precision=fields.Decimal(required=True),
        base_asset_max_loan=common_fields.PositiveDecimalField(required=True),
    ))
    def post(cls, **kwargs):
        """杠杆-添加市场配置"""
        name = kwargs.get('name', '').upper()
        if not name:
            raise InvalidArgument
        cls.validate_(market=name)
        data = kwargs
        details = kwargs['details']

        margin_account_data = MarginAccount.query.filter(
            MarginAccount.name == name
        ).first()
        if margin_account_data:
            raise InvalidArgument(message='当前已经有对应交易对的配置信息')

        if data["leverage"] not in TRANSFER_RATE_DICT:
            raise InvalidArgument(message=f'不支持杠杆倍数为{data["leverage"]}')
        details_config = [MarginLiquidationRate(
            market_name=data["name"],
            operator=v["operator"],
            liquidation_amount=v["liquidation_amount"],
            liquidation_rate=Decimal(str(v["liquidation_rate"])),
            status=v["status"]
        ) for v in details]
        quote_asset_max_loan = cls.calc_quote_asset_max_loan(data["name"], data["base_asset_max_loan"]) or Decimal()
        account = MarginAccount(
            status=data["status"],
            name=data["name"],
            leverage=data["leverage"],
            warning_rate=Decimal(str(data["warning_rate"])),
            max_liquidation_rate=Decimal(str(data["max_liquidation_rate"])),
            margin_burst_rates=details_config,
            base_asset_max_loan=data["base_asset_max_loan"],
            quote_asset_max_loan=quote_asset_max_loan,
        )
        if "increase_period" in data and data["increase_period"]:
            account.increase_period = data["increase_period"]
        if "base_precision" in data and data["base_precision"]:
            account.base_precision = data["base_precision"]
        if "increase_precision" in data and data["increase_precision"]:
            account.increase_precision = data["increase_precision"]
        if "max_precision" in data and data["max_precision"]:
            account.max_precision = data["max_precision"]
        if "order_period" in data and data["order_period"]:
            account.order_period = data["order_period"]
        db.session.add(account)
        db.session.add_all(details_config)
        db.session.commit()

        MarginAccountNameCache.refresh_many([account.name])
        MarginAccountIdCache.refresh_many([account.id])
        MarginAccountIdCache.refresh_online_list()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.MarginMarket,
            detail=kwargs,
        )
        ServerClient().update_assets()

    @classmethod
    def validate_(cls, market):
        m = MarketCache(market).dict
        targets = m['base_asset'], m['quote_asset']
        model = MarginAssetRule
        asset_rules = MarginAssetRule.query.with_entities(
            model.asset
        ).filter(model.asset.in_(targets)).all()
        assets = [asset_rule.asset for asset_rule in asset_rules]
        if not all(target in assets for target in targets):
            raise InvalidArgument(message=f'please ensure margin asset rule does exist')

        MarketOfflineHelper.check_permission(market, MarketOfflineHelper.BusinessType.MARGIN)

    @classmethod
    def calc_quote_asset_max_loan(cls, market, base_asset_max_loan) -> Optional[Decimal]:
        from app.schedules.margin import calc_margin_market_quote_asset_max_loan

        # noinspection PyBroadException
        try:
            return calc_margin_market_quote_asset_max_loan(market, base_asset_max_loan)
        except Exception:
            # 计算出错 不更新
            return None


@ns.route('/<int:account_id>')
@respond_with_code
class EditAccountResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        details=fields.List(fields.Dict),
        status=fields.String(required=True),
        name=fields.String(required=True),
        leverage=fields.Int(required=True),
        order_period=fields.Int(required=True),
        increase_period=fields.Int(required=True),
        warning_rate=fields.Decimal(required=True),
        base_precision=fields.Decimal(required=True),
        max_liquidation_rate=fields.Decimal(required=True),
        max_precision=fields.Decimal(required=True),
        increase_precision=fields.Decimal(required=True),
        base_asset_max_loan=common_fields.PositiveDecimalField(required=True),
    ))
    def put(cls, account_id, **kwargs):
        """杠杆-编辑市场配置"""
        name = kwargs.get('name', '').upper()
        data = kwargs
        details = kwargs.get("details", [])

        margin_account_data = MarginAccount.query.filter(
            MarginAccount.id != account_id,
            MarginAccount.name == name
        ).first()
        if margin_account_data:
            raise InvalidArgument(message='当前已经有对应交易对的配置信息')

        _status = MarginAccount.StatusType(kwargs["status"])
        if _status == MarginAccount.StatusType.OPEN:
            MarketOfflineHelper.check_permission(margin_account_data.name, MarketOfflineHelper.BusinessType.MARGIN)

        account = MarginAccount.query.filter(MarginAccount.id == account_id).first()
        if not account:
            raise InvalidArgument(message='id不存在')
        old_data = account.to_dict(enum_to_name=True)
        if data["leverage"] not in TRANSFER_RATE_DICT:
            raise InvalidArgument(message=f'不支持杠杆倍数为{data["leverage"]}')
        for v in account.margin_burst_rates.all():
            v.status = MarginLiquidationRate.StatusType.DELETE
        account.status = data["status"]
        account.leverage = data["leverage"]
        account.warning_rate = Decimal(str(data["warning_rate"]))
        account.max_liquidation_rate = Decimal(str(data["max_liquidation_rate"]))
        account.base_asset_max_loan = data["base_asset_max_loan"]
        quote_asset_max_loan = AccountListResource.calc_quote_asset_max_loan(name, data["base_asset_max_loan"])
        if quote_asset_max_loan is not None:
            account.quote_asset_max_loan = quote_asset_max_loan
        if "increase_period" in data and data["increase_period"]:
            account.increase_period = data["increase_period"]
        if "base_precision" in data and data["base_precision"]:
            account.base_precision = data["base_precision"]
        if "increase_precision" in data and data["increase_precision"]:
            account.increase_precision = data["increase_precision"]
        if "max_precision" in data and data["max_precision"]:
            account.max_precision = data["max_precision"]
        if "order_period" in data and data["order_period"]:
            account.order_period = data["order_period"]
        details_config = [MarginLiquidationRate(
            market_name=data["name"],
            operator=v["operator"],
            liquidation_amount=v["liquidation_amount"],
            liquidation_rate=Decimal(str(v["liquidation_rate"])),
            status=MarginLiquidationRate.StatusType.PASS
        ) for v in details]

        account.margin_burst_rates = details_config
        db.session.add_all(details_config)
        db.session.commit()
        MarginAccountNameCache.refresh_many([account.name])
        MarginAccountIdCache.refresh_many([account.id])
        MarginAccountIdCache.refresh_online_list()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.MarginMarket,
            old_data=old_data,
            new_data=account.to_dict(enum_to_name=True),
            special_data=dict(name=name, details=details),
        )
        ServerClient().update_assets()

    @classmethod
    @ns.use_kwargs(dict(
        status=fields.String(required=True),
    ))
    def patch(cls, account_id, **kwargs):
        """杠杆-开关市场配置"""
        margin_account_data = MarginAccount.query.filter(
            MarginAccount.id == account_id
        ).first()
        if not margin_account_data:
            raise InvalidArgument(message=f"id {account_id} not exists")
        old_data = margin_account_data.to_dict(enum_to_name=True)
        if "status" in kwargs:
            _status = MarginAccount.StatusType(kwargs["status"])
            if _status == MarginAccount.StatusType.OPEN:
                MarketOfflineHelper.check_permission(margin_account_data.name, MarketOfflineHelper.BusinessType.MARGIN)

            margin_account_data.status = kwargs['status']

        db.session.commit()
        MarginAccountNameCache.refresh_many([margin_account_data.name])
        MarginAccountIdCache.refresh_many([margin_account_data.id])
        MarginAccountIdCache.refresh_online_list()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.MarginMarket,
            old_data=old_data,
            new_data=margin_account_data.to_dict(enum_to_name=True),
            special_data=dict(name=margin_account_data.name),
        )
        ServerClient().update_assets()


@ns.route('/info')
@respond_with_code
class MarginAccountInfoResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            name=fields.String(required=True)
        )
    )
    def get(cls, **kwargs):
        """支持特殊杠杆市场借币配置自动填充"""
        params = Struct(**kwargs)
        m = MarginAccountNameCache(params.name).dict
        return {
            'base_asset_max_loan': m['base_asset_max_loan'],
            'quote_asset_max_loan': m['quote_asset_max_loan']
        }


@ns.route("/user-rules")
@respond_with_code
class UserMarginAccountRuleResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            market=fields.String,
            keyword=fields.String,
            page=fields.Int(missing=1),
            limit=fields.Int(missing=100),
        )
    )
    def get(cls, **kwargs):
        """ 用户-特殊配置-特殊市场借币额度配置->列表 """
        params = Struct(**kwargs)
        rule_q = cls.get_query_by(params)
        pagination = rule_q.paginate(params.page, params.limit)
        records = pagination.items
        user_ids = list({i.user_id for i in records})
        user_email_map = dict(
            User.query.filter(User.id.in_(user_ids))
            .with_entities(
                User.id,
                User.email,
            )
            .all()
        )
        record_ids = [i.id for i in records]
        operator_id_dict, operator_name_dict = get_special_conf_create_operators(
            record_ids, UserSpecialConfigChangeLog.SpecialConfigType.MARGIN_ACCOUNT_LOAN_LIMIT)
        return dict(
            items=[
                {
                    "id": i.id,
                    "user_id": i.user_id,
                    "email": user_email_map.get(i.user_id) or '-',
                    "market": i.market,
                    "base_asset_max_loan": i.base_asset_max_loan,
                    "quote_asset_max_loan": i.quote_asset_max_loan,
                    "remark": i.remark,
                    "operator": operator_name_dict.get(i.id),
                    "operator_id": operator_id_dict.get(i.id)
                }
                for i in records
            ],
            total=pagination.total,
            extra=dict(
                markets=cls._online_margin_markets(),
            )
        )

    @classmethod
    def get_query_by(cls, params):
        model = UserMarginAccountRule
        rule_q = model.query.filter(model.status == model.StatusType.PASS)
        if market := params.market:
            rule_q = rule_q.filter(model.market == market)
        if keyword := params.keyword:
            user_ids = User.search_for_users(keyword)
            rule_q = rule_q.filter(model.user_id.in_(user_ids))

        rule_q = rule_q.order_by(model.id.desc())
        return rule_q

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            market=fields.String(required=True),
            base_asset_max_loan=PositiveDecimalField(required=True),
            quote_asset_max_loan=PositiveDecimalField(required=True),
            remark=fields.String(missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 用户-特殊配置-特殊市场借币额度配置->新增 """
        params = Struct(**kwargs)
        user_id = params.user_id
        user = User.query.get(user_id)
        if not user:
            raise InvalidArgument
        market = params.market
        if market not in cls._online_margin_markets():
            raise InvalidArgument(message=f'does not support the margin market: {market!r}')

        model = UserMarginAccountRule
        exist_rule = model.query.filter(
            model.user_id == user_id,
            model.market == market,
        ).first()
        if exist_rule:
            old_data = exist_rule.to_dict(enum_to_name=True)
            exist_rule.status = model.StatusType.PASS
            exist_rule.base_asset_max_loan = params.base_asset_max_loan
            exist_rule.quote_asset_max_loan = params.quote_asset_max_loan
            exist_rule.remark = params.remark or ''
            row = exist_rule

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.SpecialMarginAccountRule,
                old_data=old_data,
                new_data=exist_rule.to_dict(enum_to_name=True),
                target_user_id=user_id,
            )
        else:
            record = model(
                user_id=user_id,
                market=market,
                base_asset_max_loan=params.base_asset_max_loan,
                quote_asset_max_loan=params.quote_asset_max_loan,
                remark=params.remark or '',
                status=model.StatusType.PASS,
            )
            db.session.add(record)
            row = record

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.SpecialMarginAccountRule,
                detail=kwargs,
                target_user_id=user_id,
            )
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=row.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.MARGIN_ACCOUNT_LOAN_LIMIT,
            op_type=UserSpecialConfigChangeLog.OpType.CREATE,
            admin_user_id=g.user.id,
            change_detail=row.record_detail,
            change_remark=kwargs.get('remark'),
            op_id=row.id
        )

    @classmethod
    def _online_margin_markets(cls) -> list:
        return list(MarginAccountIdCache.list_online_markets().values())

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            user_id=fields.Integer(required=True),
            market=fields.String(required=True),
            base_asset_max_loan=PositiveDecimalField(required=True),
            quote_asset_max_loan=PositiveDecimalField(required=True),
            remark=fields.String(missing=""),
        )
    )
    def put(cls, **kwargs):
        """ 用户-特殊配置-特殊市场借币额度配置->编辑 """
        params = Struct(**kwargs)
        user_id = params.user_id
        user = User.query.get(user_id)
        if not user:
            raise InvalidArgument

        id_ = params.id
        model = UserMarginAccountRule
        to_update_rule = model.query.get(id_)
        if not to_update_rule:
            raise InvalidArgument
        old_data = to_update_rule.to_dict(enum_to_name=True)

        market = params.market
        if to_update_rule.market != market:
            exist_asset_rule = model.query.filter(
                model.user_id == user_id,
                model.market == market,
            ).first()
            if exist_asset_rule and exist_asset_rule.id != id_:
                raise InvalidArgument(f"the user margin market config already exist")

        to_update_rule.status = model.StatusType.PASS
        to_update_rule.market = market
        to_update_rule.base_asset_max_loan = params.base_asset_max_loan
        to_update_rule.quote_asset_max_loan = params.quote_asset_max_loan
        to_update_rule.remark = params.remark or ''
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=to_update_rule.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.MARGIN_ACCOUNT_LOAN_LIMIT,
            op_type=UserSpecialConfigChangeLog.OpType.UPDATE,
            admin_user_id=g.user.id,
            change_detail=to_update_rule.record_detail,
            change_remark=kwargs.get('remark'),
            op_id=to_update_rule.id
        )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpecialMarginAccountRule,
            old_data=old_data,
            new_data=to_update_rule.to_dict(enum_to_name=True),
            target_user_id=to_update_rule.user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 用户-特殊配置-特殊市场借币额度配置->删除 """
        id_ = kwargs["id"]
        model = UserMarginAccountRule
        rule = model.query.get(id_)
        if not rule:
            raise InvalidArgument
        rule.status = model.StatusType.DELETE
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=rule.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.MARGIN_ACCOUNT_LOAN_LIMIT,
            op_type=UserSpecialConfigChangeLog.OpType.DELETE,
            admin_user_id=g.user.id,
            change_detail=rule.record_detail,
            op_id=rule.id
        )

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpecialMarginAccountRule,
            detail=rule.to_dict(enum_to_name=True),
            target_user_id=rule.user_id,
        )
