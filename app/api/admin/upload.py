# -*- coding: utf-8 -*-
import hashlib
import io
import os
import json
from PIL import Image

from flask import request, g
from werkzeug.utils import secure_filename
from webargs import fields as wa_fields

from lottie.objects import Animation

from .utils import UploadFileHelper
from ..common import (Namespace, Resource, respond_with_code)
from ...business.file import upload_subtitle_file
from ...caches.activity import AdminFileUploadChunk
from ...exceptions import (InvalidArgument, ServiceUnavailable,
                           ImageFormatError, FileTooBig)
from ...models import db, File
from ...models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from ...utils import AWSBucketPublic, new_file_key, today, AWSBucketPrivate
from ...utils.file import open_file_from_url, ThumbnailDimensions, DEFAULT_THUMBNAIL_SCALE, ThumbnailScale
from logging import getLogger

ns = Namespace('Upload')
_logger = getLogger(__name__)


@ns.route('/image')
@respond_with_code
class ImageUploadResource(Resource):
    ALLOWED_ZIP_FORMAT = ['png', 'jpeg', 'jpg', 'tiff']
    VIDEO_TYPE = ('mp4', 'avi', 'wmv', 'mpeg', 'm4v', 'mov', 'flv', '3gp')
    IMAGE_TYPE = ('png', 'jpeg', 'jpg', 'webp', 'gif', 'svg', 'tiff')
    FILE_TYPE = ('pdf', 'json')
    ALL_TYPE = VIDEO_TYPE + IMAGE_TYPE + FILE_TYPE

    @classmethod
    def post(cls):
        """上传-文件上传"""
        img = request.files.get('img')
        if not img:
            raise InvalidArgument
        ext = os.path.splitext(img.filename)[1].lstrip('.')
        if ext.lower() not in cls.ALL_TYPE:
            raise ImageFormatError
        if ext == 'json':
            # 检查lottie格式是否正确
            cls._validate_lottie(img)
        img_size = request.form.get('img_size')
        if img_size:
            cls._validate_img_size(img, img_size)
            img.stream.seek(0)
        img, img_type, source_filename = cls.try_auto_zip(ext, img)
        return cls.upload_file(img, ext, img_type, source_filename)

    @classmethod
    def upload_file(cls, img, ext, img_type, fn):
        extra_args, filename, length = cls._try_get_extra_args(ext)
        if int(request.headers['CONTENT_LENGTH']) > length:
            raise FileTooBig
        mime_type = cls.get_mime_type(img_type)
        if filename:
            filename = f'{today().strftime("%Y-%m-%d")}/{filename}.{img_type}'
        is_private = request.form.get('is_private')
        bucket = AWSBucketPrivate if is_private else AWSBucketPublic

        file_key = bucket.new_file_key(key=filename) if filename \
            else bucket.new_file_key(suffix=img_type)
        if not bucket.put_file(file_key, img, **extra_args):
            raise ServiceUnavailable
        url = bucket.get_file_url(file_key)
        fn = fn or secure_filename(img.filename)
        new_file: File = File.new(
            g.user.id, file_key, fn, mime_type=mime_type)
        db.session_add_and_commit(new_file)

        if request.form.get('gen_thumbnail'):
            img.stream.seek(0)
            bucket.put_file_thumbnail(file_key, img, img_type, DEFAULT_THUMBNAIL_SCALE)
        return dict(file_url=url, file_key=file_key, file_id=new_file.id, file_name=fn)

    @classmethod
    def _try_get_extra_args(cls, ext):
        ext = ext.lower()
        extra_args = {}
        length = 5 * 1024 * 1024
        filename = None
        if ext in cls.FILE_TYPE:
            length = 10 * 1024 * 1024
            cd = request.form.get('ContentDisposition')
            filename = request.form.get('filename')
            v = f'inline;filename={filename}' if filename else 'inline'
            if cd == 'inline':
                extra_args = {
                    'ContentType': f'application/{ext}',
                    'ContentDisposition': v,
                }
        elif ext in cls.VIDEO_TYPE:
            length = 30 * 1024 * 1024
        elif ext == "svg":
            extra_args = {'ContentType': 'image/svg+xml'}
        return extra_args, filename, length

    @classmethod
    def _validate_img_size(cls, img, img_size):
        # case1指定大小 '400,400'
        # case2指定大小范围 '400-600,400-600'
        require_width, require_height = img_size.split(',')
        require_widths = require_width.split('-')
        if len(require_widths) == 1:
            min_width = max_width = int(require_widths[0])
        elif len(require_widths) == 2:
            min_width, max_width = int(require_widths[0]), int(require_widths[1])
        else:
            raise InvalidArgument(message='上传的图片宽度参数错误！')
        require_heights = require_height.split('-')
        if len(require_heights) == 1:
            min_height = max_height = int(require_heights[0])
        elif len(require_heights) == 2:
            min_height, max_height = int(require_heights[0]), int(require_heights[1])
        else:
            raise InvalidArgument(message='上传的图片高度参数错误！')
        im = Image.open(img)
        img_width, img_height = im.size
        if not (min_width <= img_width <= max_width) or not (min_height <= img_height <= max_height):
            width_msg = f"宽：{min_width}" if min_width == max_width else f'宽：{min_width} ~ {max_width}'
            height_msg = f'高：{min_height}' if min_height == max_height else f'高：{min_height} ～ {max_height}'
            msg = f'{width_msg}， {height_msg}'
            raise InvalidArgument(message='上传的图片尺寸错误！图片尺寸必须为: {}'.format(msg))

    @classmethod
    def _validate_lottie(cls, img):
        file_bytes = img.stream.read()
        img.stream.seek(0)
        try:
            lottie_json_content = json.loads(file_bytes)
            lottie_obj = Animation.load(lottie_json_content)
        except Exception:
            raise InvalidArgument(message='无效的lottie文件')
        if not lottie_obj:
            raise InvalidArgument(message='无效的lottie文件')

    @classmethod
    def get_mime_type(cls, img_type):
        if img_type in cls.FILE_TYPE:
            mime_type = File.MimeType.FILE
        elif img_type == 'svg':
            mime_type = File.MimeType.IMG_SVG
        elif img_type == 'webp':
            mime_type = File.MimeType.IMG_WEBP
        elif img_type in cls.VIDEO_TYPE:
            mime_type = File.MimeType.VIDEO
        else:
            mime_type = File.MimeType.IMG_PNG if img_type == 'png' \
                else File.MimeType.IMG_JPG
        return mime_type

    @classmethod
    def try_auto_zip(cls, ext, img):
        source_filename = secure_filename(img.filename)
        img_type = ext.lower()
        auto_zip = request.form.get('auto_zip')
        if img_type in cls.ALLOWED_ZIP_FORMAT and auto_zip == 'WEBP':
            img_type = 'webp'
            opened_img = Image.open(img)
            output = io.BytesIO()
            opened_img.save(output, format="WEBP")
            source_filename = f'{source_filename.split(".")[0]}.{img_type}'
            return output.getvalue(), img_type, source_filename
        return img, img_type, source_filename


@ns.route('/generate-icon-thumbnail')
@respond_with_code
class GenerateIconThumbnailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        image_file_key=wa_fields.String(required=True),
        width=wa_fields.Integer(required=True),
        height=wa_fields.Integer(required=True),
    ))
    def post(cls, **kwargs):
        """上传-币种Icon生成缩略图"""
        image_file_key = kwargs['image_file_key']
        width_and_height = kwargs['width'], kwargs['height']
        return cls.generate_thumbnail_icon(image_file_key, width_and_height, g.user.id)

    @classmethod
    def generate_thumbnail_icon(cls, icon_key: str, width_and_height, op_user_id: int):
        """ 币种icon-生成缩略图，特殊逻辑太多，不使用handle_public_reform，单独实现 """
        thumbnail_img_type = 'png'
        thumbnail_img_mime_type = File.MimeType.IMG_PNG

        dim_size = ThumbnailDimensions(*width_and_height, quality=95)
        icon_url = AWSBucketPublic.get_file_url(icon_key)
        image = Image.open(open_file_from_url(icon_url))
        orig_w, orig_h = image.size

        thumbnail_key = dim_size.gen_thumbnail_key(icon_key)
        thumbnail_key = os.path.splitext(thumbnail_key)[0] + "." + thumbnail_img_type

        # 分步压缩
        if int(orig_w / 2) > width_and_height[0] and int(orig_h / 2) > width_and_height[1]:
            image = image.resize((int(orig_w / 2), int(orig_h / 2)), resample=Image.LANCZOS)
        if int(orig_w / 4) > width_and_height[0] and int(orig_h / 4) > width_and_height[1]:
            image = image.resize((int(orig_w / 4), int(orig_h / 4)), resample=Image.LANCZOS)

        resized_image = image.resize(width_and_height, resample=Image.LANCZOS)
        image_binary = io.BytesIO()
        # 生成png格式的缩略图（png比webp的缩略图更清晰）；method 6质量最佳，速度最慢
        resized_image.save(image_binary, thumbnail_img_type, quality=dim_size.quality, method=6)
        image_binary.seek(0)

        if not AWSBucketPublic.put_file(thumbnail_key, image_binary):
            raise ServiceUnavailable

        url = AWSBucketPublic.get_file_url(thumbnail_key)
        source_filename = secure_filename(os.path.basename(thumbnail_key))
        new_file: File = File.new(op_user_id, thumbnail_key, source_filename, mime_type=thumbnail_img_mime_type)
        db.session.add(new_file)
        db.session.commit()

        return dict(file_url=url, file_key=thumbnail_key, file_id=new_file.id)


@ns.route('/video')
@respond_with_code
class VideoUploadResource(Resource):
    CHUNK_DIR = '/tmp/chunks/'
    AWS_UPLOAD_DIR = 'upload-video'
    CHUNK_SIZE_LIMIT = 10 * 1024 * 1024  # MB
    TOTAL_SIZE_LIMIT = 500 * 1024 * 1024  # MB

    @classmethod
    def post(cls):
        """上传-视频上传"""
        file = request.files.get('file')
        chunk_number = int(request.form.get('chunkNumber'))
        total_chunks = int(request.form.get('totalChunks'))
        chunk_md5 = request.form.get('chunkMd5')
        file_md5 = request.form.get('fileMd5')
        total_size = int(request.form.get('size'))
        chunk_size = int(request.form.get('chunkSize'))
        origin_filename = request.form.get('name')
        format_file_name, format_file_type = os.path.splitext(origin_filename)
        file_type = format_file_type.strip('.')
        md5_file_name = hashlib.md5(format_file_name.encode('utf-8')).hexdigest()
        if not file:
            raise InvalidArgument(message="文件未上传")
        if file_type not in {'mp4'}:
            raise InvalidArgument(message="仅支持mp4的文件上传")
        if chunk_size > cls.CHUNK_SIZE_LIMIT or total_size > cls.TOTAL_SIZE_LIMIT:
            raise FileTooBig
        file_stream = file.read()

        file_helper = UploadFileHelper(cls.CHUNK_DIR)

        if not file_helper.is_match_md5(file_stream, chunk_md5):
            raise InvalidArgument(message="文件MD5不一致，请重新上传")

        # 暂时不支持同文件名下的多并发上传
        file_cache = AdminFileUploadChunk(md5_file_name)
        if file_cache.exists() and total_chunks > int(file_cache.get()) > chunk_number:
            # 暂停/断开后继续上传
            return dict(is_completed=False, chunkNumber=int(file_cache.get())+1)

        filename = f'{md5_file_name}.{file_type}'
        file_helper.write_chunk_file(filename, file_stream, chunk_number)
        file_cache.set(chunk_number, ex=86400)

        if chunk_number == total_chunks:  # 表示上传到了最后一个
            file_helper.merge_file(filename, total_chunks)
            file_stream = file_helper.read_file(filename)
            if not file_helper.is_match_md5(file_stream, file_md5):
                raise InvalidArgument(message="文件MD5不一致，请重新上传")
            mime_type = File.MimeType.VIDEO
            file_key = AWSBucketPublic.new_file_key(key=f'{cls.AWS_UPLOAD_DIR}/{new_file_key(suffix=file_type)}')

            thumbnail_keys = AWSBucketPublic.put_file_with_thumbnail(
                    file_key, file_stream, suffix=file_type, size=ThumbnailScale(ThumbnailScale.Size.ORIGINAL)
            )
            if not thumbnail_keys:
                raise ServiceUnavailable
            cover_key = thumbnail_keys[0]

            url = AWSBucketPublic.get_file_url(file_key)
            filename = secure_filename(filename)
            new_file: File = File.new(
                g.user.id, file_key, filename, mime_type=mime_type, size=total_size)
            db.session.add(new_file)
            db.session.commit()
            file_helper.clear_chunks(filename, total_chunks, ignore_not_exist=True)
            file_cache.delete()

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.VideoUpload,
                detail=dict(id=new_file.id, name=new_file.name, static_url=new_file.static_url),
            )

            result = dict(
                file_url=url,
                file_key=file_key,
                file_id=new_file.id,
                filename=filename,
                is_completed=True,
                cover_key=cover_key,
            )

            return result

        return dict(
            is_completed=False,
            chunkNumber=chunk_number+1,
        )


@ns.route('/video-subtitle')
@respond_with_code
class VideoSubtitleUploadResource(Resource):
    FILE_TYPE = ['vtt']

    @classmethod
    def post(cls):
        """上传-文件上传"""
        file = request.files.get('file')
        if not file:
            _logger.error(f'Video subtitle file upload failed: no file provided')
            raise InvalidArgument
        ext = os.path.splitext(file.filename)[1].lstrip('.')
        if ext.lower() not in cls.FILE_TYPE:
            _logger.error(f'Video subtitle file upload failed: unsupported file type {ext}')
            raise ImageFormatError
        video_file_key = request.form.get('video_file_key')
        lang = request.form.get('lang')

        upload_result = upload_subtitle_file(file, ext, video_file_key, lang)
        new_file: File = File.new(
            g.user.id, upload_result['file_key'], upload_result['file_name'], mime_type=upload_result['mime_type']
        )
        db.session_add_and_commit(new_file)
        upload_result['file_id'] = new_file.id

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.VideoSubtitleUpload,
            detail=dict(id=new_file.id, name=new_file.name, static_url=new_file.static_url),
        )

        return upload_result
