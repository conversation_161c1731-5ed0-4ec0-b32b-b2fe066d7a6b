from collections import defaultdict
from logging import getLogger

from sqlalchemy import or_
from webargs import fields

from flask import g

from app.business.clients.ai_translate import AITranslateClient, FormatType, TermsEnum
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from app.models.mongo.translation import TranslationTaskMySQL
from app.utils import AWSBucketPublic, batch_iter
from app.utils.helper import Struct
from app.api.common import Resource
from app.api.common import Namespace
from app.api.common import respond_with_code
from app.api.common.fields import EnumField, PageField, LimitField
from app.common import Language, language_cn_names

from app.models import db
from app.models.user import File, User
from app.models.media import Video, VideoSubtitle
from app.exceptions import InvalidArgument, RecordNotFound

ns = Namespace('Media')
lang_names = language_cn_names()


_logger = getLogger(__name__)


def get_user_name_dict(user_ids):
    res = dict()
    for ids in batch_iter(user_ids, 5000):
        users = User.query.filter(
            User.id.in_(ids)
        ).with_entities(
            User.id,
            User.name,
            User.email,
        ).all()
        res.update({user.id: user.name or user.email for user in users})
    return res


@ns.route('/video-list')
@respond_with_code
class MediaVideoListResource(Resource):
    """运营-视频文件上传管理列表"""
    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(unlimited=True, missing=1),
        limit=LimitField(missing=50),
        id=fields.Integer(required=False, allow_none=True, missing=None, default=None, load_default=None),
        name=fields.String(required=False, missing=None, default=None),
        business=EnumField(Video.Business, required=False, missing=None, default=None),
        user_id=fields.Integer(required=False, allow_none=True, missing=None),
    ))
    def get(cls, **kwargs):
        """运营-视频文件列表"""
        params = Struct(**kwargs)

        query = db.session.query(
            Video,
            File.size.label('file_size'),
            File.key.label('file_key')
        ).join(
            File, File.id == Video.file_id
        )

        # 根据参数动态添加过滤条件
        if params.id:
            query = query.filter(Video.id == params.id)
        if params.name:
            query = query.filter(Video.name.like(f"%{params.name}%"))
        if params.business:
            query = query.filter(Video.business == params.business)
        if params.user_id:
            query = query.filter(Video.user_id == params.user_id)
        query = query.order_by(Video.id.desc())
        records = query.paginate(params.page, params.limit)

        # 针对当前页的视频查询字幕统计
        video_ids = [v.Video.id for v in records.items]

        # 获取当前页视频的ID列表
        subtitles = {}
        if video_ids:
            subtitle_records = db.session.query(
                VideoSubtitle.video_id,
                VideoSubtitle.lang,
                VideoSubtitle.id,
                VideoSubtitle.file_id,
                VideoSubtitle.file_key,
                VideoSubtitle.is_translated,
            ).filter(
                VideoSubtitle.video_id.in_(video_ids)
            ).all()

            # 构建字幕信息
            subtitles = defaultdict(dict)
            for row in subtitle_records:
                subtitles[row.video_id][row.lang.name] = {
                    'name': lang_names.get(row.lang),
                    'file_key': row.file_key,
                    'file_id': row.file_id,
                    'file_url': AWSBucketPublic.get_file_url(row.file_key) if row.file_key else "",
                    'is_translated': row.is_translated,
                }

        # 构建返回数据
        user_ids = {v.Video.user_id for v in records.items}
        name_map = get_user_name_dict(user_ids)
        items = []
        for item in records.items:
            video = item.Video
            # 使用 AWSBucketPublic 生成 static_url
            file_static_url = AWSBucketPublic.get_file_url(item.file_key) if item.file_key else ""
            tmp = dict(
                id=video.id,
                created_at=video.created_at,
                name=video.name,
                user_id=video.user_id,
                user_name=name_map.get(video.user_id, ""),
                size=item.file_size,
                file_id=video.file_id,
                file_key=item.file_key,
                file_url=file_static_url,
                business=video.business.name if video.business else None,
                platform=video.platform.name if video.platform else None,
                lang=video.lang.name,
                subtitles=subtitles.get(video.id, {}),  # 没有字幕的视频默认为0
                subtitle_source=subtitles.get(video.id, {}).get(Language.EN_US.name, {}),  # 英文字幕
                update_time=video.updated_at.strftime('%Y-%m-%d %H:%M:%S') if video.updated_at else None,
            )
            items.append(tmp)

        return dict(
            total=records.total,
            items=items,
        )


@ns.route('/video/enums')
@respond_with_code
class MediaVideoEnumsResource(Resource):
    """运营-视频文件枚举接口"""

    @classmethod
    def get(cls):
        """获取视频相关枚举"""
        # 平台枚举
        platforms = {
            Video.Platform.ALL.name: 'Web + App',
            Video.Platform.WEB.name: 'Web',
            Video.Platform.APP.name: 'App',
        }

        # 业务枚举
        businesses = {
            Video.Business.PERPETUAL_TRADE.name: '合约交易',
            Video.Business.SPOT_TRADE.name: '现货交易',
            Video.Business.MARGIN_TRADE.name: '杠杆交易',
            Video.Business.THIRD_PARTY_BUY.name: '第三方买币',
            Video.Business.THIRD_PARTY_SELL.name: '第三方卖币',
            Video.Business.WITHDRAWAL.name: '提现',
            Video.Business.DEPOSIT.name: '充值',
            Video.Business.SWAP.name: '兑换',
            Video.Business.FINANCIAL.name: '理财',
            Video.Business.AMM.name: 'AMM',
            Video.Business.AMBASSADOR_INFO.name: '大使信息',
            Video.Business.AMBASSADOR_AGENT.name: '大使代理',
            Video.Business.P2P.name: 'P2P交易',
            Video.Business.PRE_MARKET.name: '预测市场',
            Video.Business.SPOT_GRID.name: '现货网格',
            Video.Business.AUTO_INVEST.name: '定投计划',
            Video.Business.STAKING.name: '质押',
            Video.Business.LEAD_TRADER.name: '合约跟单交易员',
            Video.Business.COPY_TRADER.name: '合约跟单员',
            Video.Business.PLEDGE_TOPIC_TOP.name: '借贷专题页顶部',
            Video.Business.PLEDGE_ASSET_SIDE.name: '借贷可借币种旁',
            Video.Business.SPOT_GUIDE_APP.name: '现货-App功能区-交易指引-现货',
            Video.Business.MARGIN_GUIDE_APP.name: '现货-App功能区-交易指引-杠杆',
            Video.Business.FUTURES_GUIDE_APP.name: '合约-App功能区-交易指引',
        }

        # 语言枚举
        languages = {
            Language.EN_US.name: '英语',
            Language.ZH_HANS_CN.name: '简体中文',
            Language.ZH_HANT_HK.name: '繁体中文',
            Language.JA_JP.name: '日语',
            Language.RU_KZ.name: '俄语',
            Language.KO_KP.name: '韩语',
            Language.ID_ID.name: '印尼语',
            Language.ES_ES.name: '西班牙语',
            Language.FA_IR.name: '波斯语',
            Language.TR_TR.name: '土耳其语',
            Language.VI_VN.name: '越南语',
            Language.AR_AE.name: '阿拉伯语',
            Language.FR_FR.name: '法语',
            Language.PT_PT.name: '葡萄牙语',
            Language.DE_DE.name: '德语',
            Language.TH_TH.name: '泰语',
            Language.IT_IT.name: '意大利语',
            Language.PL_PL.name: '波兰语',
        }

        return dict(
            enums=dict(
                platforms=platforms,
                businesses=businesses,
                languages=languages,
            )
        )


def _translate_video_subtitle(video_id, subtitle_key):
    """翻译视频字幕"""
    subtitle_content = AWSBucketPublic.get_file_content(subtitle_key).decode('utf-8')
    translator = AITranslateClient(business=TranslationTaskMySQL.Business.VIDEO_SUBTITLE)
    _logger.warning(f"Video {video_id} translating...")
    translator.translate_async(
        subtitle_content, Language.EN_US, [lang for lang in Language if lang != Language.EN_US],
        business_id=video_id, format_type=FormatType.VTT, terms=TermsEnum.DEFAULT_TERMS
    )


@ns.route('/video')
@respond_with_code
class MediaVideoNewResource(Resource):
    """运营-视频文件及字幕上传管理"""
    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        file_id=fields.Integer(required=True),
        file_key=fields.String(required=True),
        cover_key=fields.String(required=False, missing=None),
        business=EnumField(Video.Business, required=False, missing=None),
        platform=EnumField(Video.Platform, required=False, missing=None),
        lang=EnumField(Language, default=Language.EN_US),
        subtitle=fields.Dict(required=False, default=None, missing=None),
        remark=fields.String,
    ))
    def post(cls, **kwargs):
        """运营管理-视频新建"""
        params = Struct(**kwargs)
        user_id = g.user.id

        # 新建视频
        video = Video(
            name=params.name,
            business=params.business,
            platform=params.platform,
            lang=params.lang,
            file_id=params.file_id,
            file_key=params.file_key,
            cover_key=params.cover_key,
            user_id=user_id,
            usage=Video.Usage.TUTORIAL,
        )
        db.session.add(video)

        db.session.flush()

        subtitle = params.subtitle
        subtitle_id = None
        subtitle_key = None
        if subtitle:
            subtitle_lang = Language[subtitle.get('lang')]
            subtitle_id = subtitle.get('file_id', '')
            subtitle_key = subtitle.get('file_key', '')
            if not subtitle_lang or not subtitle_id or not subtitle_key:
                raise InvalidArgument(message="Subtitle lang, file_id and file_key are required")

            # 创建字幕
            subtitle_obj = VideoSubtitle(
                video_id=video.id,
                lang=subtitle_lang,
                file_id=subtitle_id,
                file_key=subtitle_key
            )
            db.session.add(subtitle_obj)
            db.session.flush()
            subtitle_id = subtitle_obj.id

        db.session.commit()

        if subtitle_id:
            _translate_video_subtitle(video.id, subtitle_key)

        AdminOperationLog.new_add(
            user_id=user_id,
            ns_obj=OPNamespaceObjectOperation.BusinessVideo,
            detail=kwargs,
            target_user_id=user_id,
        )

        return dict(
            id=video.id,
            subtitle_id=subtitle_id,
        )


@ns.route('/video/<video_id>')
@respond_with_code
class MediaVideoBusinessResource(Resource):
    @classmethod
    def _get_video(cls, target_business, target_platform, target_lang, video_id):
        """检查视频是否存在"""
        if target_business is None or target_platform is None:
            return None
        if target_platform == Video.Platform.ALL:
            # 如果是全平台，则只需要检查业务和语言
            return Video.query.filter(
                Video.business == target_business, Video.lang == target_lang, Video.id != video_id
            ).first()
        # 如果是特定平台，则需要检查业务、语言和平台
        else:
            return Video.query.filter(
                Video.business == target_business, Video.lang == target_lang,
                or_(Video.platform == target_platform, Video.platform == Video.Platform.ALL),
                Video.id != video_id
            ).first()

    @classmethod
    @ns.use_kwargs(dict(
        business=EnumField(Video.Business, required=True),
        platform=EnumField(Video.Platform, required=True),
    ))
    def get(cls, video_id, **kwargs):
        """业务视频管理-查询是否已经存在跟该视频同样语言，同样目标展示位的视频"""
        params = Struct(**kwargs)
        video = Video.query.get(video_id)

        conflict_video = cls._get_video(params.business, params.platform, video.lang, video_id)
        # 返回视频的基本信息
        return dict(
            target_vidio=video,
            conflict_video=conflict_video
        )

    @classmethod
    def delete(cls, video_id):
        """业务视频管理-删除视频"""
        video = Video.query.get(video_id)
        if not video:
            raise RecordNotFound(message=f"Video record not found for id {video_id}")

        db.session.delete(video)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.BusinessVideo,
            detail=video.to_dict(enum_to_name=True),
        )

        return dict(
            message="Video deleted successfully"
        )

    @classmethod
    def _assign_business(cls, video, business: Video.Business, platform: Video.Platform):
        """分配业务视频"""
        old_video = cls._get_video(business, platform, video.lang, video.id)
        old_business = video.business
        if old_video:
            old_video.business = None

        video.business = business

        old_platform = video.platform
        video.platform = platform
        db.session.commit()

        # 记录操作日志
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.BusinessVideo,
            old_data=dict(
                business=old_business.name if old_business else None,
                platform=old_platform.name if old_platform else None,
            ),
            new_data=dict(
                business=business.name if business else None,
                platform=platform.name if platform else None,
            ),
        )
        return old_video

    @classmethod
    def _update_subtitle(cls, video, subtitle):
        """更新视频字幕"""
        subtitle_lang_str = subtitle.get('lang')
        subtitle_id = subtitle.get('file_id', '')
        subtitle_key = subtitle.get('file_key', '')

        if not subtitle_lang_str or not subtitle_id or not subtitle_key:
            raise InvalidArgument(message="Subtitle lang, file_id and file_key are required")

        subtitle_lang = Language[subtitle_lang_str]
        if subtitle_lang != Language.EN_US:
            raise InvalidArgument(message="Only EN_US subtitles can be updated")

        # 检查是否已经存在同样语言的字幕
        old_subtitle = VideoSubtitle.query.filter_by(video_id=video.id, lang=subtitle_lang).first()
        if old_subtitle:
            db.session.delete(old_subtitle)
            db.session.flush()

        # 创建新的字幕
        new_subtitle = VideoSubtitle(
            video_id=video.id,
            lang=subtitle_lang,
            file_id=subtitle_id,
            file_key=subtitle_key
        )
        db.session.add(new_subtitle)
        db.session.commit()

        _translate_video_subtitle(video.id, subtitle_key)

        # 记录操作日志
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.BusinessVideo,
            old_data=dict(subtitle=old_subtitle.to_dict() if old_subtitle else None),
            new_data=dict(subtitle=new_subtitle.to_dict()),
        )
        return old_subtitle, new_subtitle

    @classmethod
    @ns.use_kwargs(dict(
        platform=EnumField(Video.Platform, required=False, missing=None),
        business=EnumField(Video.Business, required=False, missing=None),
        subtitle=fields.Dict(required=False, default=None, missing=None),
    ))
    def put(cls, video_id, **kwargs):
        """业务视频管理-分配展示位置"""
        params = Struct(**kwargs)
        video = Video.query.get(video_id)
        if not video:
            raise RecordNotFound(message=f"Video record not found for id {video_id}")
        if params.business and video.business == params.business and params.platform and params.platform == video.platform:
            raise InvalidArgument(message="Video business and platform is already set to this value")

        if params.business or params.platform:
            old_video = cls._assign_business(video, params.business, params.platform)
            # 返回被替换掉的视频id
            return dict(
                old_video_id=old_video.id if old_video else None,  # 替换旧视频的id
            )
        elif params.subtitle:
            old_subtitle, new_subtitle = cls._update_subtitle(video, params.subtitle)
            # 返回被替换掉的字幕信息
            return dict(
                old_subtitle_id=old_subtitle.id if old_subtitle else None,  # 替换旧字幕的id
                new_subtitle_id=new_subtitle.id
            )
        else:
            raise InvalidArgument(message="Either business or subtitle must be provided for update")


@ns.route('/video/<video_id>/translate')
@respond_with_code
class MediaVideoTranslateResource(Resource):
    @classmethod
    def post(cls, video_id):
        """业务视频管理-分配展示位置"""
        video = Video.query.get(video_id)
        if not video:
            raise RecordNotFound(message=f"Video record not found for id {video_id}")

        source_subtitle = VideoSubtitle.query.filter_by(video_id=video_id, lang=Language.EN_US).first()
        if not source_subtitle:
            raise RecordNotFound(message=f"Source subtitle not found for video id {video_id}")

        _translate_video_subtitle(video_id, source_subtitle.file_key)
