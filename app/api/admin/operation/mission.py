from collections import defaultdict
import json
from datetime import timedelta
from enum import Enum
from typing import Dict

from flask import g
from marshmallow import Schema, EXCLUDE
from sqlalchemy import func
from webargs import fields

from app import Language
from app.api.common import Resource, Namespace, respond_with_code
from app.api.common.fields import TimestampField, EnumField, PageField, LimitField
from app.business import get_admin_user_name_map
from app.business.equity_center.helper import EquityCenterService
from app.business.mission_center.group import MissionGroupBiz
from app.business.mission_center.mission import MissionBiz, SceneHandler
from app.business.mission_center.plan import MissionPlanBiz
from app.business.push_statistic import UserTagGroupBiz
from app.caches.mission import MissionCache
from app.common import language_name_cn_names, get_country, list_country_codes_3_admin, ADMIN_EXPORT_LIMIT, \
    BusinessParty
from app.exceptions import RecordNotFound, InvalidArgument
from app.models import db, User, LoginRelationHistory
from app.models.equity_center import EquityType, UserEquity
from app.models.mission_center import MissionScene, SceneType, LogicTemplate, MissionCondition, MissionPlan, \
    MissionPlanUserGroup, \
    Mission, UserMission, DailyMissionStatistics
from app.utils import validate_email, export_xlsx, amount_to_str, datetime_to_utc8_str, now
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from app.utils.parser import JsonEncoder

ns = Namespace('Mission')


class MissionSchema(Schema):
    id = fields.Integer(allow_none=True)
    equity_id = fields.Integer(allow_none=True)
    asset = fields.String(allow_none=True)
    amount = fields.Decimal(allow_none=True)
    sequence = fields.Integer(required=True)
    deadline_days = fields.Integer(required=True)
    reward_type = fields.String(required=True)
    mission_condition = EnumField(MissionCondition, required=True)
    logic_params = fields.Dict(keys=fields.String, values=fields.Raw, required=True)

    class Meta:
        UNKNOWN = EXCLUDE


class ChannelTypes(Enum):
    NORMAL = '普通注册'
    REFERER = '邀请注册'
    POOL = '矿池注册'


LOGIC_TEMPLATE_CHANNEL_MAPPER = {
    LogicTemplate.CHANNEL_ID_EQ: ChannelTypes.POOL,
    LogicTemplate.REFERER_ID_IN: ChannelTypes.REFERER,
    LogicTemplate.REGISTRATION_AREA_IN: ChannelTypes.NORMAL
}

CHANNEL_LOGIC_MAPPER = {
    channel: temp for temp, channel in LOGIC_TEMPLATE_CHANNEL_MAPPER.items()
}


class MissionResourceMixin:
    CREATED_UPDATED_PARAMS = dict(
        name=fields.String(required=True),
        channel=EnumField(ChannelTypes),
        scene_type=EnumField(SceneType, required=True),
        scene_id=fields.Integer(allow_none=True),
        start_at=TimestampField(required=True, is_ms=True),
        end_at=TimestampField(is_ms=True, allow_none=True),
        total=fields.Integer(allow_none=True),
        deliver_content=fields.Dict(keys=EnumField(
            [i.name for i in Language]), values=fields.String),
        logic_params=fields.Dict(keys=EnumField(
            [i.name for i in LogicTemplate]), values=fields.Raw),
        missions=fields.Nested(MissionSchema, many=True, required=True),
        business_party=EnumField(BusinessParty, required=True),
        whitelist_enabled=fields.Boolean(allow_none=True),
        whitelist_user_ids=fields.String(allow_none=True),
        group_ids=fields.List(fields.Integer, allow_none=True)
    )

    @classmethod
    def validate_newbie_params(cls, params: dict):
        if not (channel := params.get('channel')):
            raise InvalidArgument(message="新手任务不能指定渠道")
        if not (logic_params := params.get('logic_params')):
            raise InvalidArgument(message="新手任务不能指定逻辑参数")
        match channel:
            case ChannelTypes.REFERER:
                logic_value: str = logic_params.get(LogicTemplate.REFERER_ID_IN.name)
                if not logic_value:
                    raise InvalidArgument(message=f"{channel.value} params value error: {logic_value}")
                logic_list = logic_value.strip().replace("，", ",").split(",")
                id_set, email_set = set(), set()
                for id_or_email in logic_list:
                    id_or_email = id_or_email.strip()
                    if id_or_email.isdigit():
                        id_set.add(int(id_or_email))
                        continue
                    if not validate_email(id_or_email):
                        raise InvalidArgument(message=f"{channel.value} 参数错误, {id_or_email} 并不是有效的用户邮箱")
                    email_set.add(id_or_email)
                email_user_mapper = {
                    e: i
                    for i, e in User.query.filter(
                        User.email.in_(email_set)
                    ).with_entities(
                        User.id,
                        User.email
                    ).all()
                }
                user_ids = {i.id for i in User.query.filter(User.id.in_(id_set)).with_entities(User.id).all()}
                not_email = email_set - set(email_user_mapper.keys())
                not_user_ids = id_set - user_ids
                if not_email:
                    raise InvalidArgument(
                        message=f"{channel.value} 参数错误, 无效用户邮箱: {','.join(not_email)}"
                    )
                if not_user_ids:
                    raise InvalidArgument(
                        message=f"{channel.value} 参数错误, 无效用户ID{','.join((map(str, not_user_ids)))}"
                    )
                logic_params[LogicTemplate.REFERER_ID_IN.name] = logic_list
            case ChannelTypes.POOL:
                logic_params[LogicTemplate.CHANNEL_ID_EQ.name] = MissionPlanBiz.POOL_CHANNEL
            case ChannelTypes.NORMAL:
                if not logic_params:
                    logic_params[LogicTemplate.REGISTRATION_AREA_IN.name] = []

        params["logic_params"] = logic_params
        params["logic_template"] = CHANNEL_LOGIC_MAPPER[channel]
        return params

    @classmethod
    def validate_routine_params(cls, params: dict, id_: int = None):
        name = params['name']
        if not (scene_id := params.get('scene_id')):
            raise InvalidArgument(message="老用户任务必须指定场景ID")
        if not params.get('group_ids'):
            raise InvalidArgument(message="老用户任务必须指定用户画像分组")
        cls.validate_routine_scene_name(name, scene_id, id_)
        whitelist_enabled = params.get('whitelist_enabled')
        if whitelist_enabled:
            if not (whitelist_user_ids := params.get('whitelist_user_ids')):
                raise InvalidArgument(message="请上传白名单用户ID")
            user_ids = list(map(int, whitelist_user_ids.split(',')))
            params['whitelist_user_ids'] = user_ids
        else:
            params['whitelist_user_ids'] = []
        return params

    @classmethod
    def validate_routine_scene_name(cls, name: str, scene_id: int, id_: int = None):
        plan = MissionPlan.query.filter(
            MissionPlan.status != MissionPlan.Status.DELETED,
            MissionPlan.scene_type == SceneType.ROUTINE,
            MissionPlan.name == name,
            MissionPlan.scene_id == scene_id
        ).first()
        if plan and plan.id != id_:
            raise InvalidArgument(message="存在相同场景，名称的推送")


    @classmethod
    def update_params(cls, params: dict, id_: int = None):
        scene_type = params['scene_type']
        now_ = now()
        if (start_at := params['start_at']) < now_:
            raise InvalidArgument(message="开始时间不能小于当前时间")
        if (end_at := params.get("end_at")) and end_at < start_at:
            raise InvalidArgument(message="结束时间不能小于开始时间")
        match scene_type:
            case SceneType.NEWBIE:
                params = cls.validate_newbie_params(params)
            case SceneType.ROUTINE:
                params = cls.validate_routine_params(params, id_)
        return params

    @classmethod
    def get_all_plan_name_mapper(cls, scene_type: SceneType = SceneType.NEWBIE) -> dict:
        return {
            i.id: f"{i.id} {i.scene_type.value} {i.name}" for i in MissionPlan.query.filter(
                MissionPlan.status != MissionPlan.Status.DELETED,
                MissionPlan.scene_type == scene_type
            ).order_by(MissionPlan.id.desc()).with_entities(
                MissionPlan.id,
                MissionPlan.name,
                MissionPlan.scene_type
            ).all()
        }

    @classmethod
    def get_plan_name_mapper(cls, plan_ids: set[int]):
        return {
            i.id: f"{i.name}" for i in MissionPlan.query.filter(
                MissionPlan.id.in_(plan_ids)
            ).with_entities(
                MissionPlan.id,
                MissionPlan.name
            ).all()
        }

    @classmethod
    def get_reward_name_by_cache_data(cls, cache_data: dict):
        if not cache_data:
            return ""
        reward_data = cache_data['reward']
        match reward_type := reward_data['reward_type']:
            case EquityType.AIRDROP.name:
                return f"{EquityType[reward_type].value} ({reward_data['value_type']})"
            case EquityType.CASHBACK.name:
                return f"{EquityType[reward_type].value} (ID: {cache_data['equity_id']})"
            case _:
                return ""

    @classmethod
    def get_mission_name_mapper(cls):
        return {
            k: f'{k}  {MissionBiz.build_title(v)}' for k, v in
            MissionCache.get_all_cache_data().items()
        }

    @classmethod
    def get_unreachable_users(cls, scene_id):
        plan_ids = {
            i.id for i in MissionPlan.query.filter(
                MissionPlan.scene_id == scene_id,
                MissionPlan.status != MissionPlan.Status.DELETED
            ).with_entities(
                MissionPlan.id
            ).all()
        }
        groups = MissionPlanUserGroup.query.filter(
            MissionPlanUserGroup.plan_id.in_(plan_ids),
            MissionPlanUserGroup.group_type == MissionPlanUserGroup.GroupType.USER_TAG
        ).all()
        unreachable_users = set()
        plan_users_mapper = {}
        for group in groups:
            users = group.get_distributed_users()
            plan_users_mapper[group.plan_id] = users
            unreachable_users.update(users)
        return unreachable_users, plan_users_mapper

    @classmethod
    def get_plan_statistics_mapper(cls, plan_ids: list[int]):
        statistics_user_mapper = defaultdict(lambda: {
            "total_user_ids": set(),
            "success_user_ids": set(),
            "failed_user_ids": set(),
            "effective_user_ids": set(),
        })
        rows = UserMission.query.filter(
            UserMission.plan_id.in_(plan_ids)
        ).with_entities(
            UserMission.plan_id,
            UserMission.status,
            UserMission.user_id
        ).all()
        for row in rows:
            statistics_user_mapper[row.plan_id]["total_user_ids"].add(row.user_id)
            if row.status == UserMission.Status.PENDING:
                statistics_user_mapper[row.plan_id]["effective_user_ids"].add(row.user_id)
            if row.status == UserMission.Status.FAILED:
                statistics_user_mapper[row.plan_id]["failed_user_ids"].add(row.user_id)
            else:
                statistics_user_mapper[row.plan_id]["success_user_ids"].add(row.user_id)
        return {
            pid: {
                "total_count": len(data["total_user_ids"]),
                "success_count": len(data["success_user_ids"]),
                "failed_count": len(data["failed_user_ids"]),
                "effective_count": len(data["effective_user_ids"]),
            }
            for pid, data in statistics_user_mapper.items()
        }


@ns.route('')
@respond_with_code
class MissionPlanListResource(Resource, MissionResourceMixin):

    @classmethod
    @ns.use_kwargs(dict(
        scene_type=EnumField(SceneType, missing=SceneType.NEWBIE),
        plan_id=fields.Integer(allow_none=True),
        status=EnumField(MissionPlan.Status),
        channel=EnumField(ChannelTypes),
        business_party=EnumField(BusinessParty),
        page=PageField(missing=-1),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-任务中心-新手任务推送列表"""
        page, limit = kwargs['page'], kwargs['limit']
        scene_type = kwargs['scene_type']
        query = MissionPlan.query.filter(
            MissionPlan.status != MissionPlan.Status.DELETED,
            MissionPlan.scene_type == scene_type
        )
        if plan_id := kwargs.get('plan_id'):
            query = query.filter(MissionPlan.id == plan_id)
        if status := kwargs.get('status'):
            statuses = [status, MissionPlan.Status.PASSED] if status == MissionPlan.Status.EFFECTIVE else [status]
            query = query.filter(
                MissionPlan.status.in_(statuses)
            )
        if business_party := kwargs.get('business_party'):
            query = query.filter(MissionPlan.business_party == business_party)
        if channel := kwargs.get('channel'):
            plan_ids = {
                mpg.plan_id for mpg in MissionPlanUserGroup.query.filter(
                    MissionPlanUserGroup.scene_type == SceneType.NEWBIE,
                    MissionPlanUserGroup.logic_template == CHANNEL_LOGIC_MAPPER[channel]
                ).with_entities(
                    MissionPlanUserGroup.plan_id
                ).all()
            }
            query = query.filter(
                MissionPlan.id.in_(plan_ids)
            )
        paginate = query.order_by(
            MissionPlan.id.desc()
        ).with_entities(
            MissionPlan.id,
            MissionPlan.name,
            MissionPlan.status,
            MissionPlan.scene_type,
            MissionPlan.total,
            MissionPlan.created_by,
            MissionPlan.auditor_by,
            MissionPlan.business_party,
            MissionPlan.updated_at
        ).paginate(page, limit, error_out=False)
        plan_ids = [i.id for i in paginate.items]
        mission_count_mapper = {
            pid: count for pid, count in
            Mission.query.filter(
                Mission.plan_id.in_(plan_ids),
            ).group_by(
                Mission.plan_id
            ).with_entities(
                Mission.plan_id,
                func.count(Mission.id)
            )
        }
        plan_statistics_mapper = cls.get_plan_statistics_mapper(plan_ids)
        created_bys = [i.created_by for i in paginate.items]
        auditor_bys = [i.auditor_by for i in paginate.items]
        user_name_mapper = get_admin_user_name_map(set(created_bys + auditor_bys))
        plan_group_mapper = {
            group.plan_id: group for group in
            MissionPlanUserGroup.query.filter(
                MissionPlanUserGroup.plan_id.in_(plan_ids)
            ).with_entities(
                MissionPlanUserGroup.plan_id,
                MissionPlanUserGroup.logic_template,
            ).all()
        }
        items = []
        for item in paginate.items:
            group = plan_group_mapper.get(item.id)
            if not group:
                continue
            logic_template = group.logic_template
            statistics_data = plan_statistics_mapper.get(item.id, {})
            success_current_count = statistics_data.get("success_count", 0)
            failed_current_count = statistics_data.get("failed_count", 0)
            current_count = success_current_count + failed_current_count
            items.append(dict(
                id=item.id,
                scene_type=item.scene_type.value,
                name=item.name,
                total=item.total if item.total else "无上限",
                current_count=current_count,
                success_count=success_current_count,
                failed_count=failed_current_count,
                mission_count=mission_count_mapper.get(item.id, 0),
                status=item.status.name,
                created_by=item.created_by,
                updated_at=item.updated_at,
                auditor_by=item.auditor_by,
                auditor_by_name=user_name_mapper.get(item.auditor_by, item.auditor_by),
                created_by_name=user_name_mapper.get(item.created_by, item.created_by),
                channel=LOGIC_TEMPLATE_CHANNEL_MAPPER[logic_template].value,
                business_party=item.business_party.value
            ))

        return dict(
            total=paginate.total,
            items=items,
            channels=ChannelTypes,
            statuses={s.name: s.value for s in MissionPlan.Status
                      if s not in [MissionPlan.Status.DELETED]},
            plan_id_mapper=cls.get_all_plan_name_mapper(),
            business_parties=BusinessParty,
        )

    @classmethod
    @ns.use_kwargs(MissionResourceMixin.CREATED_UPDATED_PARAMS)
    def post(cls, **kwargs):
        """运营-任务中心-创建新手任务推送"""
        user_id = g.user.id
        params = cls.update_params(kwargs)
        plan_id = MissionPlanBiz.create(params, user_id)
        AdminOperationLog.new_add(
            user_id=user_id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            detail=kwargs,
        )
        return dict(id=plan_id)


@ns.route('/<int:id_>')
@respond_with_code
class MissionPlanDetailResource(Resource, MissionResourceMixin):

    @classmethod
    def get(cls, id_):
        """运营-任务中心-获取新手任务推送详情"""
        base_dict = dict(
            scene_types=SceneType,
            languages=language_name_cn_names(),
            conditions=MissionCondition,
            channels=ChannelTypes,
            countries={code: get_country(
                code).cn_name for code in list_country_codes_3_admin()},
            rewards=EquityCenterService.get_all_equity_dict(),
            business_parties=BusinessParty,
        )
        if not id_:
            return base_dict
        mission_plain_dict = MissionPlanBiz.get_info(id_)
        mission_plain_dict['channel'] = LOGIC_TEMPLATE_CHANNEL_MAPPER[mission_plain_dict['logic_template']].name
        mission_plain_dict['is_self_record'] = g.user.id == mission_plain_dict['created_by']
        mission_plain_dict.update(base_dict)

        return mission_plain_dict

    @classmethod
    @ns.use_kwargs(MissionResourceMixin.CREATED_UPDATED_PARAMS)
    def put(cls, id_, **kwargs):
        """运营-任务中心-修改新手任务推送详情"""
        old_plan = MissionPlan.query.get(id_)
        if not old_plan:
            raise RecordNotFound
        old_data = old_plan.to_dict(enum_to_name=True)
        params = cls.update_params(kwargs, id_)
        plan = MissionPlanBiz.update(id_, params, g.user.id)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            old_data=old_data,
            new_data=plan.to_dict(enum_to_name=True),
            special_data=dict(plan_id=id_),
        )
        return dict(id=id_)

    @classmethod
    @ns.use_kwargs(MissionResourceMixin.CREATED_UPDATED_PARAMS)
    def patch(cls, id_, **kwargs):
        """运营-任务中心-新手任务-提交审核"""
        params = cls.update_params(kwargs, id_)
        admin_user_id = g.user.id
        if not id_:
            id_ = MissionPlanBiz.create(params, admin_user_id)
        else:
            MissionPlanBiz.update(id_, params, admin_user_id)
        MissionPlanBiz.submit(id_)
        AdminOperationLog.new_audit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            detail=dict(id=id_, name=kwargs['name']),
        )
        return dict(id=id_)

    @classmethod
    def delete(cls, id_):
        """运营-任务中心-新手任务-删除推送"""
        MissionPlanBiz.delete(id_)
        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            detail=dict(id=id_),
        )


@ns.route('/<int:id_>/review')
@respond_with_code
class MissionPlanReviewResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(MissionPlan.Status, required=True)
    ))
    def post(cls, id_, **kwargs):
        """运营-任务中心-新手任务-审核通过/拒绝审核"""
        status = kwargs["status"]
        plan: MissionPlan = MissionPlan.query.get(id_)
        if not plan:
            raise RecordNotFound
        old_data = plan.to_dict(enum_to_name=True)
        admin_user_id = g.user.id
        if plan.created_by == admin_user_id:
            raise InvalidArgument(message="审核和创建不能是同一个人。")
        if plan.status != MissionPlan.Status.PENDING:
            raise InvalidArgument
        if status not in [MissionPlan.Status.PASSED, MissionPlan.Status.REJECTED]:
            raise InvalidArgument
        if status == MissionPlan.Status.PASSED:
            if plan.start_at + timedelta(minutes=2) <= now():
                MissionPlanBiz.rejected(id_)
                raise InvalidArgument(
                    message="审核已超时，请修改推送时间重新提交", data=dict(id=id_))
            else:
                MissionPlanBiz.audit_pass(id_)
        elif status == MissionPlan.Status.REJECTED:
            MissionPlanBiz.rejected(id_)
        plan.auditor_by = admin_user_id
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=admin_user_id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            old_data=old_data,
            new_data=plan.to_dict(enum_to_name=True),
            special_data=dict(plan_id=id_),
        )
        return dict(id=id_)


@ns.route('/<int:id_>/stop')
@respond_with_code
class MissionPlanStopResource(Resource):

    @classmethod
    def post(cls, id_, **kwargs):
        """运营-任务中心-新手任务-暂停推送"""
        MissionPlanBiz.stop(id_)
        AdminOperationLog.new_stop(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            detail=dict(id=id_),
        )


@ns.route("/<int:id_>/update-content")
@respond_with_code
class MissionPlanUpdateContentResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        deliver_content=fields.Dict(keys=EnumField(
            [i.name for i in Language]), values=fields.String, required=True)
    ))
    def put(cls, id_, **kwargs):
        """运营-任务中心-新手任务-更新推送内容"""
        old_plan = MissionPlan.query.get(id_)
        if not old_plan:
            raise RecordNotFound
        deliver_content = kwargs["deliver_content"]
        old_deliver_content = old_plan.deliver_content
        plan = MissionPlanBiz.update_deliver_content(id_, deliver_content)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.NewBieMissionPlan,
            old_data=old_deliver_content,
            new_data=deliver_content,
            special_data=dict(plan_id=id_),
        )
        return dict(id=plan.id)


@ns.route('/user-mission')
@respond_with_code
class UserMissionInfoResource(Resource, MissionResourceMixin):
    export_headers = (
        {"field": "id", Language.ZH_HANS_CN: "推送ID"},
        {"field": "plan_name", Language.ZH_HANS_CN: "推送名称"},
        {"field": "mission_id", Language.ZH_HANS_CN: "任务ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "push_status", Language.ZH_HANS_CN: "推送状态"},
        {"field": "created_at", Language.ZH_HANS_CN: "推送时间"},
        {"field": "mission_condition", Language.ZH_HANS_CN: "任务类型"},
        {"field": "status", Language.ZH_HANS_CN: "任务进行状态"},
        {"field": "completed_at", Language.ZH_HANS_CN: "达标时间"},
        {"field": "reward_name", Language.ZH_HANS_CN: "任务奖励"},
        {"field": "reward_status", Language.ZH_HANS_CN: "奖励发放状态"},
        {"field": "reward_amount", Language.ZH_HANS_CN: "应发奖励"},
        {"field": "reward_real_amount", Language.ZH_HANS_CN: "实发奖励"},
        {"field": "reward_created_at", Language.ZH_HANS_CN: "发放时间"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间"},
        {"field": "fail_reason", Language.ZH_HANS_CN: "失败原因"},
    )

    class MissionPushStatus(Enum):
        FAILED = '推送失败'
        SUCCEEDED = '推送成功'

    class MissionRewardStatus(Enum):
        NOT_REWARDED = "未发放"
        NORMAL_REWARDED = "正常发放"
        ABNORMAL_REWARDED = "异常发放"

    # 添加新的常量=
    MAX_ASYNC_DOWNLOAD_LIMIT = 500000  # 最大异步下载限制

    @classmethod
    def get_reward_status(cls, user_mission_status: UserMission.Status, equity_status: UserEquity.Status):
        if user_mission_status != UserMission.Status.FINISHED:
            return cls.MissionRewardStatus.NOT_REWARDED
        if equity_status in EquityCenterService.FAILED_STATUS:
            return cls.MissionRewardStatus.ABNORMAL_REWARDED
        return cls.MissionRewardStatus.NORMAL_REWARDED

    @classmethod
    def get_push_status(cls, user_mission_status: UserMission.Status):
        if user_mission_status == UserMission.Status.FAILED:
            return cls.MissionPushStatus.FAILED
        return cls.MissionPushStatus.SUCCEEDED

    @classmethod
    def get_row_waring_flag(cls, reward_info: dict):
        if not reward_info:
            return False
        if reward_info['status'] in EquityCenterService.FAILED_STATUS:
            return True
        return reward_info['cost_amount'] != reward_info['real_amount']

    @classmethod
    def get_fail_reason(cls, equity_status):
        if equity_status == EquityCenterService.UserEquityStatus.FAILED:
            return UserMission.FailReason.RISK_USER
        if equity_status == EquityCenterService.UserEquityStatus.CREATED:
            return UserMission.FailReason.SETTLING_ERROR
        return None

    @classmethod
    def build_user_mission_query(cls, scene_type, **kwargs):
        """构建用户任务查询的通用方法"""
        query = UserMission.query.filter(
            UserMission.scene_type == scene_type
        )
        
        if user_id := kwargs.get('user_id'):
            query = query.filter(UserMission.user_id == user_id)
        if plan_id := kwargs.get('plan_id'):
            query = query.filter(UserMission.plan_id == plan_id)
        if mission_id := kwargs.get('mission_id'):
            query = query.filter(UserMission.mission_id == mission_id)
        if status := kwargs.get('status'):
            query = query.filter(UserMission.status == status)
        if push_status := kwargs.get('push_status'):
            if push_status == cls.MissionPushStatus.FAILED:
                query = query.filter(UserMission.status == UserMission.Status.FAILED)
            else:
                query = query.filter(UserMission.status != UserMission.Status.FAILED)
        if reward_type := kwargs.get('reward_type'):
            mission_ids = MissionCache.get_mission_by_reward_type(reward_type.name)
            query = query.filter(UserMission.mission_id.in_(mission_ids))
        if reward_status := kwargs.get('reward_status'):
            match reward_status:
                case cls.MissionRewardStatus.NOT_REWARDED:
                    query = query.filter(UserMission.status != UserMission.Status.FINISHED)
                case cls.MissionRewardStatus.NORMAL_REWARDED:
                    biz_ids = EquityCenterService.query_biz_id_by_biz_type_status(
                        EquityCenterService.BizTypes.MISSION,
                        EquityCenterService.UserEquityStatus.FAILED
                    )
                    query = query.filter(
                        UserMission.id.notin_(biz_ids),
                        UserMission.status == UserMission.Status.FINISHED
                    )
                case cls.MissionRewardStatus.ABNORMAL_REWARDED:
                    failed_biz_ids = EquityCenterService.query_biz_id_by_biz_type_status(
                        EquityCenterService.BizTypes.MISSION,
                        EquityCenterService.UserEquityStatus.FAILED
                    )
                    created_biz_ids = EquityCenterService.query_biz_id_by_biz_type_status(
                        EquityCenterService.BizTypes.MISSION,
                        EquityCenterService.UserEquityStatus.CREATED
                    )
                    query = query.filter(UserMission.id.in_(failed_biz_ids | created_biz_ids))
        if created_at_start := kwargs.get('created_at_start'):
            query = query.filter(UserMission.created_at >= created_at_start)
        if created_at_end := kwargs.get('created_at_end'):
            query = query.filter(UserMission.created_at <= created_at_end)
        if completed_at_start := kwargs.get('completed_at_start'):
            query = query.filter(UserMission.completed_at >= completed_at_start)
        if completed_at_end := kwargs.get('completed_at_end'):
            query = query.filter(UserMission.completed_at <= completed_at_end)
            
        return query.order_by(UserMission.plan_id.desc(), UserMission.id.desc())

    @classmethod
    def process_user_mission_items(cls, items):
        """处理用户任务数据项的通用方法"""
        biz_ids = {i.id for i in items}
        plan_ids = {i.plan_id for i in items}
        mission_ids = {i.mission_id for i in items}
        mission_info_mapper = MissionCache.get_cache_data_by_ids(list(mission_ids))
        reward_info_mapper = EquityCenterService.batch_query_user_eq_info(EquityCenterService.BizTypes.MISSION, biz_ids)
        plan_name_mapper = cls.get_plan_name_mapper(plan_ids)
        
        processed_items = []
        for item in items:
            reward_info = reward_info_mapper.get(item.id, {})
            waring_flag = cls.get_row_waring_flag(reward_info)
            origin_reward_status = reward_info.get("status")
            new_status = cls.get_reward_status(item.status, origin_reward_status)
            reward_info['origin_status'] = origin_reward_status.name if origin_reward_status else None
            reward_info['status'] = new_status
            mission_info = mission_info_mapper.get(item.mission_id, {})
            reward_info.update(**mission_info.get("reward", {}))
            processed_items.append(dict(
                id=item.id,
                plan_name=plan_name_mapper.get(item.plan_id),
                user_id=item.user_id,
                plan_id=item.plan_id,
                equity_id=mission_info.get("equity_id"),
                mission_id=item.mission_id,
                status=item.status.value,
                push_status=cls.get_push_status(item.status).value,
                reward_name=cls.get_reward_name_by_cache_data(mission_info),
                reward=reward_info,
                created_at=item.used_at if item.used_at != UserMission.MIN_UTC_DATETIME else item.created_at,
                updated_at=item.last_updated_at or item.updated_at,
                completed_at=item.completed_at,
                mission_condition=item.mission_condition.value,
                fail_reason=cls.get_fail_reason(origin_reward_status),
                waring_flag=waring_flag,
            ))
        return processed_items

    @classmethod
    def build_export_items(cls, items):
        """构建导出数据项的通用方法"""
        export_items = []
        for item in items:
            reward = item['reward']
            reward_created_at = reward.get('created_at')
            export_items.append({
                "id": item['plan_id'],
                "user_id": item["user_id"],
                "plan_name": item["plan_name"],
                "mission_id": item["mission_id"],
                "status": "--" if item["status"] == '已失败' else item["status"],
                "push_status": item["push_status"],
                "reward_name": item["reward_name"],
                "reward_status": reward.get("status").value,
                "reward_amount": f'{amount_to_str(reward.get("cost_amount", 0))} {reward["value_type"]}',
                "reward_real_amount": f'{amount_to_str(reward.get("real_amount", 0))} {reward["value_type"]}',
                "reward_created_at": datetime_to_utc8_str(reward_created_at) if reward_created_at else None,
                "updated_at": datetime_to_utc8_str(item["updated_at"]),
                "completed_at": datetime_to_utc8_str(item["completed_at"]) if item["completed_at"] else "",
                "mission_condition": item["mission_condition"],
                "created_at": datetime_to_utc8_str(item['created_at']),
                "fail_reason": item["fail_reason"].value if item["fail_reason"] else ""
            })
        return export_items

    @classmethod
    @ns.use_kwargs(dict(
        scene_type=EnumField(SceneType, missing=SceneType.NEWBIE),
        user_id=fields.Integer,
        plan_id=fields.Integer,
        mission_id=fields.Integer,
        status=EnumField(UserMission.Status),
        push_status=EnumField(MissionPushStatus),
        reward_type=EnumField(EquityType),
        reward_status=EnumField(MissionRewardStatus),
        cursor=fields.Integer(missing=None),
        created_at_start=TimestampField,
        created_at_end=TimestampField,
        completed_at_start=TimestampField,
        completed_at_end=TimestampField,
        page=PageField(missing=1),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """运营-任务中心-新手任务明细列表"""
        cursor, limit = kwargs.get("cursor"), kwargs['limit']
        scene_type = kwargs.pop('scene_type')
        
        # 构建查询
        query = cls.build_user_mission_query(scene_type, **kwargs)
        
        if kwargs.get('export'):
            # 检查总数据量
            total_count = query.count()
            
            # 如果数据量超过阈值，使用异步下载
            if total_count > ADMIN_EXPORT_LIMIT:
                # 检查是否超过最大异步下载限制
                if total_count > cls.MAX_ASYNC_DOWNLOAD_LIMIT:
                    raise InvalidArgument(
                        message=f"数据量过大，异步下载限制为{cls.MAX_ASYNC_DOWNLOAD_LIMIT}条，当前数据量为{total_count}条"
                    )
                
                # 启动异步下载任务
                from app.schedules.reports.admin_async_download import async_download_user_mission_report
                async_download_user_mission_report.delay(
                    email=g.user.email,
                    scene_type=scene_type.name,
                    params=json.dumps(
                        {k: v.name if isinstance(v, Enum) else v for k, v in kwargs.items()
                         if k not in ['export', 'page', 'limit', 'cursor']},
                        cls=JsonEncoder
                    )
                )
                raise InvalidArgument(message="数据量较大，已启动异步下载，请稍后查收邮件")
            else:
                # 使用同步下载
                paginate = query.cursor_paginate(None, ADMIN_EXPORT_LIMIT)
        else:
            paginate = query.cursor_paginate(cursor, limit)
            
        # 处理数据项
        items = cls.process_user_mission_items(paginate.items)
        
        if kwargs.get('export'):
            export_items = cls.build_export_items(items)
            return export_xlsx(
                filename='user_mission_list',
                data_list=export_items,
                export_headers=cls.export_headers
            )
        return dict(
            cursor=paginate.cursor,
            has_next=paginate.has_next,
            items=items,
            extra_data=dict(
                statuses={u.name: u.value for u in UserMission.Status if u != UserMission.Status.FAILED},
                reward_types=EquityType,
                reward_statuses=cls.MissionRewardStatus,
                push_status=cls.MissionPushStatus,
                plan_id_mapper=cls.get_all_plan_name_mapper(scene_type),
                mission_id_mapper=cls.get_mission_name_mapper()
            )
        )


@ns.route('/failed-users')
@respond_with_code
class MissionFailedUsersResource(Resource):

    @classmethod
    def get_user_email_dict(cls, user_ids: set[int]) -> Dict[int, str]:
        user_query = User.query.filter(
            User.id.in_(user_ids)
        ).with_entities(
            User.id,
            User.email
        ).all()
        return {i.id: i.email for i in user_query}

    @classmethod
    @ns.use_kwargs(dict(
        plan_id=fields.Integer(required=True),
        page=PageField(missing=1),
        limit=LimitField(missing=50),
        user_id=fields.Integer,
    ))
    def get(cls, **kwargs):
        """运营-任务中心-失败用户列表"""
        plan_id = kwargs['plan_id']
        plan = MissionPlan.query.get(plan_id)
        if not plan:
            raise RecordNotFound
        query = UserMission.query.filter(
            UserMission.plan_id == plan_id,
            UserMission.status == UserMission.Status.FAILED
        )
        if user_id := kwargs.get('user_id'):
            query = query.filter(UserMission.user_id == user_id)

        query = query.group_by(
            UserMission.user_id
        ).with_entities(
            UserMission.user_id,
            func.min(UserMission.created_at).label('created_at'),
        )
        paginate = query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        failed_users = paginate.items
        user_ids = {i.user_id for i in failed_users}
        if plan.scene_type == SceneType.NEWBIE:
            user_device_mapper = LoginRelationHistory.query_duplicate_device_users(user_ids, is_get_device_mapper=True)
        else:
            group = MissionPlanUserGroup.query.filter(
                MissionPlanUserGroup.plan_id == plan_id
            ).first()
            if not group:
                raise RecordNotFound
            failed_users_mapper = group.get_failed_users_mapper()
            user_device_mapper = {}
            for user_id, failed_user_data in failed_users_mapper.items():
                if device_id := failed_user_data.get('fail_data', {}).get('device_id'):
                    user_device_mapper[user_id] = device_id

        user_email_mapper = cls.get_user_email_dict(user_ids)
        return dict(
            total=paginate.total,
            items=[
                dict(
                    user_id=user_id,
                    email=user_email_mapper.get(user_id),
                    created_at=created_at,
                    plan_id=plan_id,
                    title=plan.name,
                    fail_reason="设备相同",  # 现阶段 任务 失败只有这一个原因。
                    device_id=user_device_mapper.get(user_id)
                ) for user_id, created_at in failed_users
            ]
        )


@ns.route('/statistics')
@respond_with_code
class MissionStatisticsResource(Resource, MissionResourceMixin):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "统计日期"},
        {"field": "plan_id", Language.ZH_HANS_CN: "推送ID"},
        {"field": "plan_name", Language.ZH_HANS_CN: "推送名称"},
        {"field": "mission_id", Language.ZH_HANS_CN: "任务ID"},
        {"field": "mission_condition", Language.ZH_HANS_CN: "任务类型"},
        {"field": "delivery_count", Language.ZH_HANS_CN: "推送成功用户数"},
        {"field": "completion_count", Language.ZH_HANS_CN: "达标用户数"},
        {"field": "finished_count", Language.ZH_HANS_CN: "已发奖用户数"},
        {"field": "reward_type", Language.ZH_HANS_CN: "任务奖励"},
        {"field": "reward_amount", Language.ZH_HANS_CN: "应发奖励"},
        {"field": "real_reward_amount", Language.ZH_HANS_CN: "实发奖励"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        scene_type=EnumField(SceneType, missing=SceneType.NEWBIE),
        report_date_start=fields.Date,
        report_date_end=fields.Date,
        plan_id=fields.Integer,
        mission_id=fields.Integer,
        reward_type=EnumField(EquityType),
        mission_condition=EnumField(MissionCondition),
        limit=LimitField(missing=50),
        page=PageField(missing=1),
        export=fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """运营-任务中心-新手任务统计列表"""
        page, limit = kwargs['page'], kwargs['limit']
        scene_type = kwargs['scene_type']
        scene_plans_ids = {i.id for i in MissionPlan.query.filter(
            MissionPlan.scene_type == scene_type
        ).with_entities(
            MissionPlan.id
        ).all()}
        query = DailyMissionStatistics.query.filter(
            DailyMissionStatistics.plan_id.in_(scene_plans_ids)
        )
        if report_date_start := kwargs.get('report_date_start'):
            query = query.filter(DailyMissionStatistics.report_date >= report_date_start)
        if report_date_end := kwargs.get('report_date_end'):
            query = query.filter(DailyMissionStatistics.report_date <= report_date_end)
        if plan_id := kwargs.get('plan_id'):
            query = query.filter(DailyMissionStatistics.plan_id == plan_id)
        if mission_id := kwargs.get('mission_id'):
            query = query.filter(DailyMissionStatistics.mission_id == mission_id)
        if reward_type := kwargs.get('reward_type'):
            mission_ids = MissionCache.get_mission_by_reward_type(reward_type.name)
            query = query.filter(
                DailyMissionStatistics.mission_id.in_(mission_ids)
            )
        if mission_condition := kwargs.get('mission_condition'):
            mission_ids = MissionCache.get_mission_by_condition(mission_condition.name)
            query = query.filter(
                DailyMissionStatistics.mission_id.in_(mission_ids)
            )
        query = query.order_by(
            DailyMissionStatistics.report_date.desc(),
            DailyMissionStatistics.plan_id.desc(),
            DailyMissionStatistics.mission_id.desc()
        )
        if kwargs.get('export'):
            paginate = query.paginate(page, ADMIN_EXPORT_LIMIT, error_out=False)
        else:
            paginate = query.paginate(page, limit, error_out=False)
        items = []
        mission_ids = {i.mission_id for i in paginate.items}
        plan_ids = {i.plan_id for i in paginate.items}
        mission_info_mapper = MissionCache.get_cache_data_by_ids(list(mission_ids))
        plan_name_mapper = cls.get_plan_name_mapper(plan_ids)
        for item in paginate.items:
            mission_info = mission_info_mapper[item.mission_id]
            items.append(dict(
                report_date=item.report_date,
                plan_id=item.plan_id,
                mission_id=item.mission_id,
                plan_name=plan_name_mapper.get(item.plan_id),
                mission_condition=MissionCondition[mission_info['mission_condition']].value,
                reward_name=cls.get_reward_name_by_cache_data(mission_info),
                reward=mission_info['reward'],
                delivery_count=item.delivery_count,
                completion_count=item.completion_count,
                finished_count=item.finished_count,
                real_reward_amount=item.real_reward_amount,
                reward_amount=item.reward_amount,
                updated_at=item.updated_at,
                waring_flag=item.real_reward_amount != item.reward_amount,
            ))
        if kwargs.get('export'):
            export_items = []
            for item in items:
                reward = item['reward']
                export_items.append({
                    "report_date": item["report_date"].strftime("%Y-%m-%d"),
                    "plan_id": item["plan_id"],
                    "plan_name": item["plan_name"],
                    "mission_id": item["mission_id"],
                    "mission_condition": item["mission_condition"],
                    "reward_type": item["reward_name"],
                    "delivery_count": item["delivery_count"],
                    "completion_count": item["completion_count"],
                    "finished_count": item["finished_count"],
                    "real_reward_amount": f'{amount_to_str(item["real_reward_amount"])} {reward["value_type"]}',
                    "reward_amount": f'{amount_to_str(item["reward_amount"])} {reward["value_type"]}',
                    "updated_at": datetime_to_utc8_str(item["updated_at"])
                })
            return export_xlsx(
                filename='mission_statistics',
                data_list=export_items,
                export_headers=cls.export_headers
            )
        return dict(
            total=paginate.total,
            items=items,
            extra_data=dict(
                reward_types=EquityType,
                plan_id_mapper=cls.get_all_plan_name_mapper(scene_type=scene_type),
                mission_id_mapper=cls.get_mission_name_mapper(),
                mission_conditions=MissionCondition,
            )
        )


@ns.route('/routine')
@respond_with_code
class MissionPlanRoutineListResource(Resource, MissionResourceMixin):
    @classmethod
    @ns.use_kwargs(dict(
        page=PageField(missing=1),
        limit=LimitField(missing=50),
        scene_id=fields.Integer(),
        plan_id=fields.Integer(),
        group_id=fields.Integer(),
        business_party=EnumField(BusinessParty),
        status=EnumField(MissionPlan.Status)
    ))
    def get(cls, **kwargs):
        """运营-任务中心-老用户任务-列表"""
        page, limit = kwargs['page'], kwargs['limit']
        query = MissionPlan.query.filter(
            MissionPlan.scene_type == SceneType.ROUTINE,
            MissionPlan.status != MissionPlan.Status.DELETED
        )
        if scene_id := kwargs.get('scene_id'):
            query = query.filter(MissionPlan.scene_id == scene_id)
        if plan_id := kwargs.get('plan_id'):
            query = query.filter(MissionPlan.id == plan_id)
        if business_party := kwargs.get('business_party'):
            query = query.filter(MissionPlan.business_party == business_party)
        if status := kwargs.get('status'):
            statuses = [status, MissionPlan.Status.PASSED] if status == MissionPlan.Status.EFFECTIVE else [status]
            query = query.filter(MissionPlan.status.in_(statuses))
        if group_id := kwargs.get('group_id'):
            plan_ids = MissionGroupBiz.get_plan_ids_by_group_id(group_id)
            query = query.filter(MissionPlan.id.in_(plan_ids))
        query = query.order_by(
            MissionPlan.id.desc()
        ).with_entities(
            MissionPlan.id,
            MissionPlan.scene_id,
            MissionPlan.name,
            MissionPlan.status,
            MissionPlan.scene_type,
            MissionPlan.total,
            MissionPlan.created_by,
            MissionPlan.auditor_by,
            MissionPlan.business_party,
            MissionPlan.updated_at
        )
        paginate = query.paginate(page, limit, error_out=False)
        plan_ids = [i.id for i in paginate.items]
        mission_count_mapper = {
            pid: count for pid, count in
            Mission.query.filter(
                Mission.plan_id.in_(plan_ids),
            ).group_by(
                Mission.plan_id
            ).with_entities(
                Mission.plan_id,
                func.count(Mission.id)
            )
        }
        created_bys = [i.created_by for i in paginate.items]
        auditor_bys = [i.auditor_by for i in paginate.items]
        user_name_mapper = get_admin_user_name_map(set(created_bys + auditor_bys))
        plan_group_mapper = {
            i.plan_id: i for i in MissionPlanUserGroup.query.filter(
                MissionPlanUserGroup.plan_id.in_(plan_ids)
            ).with_entities(
                MissionPlanUserGroup.plan_id,
                MissionPlanUserGroup.group_ids
            ).all()
        }
        plan_statistics_mapper = cls.get_plan_statistics_mapper(plan_ids)
        scene_name_mapper = SceneHandler.get_scene_mapper(SceneType.ROUTINE)
        items = []
        for item in paginate.items:
            group = plan_group_mapper.get(item.id)
            if not group:
                continue
            group_ids = json.loads(group.group_ids) if group.group_ids else []
            statistics_data = plan_statistics_mapper.get(item.id, {})
            success_current_count = statistics_data.get("success_count", 0)
            failed_current_count = statistics_data.get("failed_count", 0)
            current_count = success_current_count + failed_current_count
            effective_users_count = statistics_data.get("effective_count", 0)
            items.append(dict(
                id=item.id,
                scene_type=item.scene_type.value,
                scene_id=item.scene_id,
                scene_name=scene_name_mapper.get(item.scene_id, ""),
                name=item.name,
                group_ids=group_ids,
                total=item.total if item.total else "无上限",
                current_count=current_count,
                success_count=success_current_count,
                failed_count=failed_current_count,
                effective_users_count=effective_users_count,
                mission_count=mission_count_mapper.get(item.id, 0),
                status=item.status.name,
                created_by=item.created_by,
                auditor_by=item.auditor_by,
                updated_at=item.updated_at,
                created_by_name=user_name_mapper.get(item.created_by, item.created_by),
                auditor_by_name=user_name_mapper.get(item.auditor_by, item.auditor_by),
                business_party=item.business_party.value
            ))
        return dict(
            total=paginate.total,
            items=items,
            extra_data=dict(
                statuses={s.name: s.value for s in MissionPlan.Status if
                          s not in [MissionPlan.Status.DELETED]},
                plan_id_mapper=[
                    dict(key=k, value=v) for k, v in cls.get_all_plan_name_mapper(SceneType.ROUTINE).items()
                ],
                mission_id_mapper=cls.get_mission_name_mapper(),
                business_parties=BusinessParty,
                scenes=[
                    dict(key=k, value=v) for k, v in SceneHandler.get_scene_mapper(SceneType.ROUTINE).items()
                ]
            )
        )


@ns.route('/routine/<int:id_>')
@respond_with_code
class MissionPlanRoutineResource(Resource):

    @classmethod
    def get(cls, id_):
        """运营-任务中心-老用户任务-获取详情"""
        base_dict = dict(
            scene_types=SceneType,
            conditions=MissionCondition,
            rewards=EquityCenterService.get_all_equity_dict(),
            business_parties=BusinessParty,
            scenes=SceneHandler.get_scene_mapper(SceneType.ROUTINE)
        )
        if not id_:
            return base_dict
        mission_plain_dict = MissionPlanBiz.get_info(id_)
        group_ids = json.loads(mission_plain_dict['group_ids']) if mission_plain_dict['group_ids'] else []
        tag_groups_map = UserTagGroupBiz.get_tag_group_info_dict(group_ids)
        mission_plain_dict['tag_groups'] = [tag_groups_map.get(i) for i in group_ids]
        mission_plain_dict['is_self_record'] = g.user.id == mission_plain_dict['created_by']
        mission_plain_dict.update(base_dict)
        return mission_plain_dict


@ns.route('/scenes')
@respond_with_code
class MissionSceneResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        scene_type=EnumField(SceneType, missing=SceneType.ROUTINE)
    ))
    def get(cls, **kwargs):
        """运营-任务中心-场景列表"""
        scene_type = kwargs['scene_type']
        scenes = MissionScene.query.filter(
            MissionScene.scene_type == scene_type
        ).order_by(
            MissionScene.id.desc()
        ).all()
        created_ids = {i.created_by for i in scenes}
        admin_user_mapper = get_admin_user_name_map(list(created_ids))
        result = []
        for scene in scenes:
            result.append(dict(
                id=scene.id,
                name=scene.name,
                created_by_name=admin_user_mapper.get(scene.created_by, scene.created_by),
                created_by=scene.created_by,
                created_at=scene.created_at,
                updated_at=scene.updated_at,
                remark=scene.remark
            ))
        return dict(
            total=len(result),
            items=result
        )

    @classmethod
    def check_scene_name_exist(cls, name, scene_type, id_=None):
        scene_query = MissionScene.query.filter(
            MissionScene.name == name,
            MissionScene.scene_type == scene_type
        )
        if id_:
            scene_query = scene_query.filter(
                MissionScene.id != id_
            )
        if scene_query.first():
            raise InvalidArgument(message="场景名称已存在")

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        remark=fields.String(required=True),
        scene_type=EnumField(SceneType, missing=SceneType.ROUTINE)
    ))
    def post(cls, **kwargs):
        """运营-任务中心-场景创建"""
        name = kwargs['name']
        scene_type = kwargs['scene_type']
        cls.check_scene_name_exist(name, scene_type)
        scene = MissionScene(
            name=name,
            scene_type=scene_type,
            remark=kwargs['remark'],
            created_by=g.user.id
        )
        db.session.add(scene)
        db.session.commit()
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MissionScene,
            detail=kwargs,
        )
        return dict(id=scene.id)

    @classmethod
    @ns.use_kwargs(dict(
        id=fields.Integer(required=True),
        name=fields.String(required=True),
        remark=fields.String(),
        scene_type=EnumField(SceneType, missing=SceneType.ROUTINE)
    ))
    def put(cls, **kwargs):
        """运营-任务中心-场景更新"""
        id_ = kwargs['id']
        scene = MissionScene.query.filter(
            MissionScene.id == id_
        ).first()
        if not scene:
            raise InvalidArgument(message="场景不存在")
        old_data = scene.to_dict()
        name, scene_type = kwargs['name'], kwargs['scene_type']
        cls.check_scene_name_exist(name, scene_type, id_)
        scene.name = name
        scene.remark = kwargs.get('remark', '')
        scene.scene_type = scene_type
        db.session.commit()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.MissionScene,
            old_data=old_data,
            new_data=kwargs,
        )
        return dict(id=scene.id)


@ns.route('/check-reception')
@respond_with_code
class MissionCheckReceptionResource(Resource, MissionResourceMixin):

    @classmethod
    @ns.use_kwargs(dict(
        scene_id=fields.Integer(required=True),
        group_ids=fields.List(fields.Integer, required=True),
        whitelist_user_ids=fields.String(allow_none=True),
        is_export=fields.Boolean(missing=False)
    ))
    def post(cls, **kwargs):
        """运营-任务中心-检查客群接受情况"""
        scene_id = kwargs['scene_id']
        group_ids = kwargs['group_ids']
        whitelist_user_ids = kwargs.get('whitelist_user_ids', '')
        tag_groups_map = UserTagGroupBiz.get_groups_users(group_ids, allow_update=True)
        users = set()
        for _, user_ids in tag_groups_map.items():
            users.update(user_ids)
        unreachable_users, _ = cls.get_unreachable_users(scene_id)
        whitelist_user_ids = set(map(int, whitelist_user_ids.split(','))) if whitelist_user_ids else set()
        users -= set(whitelist_user_ids)
        if kwargs['is_export']:
            rows = [
                dict(
                    id=user.id,
                    email=user.email
                ) for user in User.query.filter(
                    User.id.in_(users)
                ).with_entities(
                    User.id,
                    User.email
                ).all()
            ]
            return export_xlsx(
                filename='unreachable_users',
                data_list=rows,
                export_headers=(
                    {"field": "id", Language.ZH_HANS_CN: "用户ID"},
                    {"field": "email", Language.ZH_HANS_CN: "邮箱"},
                )
            )
        return dict(
            user_count=len(users),
            reachable_count=len(users - unreachable_users),
            unreachable_count=len(unreachable_users & users)
        )


@ns.route('/download-unreachable-users')
@respond_with_code
class MissionDownloadUnreachableUsersResource(Resource, MissionResourceMixin):

    @classmethod
    @ns.use_kwargs(dict(
        scene_id=fields.Integer(required=True),
        group_ids=fields.List(fields.Integer, required=True),
        whitelist_user_ids=fields.String(allow_none=True),
    ))
    def post(cls, **kwargs):
        """运营-任务中心-下载客群未接受用户"""
        scene_id = kwargs['scene_id']
        group_ids = kwargs['group_ids']
        whitelist_user_ids = kwargs.get('whitelist_user_ids', '')
        tag_groups_map = UserTagGroupBiz.get_groups_users(group_ids, allow_update=True)
        users = set()
        for _, user_ids in tag_groups_map.items():
            users.update(user_ids)
        _, plan_users_mapper = cls.get_unreachable_users(scene_id)
        whitelist_user_ids = set(map(int, whitelist_user_ids.split(','))) if whitelist_user_ids else set()
        users -= set(whitelist_user_ids)
        plan_name_mapper = cls.get_plan_name_mapper(set(plan_users_mapper.keys()))

        rows = []
        for plan_id, plan_users in plan_users_mapper.items():
            plan_name = plan_name_mapper.get(plan_id, '')
            for user_id in plan_users:
                if user_id not in users:
                    continue
                rows.append(dict(
                    user_id=user_id,
                    plan_info=f'{plan_id} ({plan_name})'
                ))
        return export_xlsx(
            filename='unreachable_users',
            data_list=rows,
            export_headers=(
                {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
                {"field": "plan_info", Language.ZH_HANS_CN: "推送ID"},
            )
        )