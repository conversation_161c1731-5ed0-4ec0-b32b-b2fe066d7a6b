from flask_babel import gettext, force_locale
from flask import g

from app import Language
from app.api.common import Resource, Namespace, respond_with_code, get_request_user
from app.business import UserPreferences
from app.models.media import Video, VideoSubtitle
from app.utils import AWSBucketPublic

ns = Namespace("Tutorial")

QUESTIONS = [
    {
        "question": gettext("以下哪个不是正向合约与反向合约的差异？"),
        "options": [
            gettext("保证金资产种类"),
            gettext("计价单位"),
            gettext("杠杆倍数"),
        ],
        "answer": 2,
        "detail": [
            gettext(
                "Tips：正向合约的保证金资产为定价货币，反向合约的保证金资产为交易货币。"
                "比如正向合约BTCUSDT的保证金为USDT，计价单位为USDT；"
                "反向合约BTCUSD的保证金为BTC，计价单位为USD。正向合约和反向合约，均支持最高100倍杠杆。"
            )
        ],
    },
    {
        "question": gettext("关于“全仓”和“逐仓“保证金模式，以下哪个描述不正确？"),
        "options": [
            gettext("全仓模式下，合约账户中所有可用余额均可用作当前仓位的保证金"),
            gettext("逐仓模式下，只有当前仓位的保证金用于维持仓位，或自行手动追加"),
            gettext("当前委托存在订单记录时，可以随意切换全仓或者逐仓模式"),
        ],
        "answer": 2,
        "detail": [gettext("Tips：若当前交易市场存在委托订单时，不可以切换全仓逐仓，也不可以进行杠杆倍数更改。")],
    },
    {
        "question": gettext("CoinEx合约采用哪种价格来决定强平价格？"),
        "options": [
            gettext("指数价格"),
            gettext("标记价格"),
            gettext("最新价格"),
        ],
        "answer": 1,
        "detail": [
            gettext("Tips：为了减少合约交易过程中价格操纵和流动性缺失导致的损失，CoinEx会引用多种价格指标供用户参考。"),
            gettext("最新价格：指CoinEx平台中，当前合约市场的最新成交价格。"),
            gettext("指数价格：取多家主流交易所现货市场价格的加权平均值，每5s更新一次。"),
            gettext("标记价格：基于指数价格和资金费率计算得出，主要作为当前持仓强平处理的参考价格。"),
        ],
    },
    {
        "question": gettext("关于“爆仓风险率”以下描述不正确的是？"),
        "options": [
            gettext("爆仓风险率根据仓位保证金及当前仓位所需的维持保证金计算，爆仓风险率数值越高表示风险越高"),
            gettext("爆仓风险率达到100%时，平台会发出强平预警通知"),
            gettext("爆仓风险率达到100%时，触发强制平仓流程"),
        ],
        "answer": 1,
        "detail": [gettext("Tips：爆仓风险率数值达到70%时，平台会发出强平预警通知。")],
    },
    {
        "question": gettext("合约交易最大的亏损风险为？"),
        "options": [
            gettext("合约账户的全部余额"),
            gettext("合约账户余额的50%"),
            gettext("合约交易不会亏损"),
        ],
        "answer": 0,
        "detail": [
            gettext(
                "Tips：合约交易具有高杠杆性和高风险性，当仓位保证金无法满足维持保证金的要求时将被强制平仓，"
                "损失全部保证金，因此合约交易有风险，请合理控制仓位。"
            )
        ],
    },
]

VIDEOS = {
    "perpetual_trade": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "perpetual-beginner-tutorial-app.mp4"
            ),
            Language.ES_ES: AWSBucketPublic.get_file_url(
                "2023-07-13/D3035F407832BD21A64DFC55D569F2EF.mp4"
            ),
            Language.FA_IR: AWSBucketPublic.get_file_url(
                "2023-07-13/E9ED0F8495424AB32D029042FF337F98.mp4"
            ),
            Language.RU_KZ: AWSBucketPublic.get_file_url(
                "2023-07-13/2D3CE5AAA2612E6EF24EB5BADD1A3E9F.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "perpetual-beginner-tutorial-web.mp4"
            ),
        },
    },
    "spot_trade": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/77205A543F7D32BBC357ED0A91EA1E4E.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/77205A543F7D32BBC357ED0A91EA1E4E.mp4"
            ),
            Language.ES_ES: AWSBucketPublic.get_file_url(
                "spot-trade-beginner-tutorial-web-en_es.mp4"
            ),
            Language.FA_IR: AWSBucketPublic.get_file_url(
                "upload-video/2023-09-07/126D0FD0D229CEF76B71CE5F7B8B6233.mp4"
            ),
            Language.AR_AE: AWSBucketPublic.get_file_url(
                "spot-trade-beginner-tutorial-web-ar_ae.mp4"
            ),
        },
    },
    "margin_trade": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/DB2DA930866226F8F1EF1B9F2F9DA891.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/DB2DA930866226F8F1EF1B9F2F9DA891.mp4"
            )
        },
    },
    "buy_crypto": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "video/********************************.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "video/d1e120905826d72babeede4ef583d64f.mp4"
            )
        },
    },
    "sell_crypto": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "video/c660dd99300f866f651a63c898d27928.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "video/018b57c8258483ac1339838c9dc5637a.mp4"
            )
        },
    },
    "withdrawal": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/8CF6ADF5395E0F0D4B5D077CFC94B694.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/8CF6ADF5395E0F0D4B5D077CFC94B694.mp4"
            ),
            Language.FA_IR: AWSBucketPublic.get_file_url(
                "2023-07-13/71A1C3E9E0E003C91AA90B2C142C8F9E.mp4"
            ),
        },
    },
    "deposit": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/DA1D65BE79945FE9A4D1B079EDF8EBF3.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/DA1D65BE79945FE9A4D1B079EDF8EBF3.mp4"
            ),
            Language.ES_ES: AWSBucketPublic.get_file_url(
                "deposit-beginner-tutorial-web-en_es.mp4"
            ),
            Language.FA_IR: AWSBucketPublic.get_file_url(
                "upload-video/2023-09-08/5D6F5A33770BA0334D04708BF3CBB41B.mp4"
            ),
        },
    },
    "swap": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/02580CAF35170D6CD7E67BFB236829BD.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/02580CAF35170D6CD7E67BFB236829BD.mp4"
            )
        },
    },
    "financial": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/B80D6F7ABB12E49793BAB589A009F09D.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/B80D6F7ABB12E49793BAB589A009F09D.mp4"
            )
        },
    },
    "amm": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "amm-beginner-tutorial-app.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "amm-beginner-tutorial-web.mp4"
            )
        },
    },
    "ambassador_info": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "video/c7a6f68e14016a2bb00a8bda01b9429e.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "video/c7a6f68e14016a2bb00a8bda01b9429e.mp4"
            )
        },
    },
    "ambassador_agent": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "video/b4140f62bbfc00e81feffc1a72b519f3.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "video/9071391fdb6f69ecb5c1c8241a2710c4.mp4"
            )
        },
    },
    "p2p": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/067B6DDA47D054D867889EAE6C372C67.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/067B6DDA47D054D867889EAE6C372C67.mp4"
            )
        },

    },
    "pre_trading": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/81D1A020272780E75FD6BE37BE46DA66.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/81D1A020272780E75FD6BE37BE46DA66.mp4"
            )
        },

    },
    "spot_grid": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/66B56C777EF1DB2683064C5BA5A16735.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/66B56C777EF1DB2683064C5BA5A16735.mp4"
            )
        },

    },
    "auto_invest": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/D6BD857A1D71F44861BDFA4D6F0CBA0E.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/D6BD857A1D71F44861BDFA4D6F0CBA0E.mp4"
            )
        },

    },
    "staking": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/67D59B4972C4F5644148CE743170303B.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/67D59B4972C4F5644148CE743170303B.mp4"
            )
        },
    },
    "copy_trader": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/03F798C10786F0CD9DED00E7DF1067AF.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/03F798C10786F0CD9DED00E7DF1067AF.mp4"
            )
        },
    },
    "copy_follower": {
        "app": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/E50E3F0B3D65349A864BEB36E7808439.mp4"
            ),
        },
        "web": {
            Language.EN_US: AWSBucketPublic.get_file_url(
                "upload-video/2025-02-20/E50E3F0B3D65349A864BEB36E7808439.mp4"
            )
        },
    },
}


@ns.route("/perpetual/questions")
@respond_with_code
class BeginnerTutorialQuestionResource(Resource):
    """合约新手教程答题"""

    @classmethod
    def get(cls):
        user = get_request_user(allow_none=True)
        if user:
            UserPreferences(user.main_user.id).view_perpetual_tutorial_question = True
        lang = g.lang
        ret = []
        with force_locale(lang):
            for qa in QUESTIONS:
                q = gettext(qa["question"])
                options = [gettext(option) for option in qa["options"]]
                detail = "\n".join([gettext(e) for e in qa["detail"]])
                a = qa["answer"]
                ret.append(
                    dict(
                        question=q,
                        options=options,
                        answer=a,
                        detail=detail,
                    )
                )
            return {"questions": ret}


@ns.route("/perpetual/video")
@respond_with_code
class BeginnerTutorialResource(Resource):
    """合约新手教程视频 URL (待删除接口，兼容客户端目前保留)"""

    @classmethod
    def get(cls):
        user = get_request_user(allow_none=True)
        if user:
            UserPreferences(user.main_user.id).view_perpetual_tutorial_video = True
        url = VIDEOS["perpetual_trade"]
        app_url = url["app"].get(Language(g.lang), url["app"][Language.EN_US])
        web_url = url["web"].get(Language(g.lang), url["web"][Language.EN_US])
        ret = {"url": {"app": app_url, "web": web_url}}
        return ret


@ns.route("/video")
@respond_with_code
class TutorialResource(Resource):
    """教程视频 URL"""
    # TODO: 旧版，等 admin 配置完成，前端部署后可以下线

    @classmethod
    def get(cls):
        lang = Language(g.lang)
        return {
            k: dict(
                app=v["app"].get(lang, v["app"][Language.EN_US]),
                web=v["web"].get(lang, v["web"][Language.EN_US]),
            )
            for k, v in VIDEOS.items()
        }


@ns.route("/video-with-subtitle")
@respond_with_code
class TutorialVideoWithSubtitleResource(Resource):
    """教程视频 URL（新版，视频与字幕分离）"""
    @classmethod
    def get(cls):
        lang = Language(g.lang)

        videos = Video.query.filter(Video.usage == Video.Usage.TUTORIAL, Video.business.is_not(None)).all()
        video_ids = [video.id for video in videos]

        subtitles = VideoSubtitle.query.filter(VideoSubtitle.video_id.in_(video_ids), VideoSubtitle.lang == lang).all()
        subtitle_keys = {subtitle.video_id: subtitle.file_key for subtitle in subtitles}

        ret = {}
        for video in videos:
            business_dict = ret.setdefault(video.business.value, {})
            platform_dict = business_dict.setdefault(video.platform.value, {})
            if video.lang != lang and video.lang != Language.EN_US:
                # 如果视频语言不是当前语言，也不是英文，则不处理
                continue
            if platform_dict and platform_dict.get('lang') == video.lang.name:
                # 如果已经存在对应语言的视频，则不覆盖
                continue

            key = subtitle_keys.get(video.id)
            platform_dict['video'] = AWSBucketPublic.get_file_url(video.file_key)
            platform_dict['subtitle'] = AWSBucketPublic.get_file_url(key) if key else None
            platform_dict['lang'] = video.lang.name,
            platform_dict['cover'] = AWSBucketPublic.get_file_url(video.cover_key) if video.cover_key else None

        return ret
