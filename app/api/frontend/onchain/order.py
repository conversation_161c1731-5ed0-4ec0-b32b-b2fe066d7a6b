# -*- coding: utf-8 -*-
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal

from flask import g
from flask_babel import gettext as _
from webargs import fields

from app.api.common.fields import <PERSON><PERSON>ield, LimitField
from app.business import WalletClient
from app.business.onchain.token import get_token
from app.common.onchain import Chain, OrderSide, CHAIN_MONEY_MAPPING
from app.api.common import Namespace, Resource, respond_with_code, require_login, ex_fields
from app.business.onchain.order import OnchainOrderBiz, OnchainStopOrderBiz, get_money_asset_decimals
from app.exceptions import InvalidArgument, TwoFactorAuthenticationRequired
from app.models.onchain import OnchainOrder, OnchainStopOrder, OnchainToken
from app.utils import batch_iter
from app.utils.onchain import decimal_div, quantize_amount

ns = Namespace('Order')


def check_slippage_limit(slippage_limit: Decimal):
    if slippage_limit > Decimal(50) or slippage_limit <= Decimal(0):
        raise InvalidArgument


def check_money_asset(chain: Chain, money_asset: str):
    if money_asset not in CHAIN_MONEY_MAPPING[chain]:
        raise InvalidArgument


def check_trigger_units(
        chain: Chain, trigger_type: OnchainStopOrder.TriggerType, trigger_unit: str):
    chain_money_set = set(CHAIN_MONEY_MAPPING[chain].keys())
    if trigger_type == OnchainStopOrder.TriggerType.PRICE:
        if trigger_unit not in chain_money_set:
            raise InvalidArgument


def check_exchanger_id(chain: Chain, exchanger_id: str):
    if exchanger_id != OnchainOrderBiz.chain_exchanger_id_mapping[chain]:
        raise InvalidArgument


def get_from_to_decimals(side: OrderSide, chain: Chain, money_asset: str, token: OnchainToken):
    if side == OrderSide.BUY:
        from_decimals = get_money_asset_decimals(chain, money_asset)
        to_decimals = token.decimals
    else:
        from_decimals = token.decimals
        to_decimals = get_money_asset_decimals(chain, money_asset)
    return from_decimals, to_decimals


def check_amount_positive(amount: Decimal):
    if amount <= Decimal(0):
        raise InvalidArgument


@ns.route('/quote')
@respond_with_code
class OnchainQuoteResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs({
        'side': ex_fields.EnumField(OrderSide, required=True),
        'chain': ex_fields.EnumField(Chain.get_display_name_list(), required=True),
        'token_id': fields.Integer(required=True),
        'money_asset': fields.String(required=True),
        'from_amount': fields.Decimal(required=True),
        'slippage_limit': fields.Decimal(required=True),
    })
    def get(cls, **kwargs):
        # 询价
        user_id = g.user.id
        chain = Chain.display_name_to_enum(kwargs['chain'])
        check_money_asset(chain, kwargs['money_asset'])
        check_slippage_limit(kwargs['slippage_limit'])
        if not (token := get_token(kwargs['token_id'])):
            raise InvalidArgument
        if token.chain != chain:
            raise InvalidArgument
        from_decimals, to_decimals = get_from_to_decimals(kwargs['side'], chain, kwargs['money_asset'], token)
        from_amount = quantize_amount(kwargs['from_amount'], from_decimals)
        if from_amount < Decimal(0):
            raise InvalidArgument
        r = OnchainOrderBiz.quote(
            user_id, kwargs['side'], chain, token, kwargs['money_asset'],
            from_amount, kwargs['slippage_limit']
        )
        return r


@ns.route('')
@respond_with_code
class OnchainOrderResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs({
        'side': ex_fields.EnumField(OrderSide, required=True),
        'exchanger_id': fields.String(required=True),
        'chain': ex_fields.EnumField(Chain.get_display_name_list(), required=True),
        'token_id': fields.Integer(required=True),
        'money_asset': fields.String(required=True),
        'from_amount': fields.Decimal(required=True),
        'want_amount': fields.Decimal(required=True),
        'slippage_limit': fields.Decimal(required=True),
    })
    def post(cls, **kwargs):
        # 创建市价单
        user = g.user
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        chain = Chain.display_name_to_enum(kwargs['chain'])
        check_money_asset(chain, kwargs['money_asset'])
        check_slippage_limit(kwargs['slippage_limit'])
        check_exchanger_id(chain, kwargs['exchanger_id'])
        if not (token := get_token(kwargs['token_id'])):
            raise InvalidArgument
        if token.chain != chain:
            raise InvalidArgument
        from_decimals, to_decimals = get_from_to_decimals(kwargs['side'], chain, kwargs['money_asset'], token)
        from_amount = quantize_amount(kwargs['from_amount'], from_decimals)
        want_amount = quantize_amount(kwargs['want_amount'], to_decimals)
        check_amount_positive(from_amount)
        check_amount_positive(want_amount)
        OnchainOrderBiz.create(
            user.id, kwargs['side'], OnchainOrder.Type.MARKET,
            kwargs['exchanger_id'], chain, token, kwargs['money_asset'],
            from_amount, want_amount, kwargs['slippage_limit']
        )


@ns.route('/stop')
@respond_with_code
class OnchainStopOrderResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs({
        'side': ex_fields.EnumField(OrderSide, required=True),
        'chain': ex_fields.EnumField(Chain.get_display_name_list(), required=True),
        'token_id': fields.Integer(required=True),
        'money_asset': fields.String(required=True),
        'from_amount': fields.Decimal(required=True),
        'want_amount': fields.Decimal(required=True),
        'trigger_type': ex_fields.EnumField(OnchainStopOrder.TriggerType, required=True),
        'trigger_amount': fields.Decimal(required=True),
        'trigger_unit': fields.String(required=True),
        'slippage_limit': fields.Decimal(required=True),
        'operator': ex_fields.EnumField(OnchainStopOrder.Operator, required=True),
    })
    def post(cls, **kwargs):
        # 创建计划市价单
        user = g.user
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        chain = Chain.display_name_to_enum(kwargs['chain'])
        check_money_asset(chain, kwargs['money_asset'])
        check_slippage_limit(kwargs['slippage_limit'])
        if not (token := get_token(kwargs['token_id'])):
            raise InvalidArgument
        if token.chain != chain:
            raise InvalidArgument
        check_amount_positive(kwargs['trigger_amount'])
        check_trigger_units(chain, kwargs['trigger_type'], kwargs['trigger_unit'])
        from_decimals, to_decimals = get_from_to_decimals(kwargs['side'], chain, kwargs['money_asset'], token)
        from_amount = quantize_amount(kwargs['from_amount'], from_decimals)
        want_amount = quantize_amount(kwargs['want_amount'], to_decimals)
        check_amount_positive(from_amount)
        check_amount_positive(want_amount)
        OnchainStopOrderBiz.create(
            user.id, kwargs['side'], chain, token, kwargs['money_asset'],
            from_amount, want_amount,
            kwargs['trigger_type'], kwargs['trigger_amount'], kwargs['trigger_unit'],
            kwargs['slippage_limit'], kwargs['operator'],
        )

    @classmethod
    @require_login
    @ns.use_kwargs({
        'order_id': fields.Integer(required=True),
    })
    def delete(cls, **kwargs):
        # 取消计划市价单
        order_id = kwargs['order_id']
        user = g.user
        row = OnchainStopOrder.query.filter(
            OnchainStopOrder.id == order_id,
            OnchainStopOrder.user_id == user.id
        ).first()
        if not row:
            raise InvalidArgument
        if row.status != OnchainStopOrder.Status.CREATED:
            raise InvalidArgument(message=_("订单处理中，无法撤销"))
        OnchainStopOrderBiz.cancel(order_id)


@ns.route('/stop/cancel')
@respond_with_code
class CancelOnchainStopOrderResource(Resource):

    @classmethod
    @require_login
    def post(cls):
        # 批量取消计划市价单
        user_id = g.user.id
        OnchainStopOrderBiz.batch_cancel(user_id)


@ns.route('/stats')
@respond_with_code
class OrderStatsResource(Resource):

    @classmethod
    @require_login
    def get(cls):
        user_id = g.user.id
        market_order_pending_count = cls.get_market_order_pending_count(user_id)
        limit_order_pending_count = cls.get_limit_order_pending_count(user_id)
        return {
            'pending_order_count': market_order_pending_count + limit_order_pending_count,
            'market_order_pending_count': market_order_pending_count,
            'limit_order_pending_count': limit_order_pending_count,
        }

    @classmethod
    def get_market_order_pending_count(cls, user_id: int):
        count = OnchainOrder.query.filter(
            OnchainOrder.user_id == user_id,
            OnchainOrder.status.in_([
                OnchainOrder.Status.CREATED,
                OnchainOrder.Status.PROCESSING,
                OnchainOrder.Status.TO_FINISH
            ])
        ).count()
        return count

    @classmethod
    def get_limit_order_pending_count(cls, user_id: int):
        count = OnchainStopOrder.query.filter(
            OnchainStopOrder.user_id == user_id,
            OnchainStopOrder.status == OnchainStopOrder.Status.CREATED,
        ).count()
        return count


class OrderListMixin:

    @classmethod
    def get_token_map(cls, token_ids: list):
        res = dict()
        for ids in batch_iter(token_ids, 2000):
            rows = OnchainToken.query.filter(
                OnchainToken.id.in_(ids)
            ).all()
            res.update({r.id: r for r in rows})
        return res


class MarketOrderListMixin(OrderListMixin):

    model = OnchainOrder

    @classmethod
    def get_order_list(cls, kwargs, user_id: int, status_list: list):
        page, limit = kwargs['page'], kwargs['limit']
        trade_token_ids = {v.token_id for v in cls.model.query.filter(
            cls.model.user_id == user_id,
        ).with_entities(
            cls.model.token_id.distinct().label('token_id')
        ).all()}
        query = cls.model.query.filter(
            cls.model.user_id == user_id,
            cls.model.status.in_(status_list)
        )
        if token_id := kwargs.get('token_id'):
            query = query.filter(cls.model.token_id == token_id)
        if side := kwargs.get('side'):
            query = query.filter(cls.model.side == side)
        pagination = query.order_by(cls.model.id.desc()).paginate(page, limit, error_out=False)
        token_ids = {v.token_id for v in pagination.items} | trade_token_ids
        token_mapper = cls.get_token_map(list(token_ids))
        data = []
        for v in pagination.items:
            status = {
                cls.model.Status.CREATED: 'PROCESSING',
                cls.model.Status.PROCESSING: 'PROCESSING',
                cls.model.Status.TO_FINISH: 'PROCESSING',
                cls.model.Status.FINISHED: 'FINISHED',
                cls.model.Status.FAILED: 'FAILED',
                cls.model.Status.CANCELLED: 'CANCELLED',
            }[v.status]
            data.append(dict(
                id=v.id,
                created_at=v.created_at,
                token_id=v.token_id,
                token_symbol=token_mapper[v.token_id].symbol,
                token_name=token_mapper[v.token_id].name,
                token_contract=token_mapper[v.token_id].contract,
                token_logo=token_mapper[v.token_id].logo,
                chain=v.chain.get_display_name(),
                chain_full_name=v.chain.get_display_full_name(),
                side=v.side.name,
                token_amount=v.token_amount,
                money_asset=v.money_asset,
                money_amount=v.money_amount,
                price=decimal_div(v.money_amount, v.token_amount) if Decimal(v.token_amount) else Decimal(0),
                fee=v.fee + v.gas_fee,
                tx_id=v.tx_id,
                status=status,
            ))

        explorer_tx_url_map, explorer_address_url_map = {}, {}
        tx_query_list, address_query_list = [], []
        query_order_ids, query_token_ids = [], []
        for v in data:
            chain = Chain.display_name_to_enum(v['chain'])
            address_query_list.append((chain.name, v['token_contract']))
            query_token_ids.append(v['token_id'])
            if v['tx_id']:
                tx_query_list.append((chain.name, v['tx_id']))
                query_order_ids.append(v['id'])
        for index, explorer_address_url in enumerate(WalletClient().get_explorer_addresses_url(address_query_list)):
            explorer_address_url_map[query_token_ids[index]] = explorer_address_url
        for index, explorer_tx_url in enumerate(WalletClient().get_explorer_txs_url(tx_query_list)):
            explorer_tx_url_map[query_order_ids[index]] = explorer_tx_url
        for v in data:
            v['explorer_tx_url'] = explorer_tx_url_map.get(v['id'], '')
            v['explorer_address_url'] = explorer_address_url_map.get(v['token_id'], '')

        tokens = [dict(
            token_id=token_id,
            token_symbol=token_mapper[token_id].symbol,
            token_name=token_mapper[token_id].name,
            token_contract=token_mapper[token_id].contract,
            token_logo=token_mapper[token_id].logo,
            chain=token_mapper[token_id].chain.get_display_name(),
            chain_full_name=token_mapper[token_id].chain.get_display_full_name(),
        ) for token_id in token_ids]
        return dict(
            data=data,
            total=pagination.total,
            total_page=pagination.pages,
            count=len(data),
            curr_page=pagination.page,
            has_next=pagination.has_next,
            extra=dict(
                tokens=tokens,
            )
        )


@ns.route('/pending')
@respond_with_code
class PendingOrderListResource(Resource, MarketOrderListMixin):

    @classmethod
    @require_login
    @ns.use_kwargs({
        'token_id': fields.Integer,
        'side': ex_fields.EnumField(OrderSide),
        'page': PageField(),
        'limit': LimitField(),
    })
    def get(cls, **kwargs):
        # 当前委托（市价）
        user_id = g.user.id
        status_list = [cls.model.Status.CREATED, cls.model.Status.PROCESSING, cls.model.Status.TO_FINISH]
        return cls.get_order_list(kwargs, user_id, status_list)


@ns.route('/finished')
@respond_with_code
class FinishedOrderListResource(Resource, MarketOrderListMixin):

    @classmethod
    @require_login
    @ns.use_kwargs({
        'token_id': fields.Integer,
        'side': ex_fields.EnumField(OrderSide),
        'status': ex_fields.EnumField(OnchainOrder.Status),
        'page': PageField(),
        'limit': LimitField(),
    })
    def get(cls, **kwargs):
        # 历史委托（市价）
        user_id = g.user.id
        status_list = [cls.model.Status.FINISHED, cls.model.Status.FAILED, cls.model.Status.CANCELLED]
        if status := kwargs.get('status'):
            if status not in status_list:
                raise InvalidArgument
            status_list = [status]
        return cls.get_order_list(kwargs, user_id, status_list)


class StopOrderListMixin(OrderListMixin):

    model = OnchainStopOrder

    @classmethod
    def get_order_list(cls, kwargs, user_id: int, status_list: list):
        page, limit = kwargs['page'], kwargs['limit']
        trade_token_ids = {v.token_id for v in cls.model.query.filter(
            cls.model.user_id == user_id,
        ).with_entities(
            cls.model.token_id.distinct().label('token_id')
        ).all()}
        query = cls.model.query.filter(
            cls.model.user_id == user_id,
            cls.model.status.in_(status_list)
        )
        if token_id := kwargs.get('token_id'):
            query = query.filter(cls.model.token_id == token_id)
        if side := kwargs.get('side'):
            query = query.filter(cls.model.side == side)
        pagination = query.order_by(cls.model.id.desc()).paginate(page, limit, error_out=False)
        token_ids = {v.token_id for v in pagination.items} | trade_token_ids
        token_mapper = cls.get_token_map(list(token_ids))
        data = []
        for v in pagination.items:
            v: cls.model
            trigger_price = v.trigger_amount if v.trigger_type == cls.model.TriggerType.PRICE else None
            trigger_price_unit = v.trigger_unit if v.trigger_type == cls.model.TriggerType.PRICE else None
            data.append(dict(
                id=v.id,
                created_at=v.created_at,
                expired_at=v.created_at + timedelta(days=30),
                token_id=v.token_id,
                token_symbol=token_mapper[v.token_id].symbol,
                token_name=token_mapper[v.token_id].name,
                token_contract=token_mapper[v.token_id].contract,
                token_logo=token_mapper[v.token_id].logo,
                chain=v.chain.get_display_name(),
                chain_full_name=v.chain.get_display_full_name(),
                side=v.side.name,
                token_amount=Decimal(0) if v.side == OrderSide.BUY else v.from_amount,
                money_asset=v.money_asset,
                money_amount=v.from_amount if v.side == OrderSide.BUY else Decimal(0),
                trigger_price=trigger_price,
                trigger_price_unit=trigger_price_unit,
                operator=v.operator.name,
                status=v.status.name,
                effected_at=v.effected_at,
            ))

        explorer_address_url_map = {}
        address_query_list = []
        query_token_ids = []
        for v in data:
            chain = Chain.display_name_to_enum(v['chain'])
            address_query_list.append((chain.name, v['token_contract']))
            query_token_ids.append(v['token_id'])
        for index, explorer_address_url in enumerate(WalletClient().get_explorer_addresses_url(address_query_list)):
            explorer_address_url_map[query_token_ids[index]] = explorer_address_url
        for v in data:
            v['explorer_address_url'] = explorer_address_url_map.get(v['token_id'], '')

        tokens = [dict(
            token_id=token_id,
            token_symbol=token_mapper[token_id].symbol,
            token_name=token_mapper[token_id].name,
            token_contract=token_mapper[token_id].contract,
            token_logo=token_mapper[token_id].logo,
            chain=token_mapper[token_id].chain.get_display_name(),
            chain_full_name=token_mapper[token_id].chain.get_display_full_name(),
        ) for token_id in token_ids]
        return dict(
            data=data,
            total=pagination.total,
            total_page=pagination.pages,
            count=len(data),
            curr_page=pagination.page,
            has_next=pagination.has_next,
            extra=dict(
                tokens=tokens,
            )
        )


@ns.route('/stop/pending')
@respond_with_code
class PendingStopOrderListResource(Resource, StopOrderListMixin):

    @classmethod
    @require_login
    @ns.use_kwargs({
        'token_id': fields.Integer,
        'side': ex_fields.EnumField(OrderSide),
        'page': PageField(),
        'limit': LimitField(),
    })
    def get(cls, **kwargs):
        # 当前委托（计划市价）
        user_id = g.user.id
        status_list = [cls.model.Status.CREATED]
        return cls.get_order_list(kwargs, user_id, status_list)


@ns.route('/stop/finished')
@respond_with_code
class FinishedStopOrderListResource(Resource, StopOrderListMixin):

    @classmethod
    @require_login
    @ns.use_kwargs({
        'token_id': fields.Integer,
        'side': ex_fields.EnumField(OrderSide),
        'status': ex_fields.EnumField(OnchainStopOrder.Status),
        'page': PageField(),
        'limit': LimitField(),
    })
    def get(cls, **kwargs):
        # 历史委托（计划市价）
        user_id = g.user.id
        status_list = [cls.model.Status.ACTIVE, cls.model.Status.FAILED, cls.model.Status.CANCELLED]
        if status := kwargs.get('status'):
            if status not in status_list:
                raise InvalidArgument
            status_list = [status]
        return cls.get_order_list(kwargs, user_id, status_list)
