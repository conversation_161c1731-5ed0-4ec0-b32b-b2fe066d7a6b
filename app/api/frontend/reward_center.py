# -*- coding: utf-8 -*-
from decimal import Decimal
from typing import List, Dict, Callable
from enum import Enum

from flask import g
from flask_babel import gettext
from sqlalchemy import func
from marshmallow import fields as mm_fields

from app.business.prices import PriceManager
from app.models.equity_center import EquityType, UserEquity, UserCashbackEquity, UserAirdropEquity
from app.exceptions import InvalidArgument
from app.api.common import (
    Resource,
    Namespace,
    respond_with_code,
    require_login,
)
from app.api.common.fields import LimitField, PageField, EnumField
from app.utils import group_by, now, amount_to_str, quantize_amount
from app.business.equity_center.cashback import CashbackSettleHelper


ns = Namespace("RewardCenter")
url_prefix = '/reward-center'


@ns.route("/rewards")
@respond_with_code
class RewardCenterRewardsResource(Resource):

    class FrontStatus(Enum):
        RECEIVED = "已到账"  # 空投
        USING = "使用中"  # 返现
        EXPIRED = "已过期"  # 返现
        FINISHED = "已使用"  # 返现

    FRONT_STATUS_REAL_STATUS_DICT = {
        FrontStatus.RECEIVED: UserEquity.Status.FINISHED,
        FrontStatus.USING: UserEquity.Status.USING,
        FrontStatus.EXPIRED: UserEquity.Status.EXPIRED,
        FrontStatus.FINISHED: UserEquity.Status.FINISHED,
    }

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            reward_type=EnumField(EquityType),
            status=EnumField(FrontStatus),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 任务中心-我的奖励 """
        user = g.user

        model = UserEquity
        q = model.query.filter(
            model.user_id == user.id,
        ).order_by(model.id.desc())
        if reward_type := kwargs.get("reward_type"):
            q = q.filter(model.type == reward_type)
        if front_status := kwargs.get("status"):
            real_status = cls.FRONT_STATUS_REAL_STATUS_DICT[front_status]
            if front_status == cls.FrontStatus.RECEIVED:
                # 已到账，只有空投类型
                q = q.filter(
                    model.type == EquityType.AIRDROP,
                    model.status == real_status,
                )
            elif front_status in [
                cls.FrontStatus.USING,
                cls.FrontStatus.EXPIRED,
                cls.FrontStatus.FINISHED,
            ]:
                q = q.filter(
                    model.type == EquityType.CASHBACK,
                    model.status == real_status,
                )
        else:
            q = q.filter(
                model.status.in_(
                    [
                        model.Status.USING,
                        model.Status.EXPIRED,
                        model.Status.FINISHED,
                    ]
                ),
            )

        page, limit = kwargs["page"], kwargs["limit"]
        pagination = q.paginate(page, limit, error_out=False)
        rows: List[model] = pagination.items
        items = cls.format_equity_rows(rows)

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )

    @classmethod
    def format_equity_rows(cls, rows: list[UserEquity]) -> list[Dict]:
        type_format_func_map: Dict[EquityType, Callable] = {
            EquityType.AIRDROP: cls.format_airdrop,
            EquityType.CASHBACK: cls.format_cashback,
        }
        format_items = []
        for type_, items in group_by(lambda x: x.type, rows).items():
            if func_ := type_format_func_map.get(type_):
                format_items.extend(func_(items))
        org_ids_order = [i.id for i in rows]
        format_items.sort(key=lambda x: org_ids_order.index(x["reward_id"]))
        return format_items

    @classmethod
    def format_airdrop(cls, rows: list[UserEquity]) -> list[Dict]:
        user_eq_ids = [i.id for i in rows]
        air_rows = UserAirdropEquity.query.filter(
            UserAirdropEquity.user_equity_id.in_(user_eq_ids),
        ).all()
        air_map = {i.user_equity_id: i for i in air_rows}

        items = []
        for r in rows:
            air: UserAirdropEquity = air_map[r.id]
            item = {
                'reward_id': air.user_equity_id,
                'value_type': air.airdrop_asset,
                'value': air.airdrop_amount,
                'created_at': air.created_at,
                'expired_at': None,
                'reward_type': EquityType.AIRDROP.name,
                'status': cls.FrontStatus.RECEIVED.name,
                'extra': {
                    'cost_asset': air.value_asset,
                    'cost_amount': amount_to_str(air.value_amount, 2),
                },
            }
            items.append(item)
        return items

    @classmethod
    def format_progress(cls, current: Decimal, total: Decimal, unit: str):
        progress = dict(
            total=quantize_amount(total, 2),
            current=current,
            progress=quantize_amount(current / total * 100, 2),
            unit=unit
        )
        return progress

    @classmethod
    def format_cashback(cls, rows: list[UserEquity]) -> list[Dict]:
        user_eq_ids = [i.id for i in rows]
        cb_rows = UserCashbackEquity.query.filter(
            UserCashbackEquity.user_equity_id.in_(user_eq_ids),
        ).all()
        cb_map = {i.user_equity_id: i for i in cb_rows}

        items = []
        for r in rows:
            cb: UserCashbackEquity = cb_map[r.id]
            front_status = cb.status
            if cb.status == UserCashbackEquity.Status.USING and cb.end_time <= now():
                front_status = UserCashbackEquity.Status.FINISHED if cb.cashback_amount > 0 else UserCashbackEquity.Status.EXPIRED
            item = {
                'reward_id': cb.user_equity_id,
                'value_type': cb.cost_asset,
                'value': cb.cost_amount,
                'created_at': cb.start_time,
                'expired_at': cb.end_time,
                'reward_type': EquityType.CASHBACK.name,
                'status': front_status.name,
                'extra': {
                    'remain_asset': cb.cost_asset,
                    'remain_amount': cb.remain_cost_amount,
                    'cashback_asset': cb.cashback_asset,
                    'cashback_scope': cb.cashback_scope.name,
                    'cashback_ratio': quantize_amount(cb.cashback_ratio * Decimal(100), 0),
                    'progress': cls.format_progress(cb.used_cost_amount, cb.cost_amount, cb.cost_asset)
                },
            }
            items.append(item)
        return items


@ns.route("/reward-detail")
@respond_with_code
class RewardCenterRewardDetailResource(Resource):

    class CashbackScopeDesc(Enum):
        # 返现范围
        SPOT = gettext("币币市场")
        PERPETUAL = gettext("合约市场")
        ALL = gettext("所有市场")

    @classmethod
    def format_reward_desc(cls, reward_info: dict) -> str:
        if reward_info['reward_type'] == EquityType.CASHBACK.name:
            cashback_scope = reward_info['extra']['cashback_scope']

            reward_desc = gettext(
                "1. 返现范围：%(cashback_scope)s；\n"
                "2. 返现比例：根据实际手续费金额，按%(cashback_ratio)s%%比例返还；\n"
                "3. 返现时间：符合使用条件的交易订单将在1-3小时内返还手续费；\n"
                "4. 返还形式：以 %(cashback_asset)s 形式发放到现货账户；\n"
                "5. 注意事项：\n"
                "（1）返现额度用完或返现权益到期后，无法继续使用该返现奖励；\n"
                "（2）如获得多张返现奖励，系统会按一定顺序依次消耗额度；\n"
                "（3）手续费返现奖励适用于VIP等级、CET费率折扣；\n"
                "（4）手续费返现奖励不适用于AMM市场。\n"
                "6. CoinEx保留手续费返现奖励相关规则的最终解释权。\n",
                cashback_scope=gettext(cls.CashbackScopeDesc[cashback_scope].value),
                cashback_asset=reward_info['extra']['cashback_asset'],
                cashback_ratio=quantize_amount(reward_info['extra']['cashback_ratio'], 0),
            )
        elif reward_info['reward_type'] == EquityType.AIRDROP.name:
            reward_desc = gettext("空投奖励已发放到现货账户，可划转或交易。请前往现货账户查看。")
        else:
            reward_desc = ""
        return reward_desc

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            reward_id=mm_fields.Integer,
        )
    )
    def get(cls, **kwargs):
        """ 任务中心-我的奖励详情 """
        user = g.user

        user_eq_id = kwargs['reward_id']
        eq: UserEquity = UserEquity.query.filter(
            UserEquity.user_id == user.id,
            UserEquity.id == user_eq_id
        ).first()
        if not eq:
            raise InvalidArgument

        detail = RewardCenterRewardsResource.format_equity_rows([eq])[0]
        detail['source'] = eq.business_type.name
        detail['source_desc'] = gettext(eq.business_type.display_name)
        detail['reward_desc'] = cls.format_reward_desc(detail)
        return detail


@ns.route("/total-reward")
@respond_with_code
class RewardCenterTotalRewardResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 任务中心-我的奖励总额 """
        user = g.user

        display_fiat = "USD"
        air_amount_query = UserAirdropEquity.query.filter(
            UserAirdropEquity.user_id == user.id,
            UserAirdropEquity.status == UserAirdropEquity.Status.FINISHED,
        ).group_by(
            UserAirdropEquity.value_asset
        ).with_entities(
            UserAirdropEquity.value_asset,
            func.sum(UserAirdropEquity.value_amount),
        ).all()

        cashback_amount_query = UserCashbackEquity.query.filter(
            UserCashbackEquity.user_id == user.id,
            UserCashbackEquity.status.in_(
                [
                    UserCashbackEquity.Status.USING,
                    UserCashbackEquity.Status.FINISHED,
                    UserCashbackEquity.Status.EXPIRED,
                ]
            ),
        ).group_by(
            UserCashbackEquity.cost_asset
        ).with_entities(
            UserCashbackEquity.cost_asset,
            func.sum(UserCashbackEquity.cost_amount),
        ).all()
        value_assets = {k for k, v in air_amount_query}
        cost_assets = {k for k, v in cashback_amount_query}
        rates = PriceManager.assets_to_fiat(value_assets | cost_assets, display_fiat)
        reward_amount = Decimal()
        for asset, amount in air_amount_query:
            reward_amount += amount * rates.get(asset, Decimal(1))
        for asset, amount in cashback_amount_query:
            reward_amount += amount * rates.get(asset, Decimal(1))

        return {
            "reward_asset": display_fiat,
            "reward_amount": amount_to_str(reward_amount, 2),
        }


@ns.route("/remain-cashback")
@respond_with_code
class RewardCenterRemainCashbackResource(Resource):
    REQ_SCOPES = [UserCashbackEquity.CashbackScope.SPOT.name, UserCashbackEquity.CashbackScope.PERPETUAL.name]

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            cashback_scope=EnumField(REQ_SCOPES, required=True),
        )
    )
    def get(cls, **kwargs):
        """ 任务中心-剩余返现USD额度汇总 """
        user = g.user

        display_fiat = "USD"
        cashback_scope = kwargs['cashback_scope']
        q = UserCashbackEquity.query.filter(
            UserCashbackEquity.user_id == user.id,
            UserCashbackEquity.status == UserCashbackEquity.Status.USING,
            UserCashbackEquity.end_time > now(),
        )
        if cashback_scope == UserCashbackEquity.CashbackScope.SPOT.name:
            q = q.filter(
                UserCashbackEquity.cashback_scope.in_(
                    [UserCashbackEquity.CashbackScope.SPOT, UserCashbackEquity.CashbackScope.ALL]
                )
            )
        elif cashback_scope == UserCashbackEquity.CashbackScope.PERPETUAL.name:
            q = q.filter(
                UserCashbackEquity.cashback_scope.in_(
                    [UserCashbackEquity.CashbackScope.PERPETUAL, UserCashbackEquity.CashbackScope.ALL]
                )
            )

        rows = q.with_entities(
            UserCashbackEquity.cost_asset,
            UserCashbackEquity.cost_amount,
            UserCashbackEquity.used_cost_amount,
        ).all()
        remain_amount = Decimal()
        cost_assets = [i.cost_asset for i in rows]
        usd_rates = PriceManager.assets_to_fiat(cost_assets, display_fiat)
        for i in rows:
            usd_rate = usd_rates.get(i.cost_asset, Decimal(1))
            remain_amount += max(i.cost_amount - i.used_cost_amount, Decimal(0)) * usd_rate

        return {
            "remain_asset": display_fiat,
            "remain_amount": amount_to_str(remain_amount, 2),
        }


@ns.route("/cashback-rewards")
@respond_with_code
class CashBackRewardsResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            cashback_scope=EnumField(RewardCenterRemainCashbackResource.REQ_SCOPES, required=True),
        )
    )
    def get(cls, **kwargs):
        """ 任务中心-返现权益奖励弹窗列表 """
        user = g.user

        cashback_scope = kwargs['cashback_scope']
        q = UserCashbackEquity.query.filter(
            UserCashbackEquity.user_id == user.id,
            UserCashbackEquity.status == UserCashbackEquity.Status.USING,
            UserCashbackEquity.end_time > now(),
        )
        if cashback_scope == UserCashbackEquity.CashbackScope.SPOT.name:
            q = q.filter(
                UserCashbackEquity.cashback_scope.in_(
                    [UserCashbackEquity.CashbackScope.SPOT, UserCashbackEquity.CashbackScope.ALL]
                )
            )
        elif cashback_scope == UserCashbackEquity.CashbackScope.PERPETUAL.name:
            q = q.filter(
                UserCashbackEquity.cashback_scope.in_(
                    [UserCashbackEquity.CashbackScope.PERPETUAL, UserCashbackEquity.CashbackScope.ALL]
                )
            )

        rows: List[UserCashbackEquity] = q.order_by(UserCashbackEquity.id.desc()).limit(50).all()  # 目前不分页
        rows.sort(key=CashbackSettleHelper.equity_sort_func_for_using)

        items = []
        for cb in rows:
            item = {
                'reward_id': cb.user_equity_id,
                'value_type': cb.cost_asset,
                'value': cb.cost_amount,
                'created_at': cb.start_time,
                'expired_at': cb.end_time,
                'reward_type': EquityType.CASHBACK.name,
                'status': cb.status.name,
                'extra': {
                    'remain_asset': cb.cost_asset,
                    'remain_amount': cb.remain_cost_amount,
                    'cashback_asset': cb.cashback_asset,
                    'cashback_ratio': quantize_amount(cb.cashback_ratio * Decimal(100), 0),
                    'cashback_scope': cb.cashback_scope.name,
                    'progress': RewardCenterRewardsResource.format_progress(
                        cb.used_cost_amount, cb.cost_amount, cb.cost_asset
                    )
                },
            }
            items.append(item)

        return dict(
            data=items,
        )
