# -*- coding: utf-8 -*-
import json
import time
from collections import defaultdict
from datetime import datetime, timedelta, date
from decimal import Decimal
from enum import Enum
from types import SimpleNamespace
from typing import Callable, Optional, List, NamedTuple

from celery.schedules import crontab
from flask import current_app
from pymysql import OperationalError, ProgrammingError
from sqlalchemy import func
from dateutil.relativedelta import relativedelta

from app.assets import get_asset_config, try_get_asset_config, list_all_assets
from app.business import (
    lock_call, ServerClient, BalanceBusiness, PriceManager, SPOT_ACCOUNT_ID, mem_cached, PerpetualServerClient
)
from app.business.alert import send_alert_notice
from app.business.external_dbs import TradeSummaryDB, PerpetualSummaryDB, TradeLogDB, PerpetualLogDB
from app.business.report.base import BaseMonthlyReporter, BaseReporter
from app.business.site import BusinessSettings
from app.business.utils import AssetComparator
from app.business.utils import yield_query_records_by_time_range
from app.business.amm import sum_amm_transfer_fee
from app.business.bus_referral import BusReferDetailHelper
from app.caches import MarketCache
from app.caches.spot import CetCirculationCache
from app.caches.report import (
    DailyIncomeCache, DailyBalanceIncomeCache, DailyBuyBackAssetSoldCache,
    DailySmallCoinExchangeStartCache, DailySmallCoinExchangeFinishCache,
    DailyTransferToBuyBackProcessCache, DailyBuyBackStartCache,
    DailyBuyBackFinishCache, DailyIncomeProcessResultCache, DailyBuyBackCountCache, DailyBuyBackPriceCache,
    DailyBuyBackOrderPriceCache, DailyBuyBackInitalAmountCache
)
from app.common import CeleryQueues, OrderSideType, PrecisionEnum, BIG_COINS, OrderOption, MarketStatusType
from app.config import config
from app.exceptions import ServiceUnavailable
from app.models import (
    IncomeType, DailyIncomeHistory,
    AssetIncomeBalance, DailyAssetIncomeBalanceHistory,
    db, DailyWithdrawReport,
    AssetConversion, MarginReceivableInterestHistory,
    DailyInvestmentReport, BalanceOperationType,
    DailyIncomeTransferHistory, DailySmallCoinExchangeReport,
    DailyCetBuyBackReport, DailyIncomeReport, MonthlyIncomeReport,
    DailyRealIncomeReport, MonthlyRealIncomeReport, CetDestroyReport,
    ReferralAssetHistory, BusinessAgentReferralAssetHistory, IndirectReferralAssetHistory, MakerCashBackHistory,
    CreditAssetInterestReceivableHistory, DailyLossOrderHistory,
    DailyLossProcessHistory, AssetPrice, User, DailyCetExchangeHistory, DailyAdminNetIncomeReport,
    MonthlyAdminNetIncomeReport, InsuranceExchangeTransferOutHistory,
    DailyRealTimeIncomeTransferHistory, SignOffUserBalanceTransferHistory, CleanedBalanceTransferHistory,
    MonthlyOperatingExpensesReport, TradeRankActivity, UpdateAssetBalance,
    AirdropActivity, DiscountActivity, OperationAmbassadorActivity,
    MonthlyBusinessUserReferralReport, MonthlyBusinessTeamReferralReport, MonthlyBusinessPrReferralReport,
    DiscountActivitySystemOrder, AirdropActivityRewardHistory, TradeRankActivityUserInfo, DiscountActivityRewardHistory,
    AmbassadorActivityUserInfo
)
from app.models.activity import CouponExpenditureStatistics, CouponApplyBusinessParty
from app.models.broker import BrokerReferralAssetHistory
from app.models.exchange import AssetExchangeOrderFee, ExchangeFeeType
from app.models.referral import PackageSettlementHistory
from app.models.spot import WalletAssetRewardHistory
from app.models.staking import StakingIncomeRecord
from app.models.system import SystemAccountSnapshotHistory
from app.models.pledge import PledgeInterestHistory
from app.models.p2p import P2pOrderFeeHistory
from app.models.pre_trading import PreTradingUserOpHistory
from app.models.onchain import OnchainOrder
from app.models.payment import PaymentAssetHedgingHistory
from app.schedules.realtime_income import (
    PERPETUAL_SYSTEM_INCOME_USER_ID,
    SPOT_SYSTEM_INCOME_USER_ID,
)
from app.utils import (
    underscore_to_camel, celery_task,
    route_module_to_celery_queue, datetime_to_str, str_to_datetime, scheduled,
    quantize_amount, amount_to_str,
)
from app.utils.date_ import (convert_datetime, now, today_timestamp_utc, current_timestamp,
                             date_to_datetime)
from app.utils.external_db import TABLE_NOT_EXISTS

route_module_to_celery_queue(__name__, CeleryQueues.DAILY)

SMALL_EXCHANGE_PRICE_PERCENT = Decimal('0.995')

# 缓存七天过期
CACHE_EXPIRED_TIME = 7 * 24 * 60 * 60


@mem_cached(30)
def is_asset_online(asset):
    assets = set(list_all_assets())
    return asset in assets


class IncomeGenerator(object):

    def __init__(self,
                 start_time: datetime,
                 end_time: datetime,
                 income_type: IncomeType
                 ):
        self.start_time = start_time
        self.end_time = end_time
        self.income_type = income_type

    def get_class(self):
        cls_ = f'Daily{underscore_to_camel(self.income_type.name)}Generator'
        for v in IncomeGenerator.__subclasses__():
            if v.__name__ == cls_:
                return v
        raise UnboundLocalError

    @property
    def cache(self):
        return DailyIncomeCache(self.start_time)

    def check_finish(self):
        return self.cache.get_bit(self.income_type.value)

    def check_all_finish(self):
        return self.cache.is_finish()

    # noinspection PyTypeChecker
    def dispatch(self):
        obj = self.get_class()(self.start_time, self.end_time,
                               self.income_type)
        if obj.check_finish():
            return True
        result = obj.run()
        if result:
            self.cache.set_bit(self.income_type.value, True)


class DailySpotTradeFeeGenerator(IncomeGenerator):

    def run(self):
        if not TradeSummaryDB.is_data_completed(self.start_time.date()):
            return False
        try:
            # 币币交易手续费收入, 兼容表未生成的情况
            spot_fee_records = TradeSummaryDB.daily_trade_fee(
                self.start_time.date()
            )
        except OperationalError as e:
            if e.args[0] != TABLE_NOT_EXISTS:
                raise
            else:
                return True

        onchain_fee_map = defaultdict(Decimal)
        for order in yield_query_records_by_time_range(
                table=OnchainOrder,
                start_time=self.start_time - timedelta(hours=1),  # 往前多查1小时数据, 可以认为1小时内订单一定结束了
                end_time=self.end_time,
                select_fields=[
                    OnchainOrder.money_asset,
                    OnchainOrder.fee,
                    OnchainOrder.status,
                    OnchainOrder.finished_at,
                ],
        ):
            if order.status != OnchainOrder.Status.FINISHED:
                continue
            if not order.finished_at:
                continue
            if order.finished_at < self.start_time or order.finished_at >= self.end_time:
                continue
            onchain_fee_map[order.money_asset] += Decimal(order.fee)

        for fee in spot_fee_records:
            if not is_asset_online(fee['asset']):
                continue
            asset, total_fee = fee['asset'], fee['total_fee']
            total_fee += onchain_fee_map[asset]
            trade_fee_record = DailyIncomeHistory(
                report_date=self.start_time.date(),
                asset=asset,
                amount=total_fee,
                income_type=self.income_type
            )
            db.session.add(trade_fee_record)
        db.session.commit()
        return True


class DailyPerpetualTradeFeeGenerator(IncomeGenerator):

    def run(self):
        if not PerpetualSummaryDB.is_data_completed(self.start_time.date()):
            return
        try:
            # 合约交易手续费收入, 兼容表未生成的情况
            fee_records = PerpetualSummaryDB.daily_trade_fee(
                self.start_time.date()
            )
        except OperationalError as e:
            if e.args[0] != TABLE_NOT_EXISTS:
                raise
            else:
                return True
        except ProgrammingError as e:
            if e.args[0] != TABLE_NOT_EXISTS:
                raise
            else:
                return True

        for fee in fee_records:
            if not is_asset_online(fee['asset']):
                continue
            trade_fee_record = DailyIncomeHistory(
                report_date=self.start_time.date(),
                asset=fee['asset'],
                amount=fee['total_fee'],
                income_type=self.income_type
            )
            db.session.add(trade_fee_record)
        db.session.commit()
        return True


class DailyWithdrawFeeGenerator(IncomeGenerator):

    def run(self):
        # 提现手续费收入
        check = DailyWithdrawReport.query.filter(
            DailyWithdrawReport.report_date == self.start_time.date()
        ).first()
        if not check:
            return
        withdraw_fee_items = DailyWithdrawReport.query.filter(
            DailyWithdrawReport.report_date == self.start_time.date()
        ).with_entities(
            DailyWithdrawReport.withdraw_fee,
            DailyWithdrawReport.withdraw_pay_fee,
            DailyWithdrawReport.asset
        ).group_by(
            DailyWithdrawReport.asset
        ).all()

        onchain_gas_fee_map = defaultdict(Decimal)
        for order in yield_query_records_by_time_range(
                table=OnchainOrder,
                start_time=self.start_time - timedelta(hours=1),  # 往前多查1小时数据, 可以认为1小时内订单一定结束了
                end_time=self.end_time,
                select_fields=[
                    OnchainOrder.money_asset,
                    OnchainOrder.gas_fee,
                    OnchainOrder.status,
                    OnchainOrder.finished_at,
                ],
        ):
            if order.status not in {OnchainOrder.Status.FINISHED, OnchainOrder.Status.FAILED}:
                continue
            if not order.finished_at:
                continue
            if order.finished_at < self.start_time or order.finished_at >= self.end_time:
                continue
            onchain_gas_fee_map[order.money_asset] += Decimal(order.gas_fee)

        for withdraw_record in withdraw_fee_items:
            if not is_asset_online(withdraw_record.asset):
                continue
            withdraw_fee = withdraw_record.withdraw_fee
            withdraw_fee += onchain_gas_fee_map[withdraw_record.asset]
            if withdraw_fee != 0:
                withdraw_fee_record = DailyIncomeHistory(
                    report_date=self.start_time.date(),
                    asset=withdraw_record.asset,
                    amount=withdraw_fee,
                    income_type=self.income_type
                )
                db.session.add(withdraw_fee_record)
        db.session.commit()
        return True


class DailyWithdrawPayFeeGenerator(IncomeGenerator):

    def run(self):
        # 提现手续费支出
        check = DailyWithdrawReport.query.filter(
            DailyWithdrawReport.report_date == self.start_time.date()
        ).first()

        if not check:
            return

        withdraw_fee_items = DailyWithdrawReport.query.filter(
            DailyWithdrawReport.report_date == self.start_time.date()
        ).with_entities(
            DailyWithdrawReport.withdraw_fee,
            DailyWithdrawReport.withdraw_pay_fee,
            DailyWithdrawReport.asset
        ).group_by(
            DailyWithdrawReport.asset
        ).all()
        for withdraw_record in withdraw_fee_items:
            if not is_asset_online(withdraw_record.asset):
                continue
            if withdraw_record.withdraw_pay_fee != 0:
                withdraw_pay_fee_record = DailyIncomeHistory(
                    report_date=self.start_time.date(),
                    asset=withdraw_record.asset,
                    amount=-withdraw_record.withdraw_pay_fee,
                    income_type=self.income_type)
                db.session.add(withdraw_pay_fee_record)
        db.session.commit()
        return True


class DailyUserExchangeGenerator(IncomeGenerator):

    def run(self):
        # 碎币兑换收入
        conversion_datas = AssetConversion.query.filter(
            AssetConversion.status == AssetConversion.Status.FINISHED,
            AssetConversion.updated_at >= self.start_time,
            AssetConversion.updated_at < self.end_time
        ).with_entities(
            AssetConversion.asset,
            func.sum(AssetConversion.available).label(
                'conversion_amount')
        ).group_by(
            AssetConversion.asset
        ).all()

        for conversion_data in conversion_datas:
            if not is_asset_online(conversion_data.asset):
                continue
            conversion_record = DailyIncomeHistory(
                report_date=self.start_time.date(),
                asset=conversion_data.asset,
                amount=conversion_data.conversion_amount,
                income_type=self.income_type
            )
            db.session.add(conversion_record)
        db.session.commit()
        return True


class DailyUserExchangePayGenerator(IncomeGenerator):

    def run(self):
        # 碎币兑换支出
        conversion_datas = AssetConversion.query.filter(
            AssetConversion.status == AssetConversion.Status.FINISHED,
            AssetConversion.updated_at >= self.start_time,
            AssetConversion.updated_at < self.end_time
        ).with_entities(
            AssetConversion.trg_asset,
            func.sum(AssetConversion.converted).label(
                'conversion_amount')
        ).group_by(
            AssetConversion.trg_asset
        ).all()

        for conversion_data in conversion_datas:
            if not is_asset_online(conversion_data.trg_asset):
                continue
            conversion_record = DailyIncomeHistory(
                report_date=self.start_time.date(),
                asset=conversion_data.trg_asset,
                amount=-conversion_data.conversion_amount,
                income_type=self.income_type
            )
            db.session.add(conversion_record)
        db.session.commit()
        return True


class DailyMarginInterestGenerator(IncomeGenerator):

    def run(self):
        records = MarginReceivableInterestHistory.query.with_entities(
            MarginReceivableInterestHistory.asset,
            func.sum(MarginReceivableInterestHistory.amount * (
                    1 -
                    MarginReceivableInterestHistory.fund_percent
            )).label(
                "sum_amount")
        ).filter(
            MarginReceivableInterestHistory.created_at >= self.start_time,
            MarginReceivableInterestHistory.created_at < self.end_time,
        ).group_by(MarginReceivableInterestHistory.asset).all()

        for record in records:
            if not is_asset_online(record.asset):
                continue
            interest_record = DailyIncomeHistory(
                report_date=self.start_time.date(),
                asset=record.asset,
                amount=record.sum_amount,
                income_type=self.income_type
            )
            db.session.add(interest_record)
        db.session.commit()
        return True


class DailyPledgeInterestGenerator(IncomeGenerator):
    """ 借贷 利息收入 """
    def run(self):
        model = PledgeInterestHistory
        rows = model.query.filter(
            model.created_at >= self.start_time,
            model.created_at < self.end_time,
        ).group_by(
            model.loan_asset,
        ).with_entities(
            model.loan_asset,
            func.sum(model.amount * (1 - model.fund_percent)).label("sum_amount"),
        ).all()
        for row in rows:
            if not is_asset_online(row.loan_asset):
                continue
            interest_record = DailyIncomeHistory(
                report_date=self.start_time.date(),
                asset=row.loan_asset,
                amount=row.sum_amount,
                income_type=IncomeType.PLEDGE_INTEREST,
            )
            db.session.add(interest_record)
        db.session.commit()
        return True


class DailyStakingInterestGenerator(IncomeGenerator):
    """ 链上质押 利息收入 """
    def run(self):
        query = StakingIncomeRecord.query.filter(
            StakingIncomeRecord.settle_date == self.end_time.date(),
            StakingIncomeRecord.type == StakingIncomeRecord.Type.INCOME,
        ).group_by(
            StakingIncomeRecord.asset,
        ).with_entities(
            StakingIncomeRecord.asset,
            func.sum(StakingIncomeRecord.amount).label("amount"),
        )
        if not query.first():
            return
        for record in query.all():
            if not is_asset_online(record.asset):
                continue
            new_record = DailyIncomeHistory(
                report_date=self.start_time.date(),
                asset=record.asset,
                amount=record.amount,
                income_type=self.income_type
            )
            db.session.add(new_record)
        db.session.commit()
        return True

class DailyInvestmentPayGenerator(IncomeGenerator):

    def run(self):
        # 理财支出
        query = DailyInvestmentReport.query.filter(
            DailyInvestmentReport.report_date == self.start_time.date(),
        )
        if not query.first():
            return
        for record in query.all():
            if not is_asset_online(record.asset):
                continue
            investment_record = DailyIncomeHistory(
                report_date=self.start_time.date(),
                asset=record.asset,
                amount=-record.investment_interest_amount,
                income_type=self.income_type
            )
            db.session.add(investment_record)
        db.session.commit()
        return True
    
class DailyStakingPayGenerator(IncomeGenerator):
    """ 链上质押 奖励支出 """

    def run(self):
        query = StakingIncomeRecord.query.filter(
            StakingIncomeRecord.settle_date == self.end_time.date(),
            StakingIncomeRecord.type == StakingIncomeRecord.Type.PAYMENT,
        ).group_by(
            StakingIncomeRecord.asset,
        ).with_entities(
            StakingIncomeRecord.asset,
            func.sum(StakingIncomeRecord.amount).label("amount"),
        )
        if not query.first():
            return
        for record in query.all():
            if not is_asset_online(record.asset):
                continue
            new_record = DailyIncomeHistory(
                report_date=self.start_time.date(),
                asset=record.asset,
                amount=-record.amount,
                income_type=self.income_type
            )
            db.session.add(new_record)
        db.session.commit()
        return True


class DailyReferPayGenerator(IncomeGenerator):

    def run(self):
        refer_records = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date >= self.start_time.date(),
            ReferralAssetHistory.date < self.end_time.date()
        ).group_by(
            ReferralAssetHistory.asset
        ).with_entities(
            ReferralAssetHistory.asset,
            func.sum(ReferralAssetHistory.amount).label('sum_amount')
        ).all()
        if not refer_records:
            return False
        asset_amount_map = defaultdict(Decimal)
        for r in refer_records:
            asset_amount_map[r.asset] += r.sum_amount

        # 商务代理的返佣
        agent_model = BusinessAgentReferralAssetHistory
        bus_agent_refs = agent_model.query.filter(
            agent_model.date >= self.start_time.date(),
            agent_model.date < self.end_time.date()
        ).group_by(
            agent_model.asset
        ).with_entities(
            agent_model.asset,
            func.sum(agent_model.amount).label('sum_amount'),
        ).all()
        for r in bus_agent_refs:
            asset_amount_map[r.asset] += r.sum_amount

        # 非直客的返佣
        indir_model = IndirectReferralAssetHistory
        indir_refs = indir_model.query.filter(
            indir_model.date >= self.start_time.date(),
            indir_model.date < self.end_time.date()
        ).group_by(
            indir_model.asset
        ).with_entities(
            indir_model.asset,
            func.sum(indir_model.amount).label('sum_amount'),
        ).all()
        for r in indir_refs:
            asset_amount_map[r.asset] += r.sum_amount

        for asset, sum_amount in asset_amount_map.items():
            refer_record = DailyIncomeHistory()
            refer_record.report_date = self.start_time.date()
            refer_record.asset = asset
            refer_record.amount = - abs(sum_amount)
            refer_record.income_type = IncomeType.REFER_PAY
            db.session.add(refer_record)
        db.session.commit()
        return True


class DailyBrokerPayGenerator(IncomeGenerator):

    def run(self):

        is_finish = DailyIncomeProcessResultCache(self.start_time).get_result(
            IncomeType.BROKER_PAY)
        if not is_finish:
            return False
        refer_records = BrokerReferralAssetHistory.query.filter(
            BrokerReferralAssetHistory.date >= self.start_time.date(),
            BrokerReferralAssetHistory.date < self.end_time.date()
        ).group_by(
            BrokerReferralAssetHistory.asset
        ).with_entities(
            BrokerReferralAssetHistory.asset,
            func.sum(BrokerReferralAssetHistory.amount).label('sum_amount')
        ).all()
        for record in refer_records:
            refer_record = DailyIncomeHistory()
            refer_record.report_date = self.start_time.date()
            refer_record.asset = record.asset
            refer_record.amount = - abs(
                record.sum_amount) if record.sum_amount else Decimal()
            refer_record.income_type = IncomeType.BROKER_PAY
            db.session.add(refer_record)
        db.session.commit()
        return True


class DailyRedPacketGenerator(IncomeGenerator):

    def run(self):
        # 移除红包收入
        return True


class DailyWalletAssetRewardGenerator(IncomeGenerator):

    def run(self):
        report_date = self.start_time.date()
        if WalletAssetRewardHistory.query.filter(
            WalletAssetRewardHistory.report_date == report_date,
            WalletAssetRewardHistory.asset == WalletAssetRewardHistory.PLACEHOLDER_ASSET
        ).first() is None:
            return False
        q = WalletAssetRewardHistory.query.filter(
                WalletAssetRewardHistory.report_date == report_date,
        ).all()
        for record in q:
            if not is_asset_online(record.asset):
                continue
            reward_record = DailyIncomeHistory()
            reward_record.report_date = report_date
            reward_record.asset = record.asset
            reward_record.amount = record.amount
            reward_record.income_type = IncomeType.WALLET_ASSET_REWARD
            db.session.add(reward_record)
        db.session.commit()
        return True


class DailyAmmTradePayFeeGenerator(IncomeGenerator):

    def run(self):
        # 减去注入资金池的手续费
        amm_transfer_fees = sum_amm_transfer_fee(self.start_time.date())
        for asset, amount in amm_transfer_fees.items():
            if not is_asset_online(asset):
                continue
            if amount > Decimal('0'):
                record = DailyIncomeHistory(
                    report_date=self.start_time.date(),
                    asset=asset,
                    amount=-abs(amount),
                    income_type=self.income_type
                )
                db.session.add(record)
        db.session.commit()
        return True


class DailyMakerCashbackPayGenerator(IncomeGenerator):
    """ 现货Maker返佣支出（收入报表） """

    def run(self):
        is_finish = DailyIncomeProcessResultCache(self.start_time).get_result(
            IncomeType.MAKER_CASHBACK_PAY)
        if not is_finish:
            return False
        cashback_records = (
            MakerCashBackHistory.query.filter(
                MakerCashBackHistory.report_date >= self.start_time.date(),
                MakerCashBackHistory.report_date < self.end_time.date(),
            )
            .group_by(MakerCashBackHistory.asset)
            .with_entities(
                MakerCashBackHistory.asset,
                func.sum(MakerCashBackHistory.amount).label("sum_amount"),
            )
            .all()
        )
        for record in cashback_records:
            refer_record = DailyIncomeHistory()
            refer_record.report_date = self.start_time.date()
            refer_record.asset = record.asset
            refer_record.amount = (
                -abs(record.sum_amount) if record.sum_amount else Decimal()
            )
            refer_record.income_type = IncomeType.MAKER_CASHBACK_PAY
            db.session.add(refer_record)
        db.session.commit()
        return True


class DailyCreditInterestGenerator(IncomeGenerator):
    """ 授信利息收入（收入报表） """

    def run(self):
        model = CreditAssetInterestReceivableHistory
        receivable_interest_records = (
            model.query.filter(
                model.created_at >= self.start_time.date(),
                model.created_at < self.end_time.date(),
            )
            .group_by(model.asset)
            .with_entities(
                model.asset,
                func.sum(model.receivable_amount).label("sum_amount"),
            )
            .all()
        )
        for record in receivable_interest_records:
            if not is_asset_online(record.asset):
                continue
            refer_record = DailyIncomeHistory()
            refer_record.report_date = self.start_time.date()
            refer_record.asset = record.asset
            refer_record.amount = record.sum_amount
            refer_record.income_type = IncomeType.CREDIT_INTEREST
            db.session.add(refer_record)
        db.session.commit()
        return True


class DailyExchangeFeeGenerator(IncomeGenerator):
    """ Web收取的兑换手续费（收入报表） """

    def run(self):
        model = AssetExchangeOrderFee
        fee_rows = (
            model.query.filter(
                model.status == model.Status.FINISHED,
                model.type == ExchangeFeeType.WEB,
                model.finished_at >= self.start_time.date(),
                model.finished_at < self.end_time.date(),
            )
            .group_by(model.asset)
            .with_entities(
                model.asset,
                func.sum(model.amount).label("sum_amount"),
            )
            .all()
        )
        for row in fee_rows:
            if not is_asset_online(row.asset):
                continue
            income_record = DailyIncomeHistory()
            income_record.report_date = self.start_time.date()
            income_record.asset = row.asset
            income_record.amount = row.sum_amount
            income_record.income_type = IncomeType.EXCHANGE_FEE
            db.session.add(income_record)
        db.session.commit()
        return True


class DailyPreTradingFeeGenerator(IncomeGenerator):
    """ 预测市场的赎回、交割手续费（收入报表） """

    def run(self):
        model = PreTradingUserOpHistory
        op_fee_rows: list[model] = model.query.filter(
            model.status == model.Status.FINISHED,
            model.finished_at >= self.start_time.date(),
            model.finished_at < self.end_time.date(),
        ).group_by(
            model.pledge_asset,
        ).with_entities(
            model.pledge_asset,
            func.sum(model.fee_amount).label("total_fee_amount"),
        ).all()
        asset_fee_map = defaultdict(Decimal)
        for r in op_fee_rows:
            asset_fee_map[r.pledge_asset] += r.total_fee_amount

        for fee_asset, fee_amount in asset_fee_map.items():
            if not is_asset_online(fee_asset):
                continue
            income_record = DailyIncomeHistory()
            income_record.report_date = self.start_time.date()
            income_record.asset = fee_asset
            income_record.amount = fee_amount
            income_record.income_type = IncomeType.PRE_TRADING_FEE
            db.session.add(income_record)
        db.session.commit()
        return True


class DailyFixedExchangeProfitGenerator(IncomeGenerator):
    """ 收入报表 一口价兑换-收入（收付款手续费 + 收付款兑换盈利）"""

    def run(self):
        asset_profit_map = defaultdict(Decimal)
        hg_rows: list[PaymentAssetHedgingHistory] = PaymentAssetHedgingHistory.query.filter(
            PaymentAssetHedgingHistory.status == PaymentAssetHedgingHistory.Status.FINISHED,
            PaymentAssetHedgingHistory.finished_at >= self.start_time.date(),
            PaymentAssetHedgingHistory.finished_at < self.end_time.date(),
        ).with_entities(
            PaymentAssetHedgingHistory.source_asset,
            PaymentAssetHedgingHistory.source_amount,
            PaymentAssetHedgingHistory.source_filled_amount,
            PaymentAssetHedgingHistory.target_asset,
            PaymentAssetHedgingHistory.target_amount,
            PaymentAssetHedgingHistory.target_filled_amount,
        ).all()
        for hg_r in hg_rows:
            source_pl = hg_r.source_amount - hg_r.source_filled_amount
            if source_pl > 0:
                asset_profit_map[hg_r.source_asset] += source_pl
            target_pl = hg_r.target_filled_amount - hg_r.target_amount
            if target_pl > 0:
                asset_profit_map[hg_r.target_asset] += target_pl

        for _asset, profit_amount in asset_profit_map.items():
            if not is_asset_online(_asset):
                continue
            income_record = DailyIncomeHistory()
            income_record.report_date = self.start_time.date()
            income_record.asset = _asset
            income_record.amount = profit_amount
            income_record.income_type = IncomeType.FIXED_EXCHANGE_PROFIT
            db.session.add(income_record)
        db.session.commit()
        return True


class DailyFixedExchangeLossGenerator(IncomeGenerator):
    """ 收入报表 一口价兑换亏损（收付款兑换亏损）"""

    def run(self):
        hg_rows: list[PaymentAssetHedgingHistory] = PaymentAssetHedgingHistory.query.filter(
            PaymentAssetHedgingHistory.status == PaymentAssetHedgingHistory.Status.FINISHED,
            PaymentAssetHedgingHistory.finished_at >= self.start_time.date(),
            PaymentAssetHedgingHistory.finished_at < self.end_time.date(),
        ).with_entities(
            PaymentAssetHedgingHistory.target_asset,
            PaymentAssetHedgingHistory.target_amount,
            PaymentAssetHedgingHistory.target_filled_amount,
        ).all()
        asset_loss_map = defaultdict(Decimal)
        for hg_r in hg_rows:
            # source_amount最多就是用完了，只会有盈利数，不用算source的亏损
            target_pl = hg_r.target_filled_amount - hg_r.target_amount
            if target_pl < 0:
                asset_loss_map[hg_r.target_asset] += target_pl

        for _asset, loss_amount in asset_loss_map.items():
            if not is_asset_online(_asset):
                continue
            income_record = DailyIncomeHistory()
            income_record.report_date = self.start_time.date()
            income_record.asset = _asset
            income_record.amount = loss_amount
            income_record.income_type = IncomeType.FIXED_EXCHANGE_LOSS
            db.session.add(income_record)
        db.session.commit()
        return True


class DailySignOffUserBalanceGenerator(IncomeGenerator):
    """ 注销账户资产（收入报表） """

    def run(self):
        model = SignOffUserBalanceTransferHistory
        transfer_rows = (
            model.query.filter(
                model.status == model.Status.FINISHED,
                model.finished_at >= self.start_time.date(),
                model.finished_at < self.end_time.date(),
            )
            .group_by(model.asset)
            .with_entities(
                model.asset,
                func.sum(model.amount).label("sum_amount"),
            )
            .all()
        )
        for row in transfer_rows:
            if not is_asset_online(row.asset):
                continue
            income_record = DailyIncomeHistory()
            income_record.report_date = self.start_time.date()
            income_record.asset = row.asset
            income_record.amount = row.sum_amount
            income_record.income_type = IncomeType.SIGN_OFF_USER_BALANCE
            db.session.add(income_record)
        db.session.commit()
        return True
    
class DailyCleanedBalanceGenerator(IncomeGenerator):
    """ 小额回收账户资产（收入报表） """

    def run(self):
        model = CleanedBalanceTransferHistory
        transfer_rows = (
            model.query.filter(
                model.status == model.Status.FINISHED,
                model.finished_at >= self.start_time.date(),
                model.finished_at < self.end_time.date(),
            )
            .group_by(model.asset)
            .with_entities(
                model.asset,
                func.sum(model.amount).label("sum_amount"),
            )
            .all()
        )
        for row in transfer_rows:
            if not is_asset_online(row.asset):
                continue
            income_record = DailyIncomeHistory()
            income_record.report_date = self.start_time.date()
            income_record.asset = row.asset
            income_record.amount = row.sum_amount
            income_record.income_type = IncomeType.CLEANED_BALANCE
            db.session.add(income_record)
        db.session.commit()
        return True


class DailyP2pOrderFeeGenerator(IncomeGenerator):
    """ p2p订单-手续费（收入报表） """

    def run(self):
        model = P2pOrderFeeHistory
        fee_rows = model.query.filter(
            model.created_at >= self.start_time.date(),
            model.created_at < self.end_time.date(),
        ).group_by(model.asset).with_entities(
            model.asset,
            func.sum(model.amount).label("sum_amount"),
        ).all()
        for row in fee_rows:
            if not is_asset_online(row.asset):
                continue
            income_record = DailyIncomeHistory()
            income_record.report_date = self.start_time.date()
            income_record.asset = row.asset
            income_record.amount = row.sum_amount
            income_record.income_type = IncomeType.P2P_ORDER_FEE
            db.session.add(income_record)
        db.session.commit()
        return True


class DailyIncomeReporter(BaseReporter):
    model = DailyIncomeReport

    def get_first_date(self) -> Optional[date]:
        record = DailyIncomeHistory.query.order_by(
            DailyIncomeHistory.report_date.asc()).first()
        return record.report_date if record else None

    def run(self, start_date: date, end_date: date):
        query = DailyIncomeHistory.query.filter(
            DailyIncomeHistory.report_date == start_date,
            DailyIncomeHistory.income_type.notin_(
                (
                IncomeType.INCOME_TRANSFER_OUT,
                IncomeType.LOSS_BALANCE_PAY,
                IncomeType.LOSS_BALANCE,
                IncomeType.PLATFORM_EXCHANGE,
                IncomeType.PLATFORM_EXCHANGE_PAY,
            ))
        ).with_entities(
            DailyIncomeHistory.asset,
            DailyIncomeHistory.amount
        )
        assets = set([v.asset for v in query])
        total_amount_dict = defaultdict(Decimal)
        pay_amount_dict = defaultdict(Decimal)
        net_amount_dict = defaultdict(Decimal)
        asset_rates = AssetPrice.get_close_price_map(start_date)
        for record in query:
            net_amount_dict[record.asset] += record.amount
            if record.amount >= Decimal():
                total_amount_dict[record.asset] += record.amount
            else:
                pay_amount_dict[record.asset] += record.amount
        for asset in assets:
            r = DailyIncomeReport(
                report_date=start_date,
                asset=asset,
                total_amount=total_amount_dict[asset],
                pay_amount=pay_amount_dict[asset],
                net_amount=net_amount_dict[asset],
                net_usd=asset_rates.get(asset, Decimal()) * net_amount_dict[asset]
            )
            db.session.add(r)
        db.session.commit()


class MonthlyIncomeReporter(BaseMonthlyReporter):
    daily_model = DailyIncomeReport
    monthly_model = MonthlyIncomeReport

    def get_first_month(self):
        return None

    def run(self, start_month: date, end_month: date):
        query = DailyIncomeReport.query.filter(
            DailyIncomeReport.report_date >= start_month,
            DailyIncomeReport.report_date < end_month
        ).with_entities(
            DailyIncomeReport.asset,
            func.sum(DailyIncomeReport.total_amount).label('month_amount'),
            func.sum(DailyIncomeReport.pay_amount).label('month_pay_amount'),
            func.sum(DailyIncomeReport.net_amount).label('month_net_amount'),
            func.sum(DailyIncomeReport.net_usd).label('month_net_usd'),
        ).group_by(
            self.daily_model.asset
        )
        for v in query:
            record = self.monthly_model.get_or_create(report_date=start_month, asset=v.asset)
            record.total_amount = v.month_amount
            record.pay_amount = v.month_pay_amount
            record.net_amount = v.month_net_amount
            record.net_usd = v.month_net_usd
            db.session.add(record)
        db.session.commit()


class DailyRealIncomeReporter(BaseReporter):
    model = DailyRealIncomeReport

    def get_first_date(self) -> Optional[date]:
        record = DailyRealIncomeReport.query.order_by(
            DailyRealIncomeReport.report_date.asc()).first()
        return record.report_date if record else None

    def run(self, start_date: date, end_date: date):
        query = DailyIncomeTransferHistory.query.filter(
            DailyIncomeTransferHistory.report_date == start_date,
            DailyIncomeTransferHistory.transfer_type.in_(
                (DailyIncomeTransferHistory.TransferType.INCOME_TO_ADMIN_TRANSFER,
                 DailyIncomeTransferHistory.TransferType.BUYBACK_TRANSFER))
        ).with_entities(
            DailyIncomeTransferHistory.asset,
            DailyIncomeTransferHistory.amount
        )

        assets = set([v.asset for v in query])
        asset_rates = AssetPrice.get_close_price_map(start_date)
        amount_dict = defaultdict(Decimal)
        usd_dict = defaultdict(Decimal)
        for record in query:
            amount_dict[record.asset] += record.amount
            usd_dict[record.asset] += record.amount * asset_rates.get(record.asset, Decimal())
        for asset in assets:
            r = DailyRealIncomeReport(
                report_date=start_date,
                asset=asset,
                amount=amount_dict[asset],
                usd=usd_dict[asset]
            )
            db.session.add(r)
        db.session.commit()


class MonthlyRealIncomeReporter(BaseMonthlyReporter):
    daily_model = DailyRealIncomeReport
    monthly_model = MonthlyRealIncomeReport

    def get_first_month(self):
        return None

    def run(self, start_month: date, end_month: date):
        query = DailyRealIncomeReport.query.filter(
            DailyRealIncomeReport.report_date >= start_month,
            DailyRealIncomeReport.report_date < end_month
        ).with_entities(
            DailyRealIncomeReport.asset,
            func.sum(DailyRealIncomeReport.amount).label('month_amount'),
            func.sum(DailyRealIncomeReport.usd).label('month_usd'),
        ).group_by(
            self.daily_model.asset
        )
        for v in query:
            record = self.monthly_model.get_or_create(report_date=start_month, asset=v.asset)
            record.amount = v.month_amount
            record.usd = v.month_usd
            db.session.add(record)
        db.session.commit()


class DailyAdminNetIncomeReporter(BaseReporter):
    model = DailyAdminNetIncomeReport

    def get_first_date(self):
        record = DailyIncomeTransferHistory.query.order_by(
            DailyIncomeTransferHistory.report_date.asc()).first()
        return record.report_date if record else None

    def run(self, start_date: date, end_date: date):
        report_date = start_date
        income_transfer_data = self.records_from_income_transfer(report_date)  # 交易账号划转来源
        insurance_transfer_data = self.records_from_insurance_transfer(report_date)  # 保险基金划转来源
        cet_exchange_data = self.records_from_cet_exchange(report_date)  # CET回购账号划转来源
        assets_rate = AssetPrice.get_close_price_map(report_date)
        for asset in BIG_COINS:
            amount = income_transfer_data.get(asset, 0) + insurance_transfer_data.get(asset, 0) \
                     + cet_exchange_data.get(asset, 0)
            usd = amount * assets_rate.get(asset, Decimal(0))
            record = self.model(
                report_date=report_date,
                asset=asset,
                amount=amount,
                usd=usd
            )
            db.session.add(record)
        db.session.commit()

    @staticmethod
    def records_from_income_transfer(report_date):
        """交易账号划转到Admin账号的币种记录"""
        transfer_model = DailyIncomeTransferHistory
        rows = transfer_model.query.filter(
            transfer_model.report_date == report_date,
            transfer_model.transfer_type == transfer_model.TransferType.INCOME_TO_ADMIN_TRANSFER,
            transfer_model.status == transfer_model.StatusType.PASS,
            transfer_model.asset.in_(BIG_COINS)
        ).all()
        return {x.asset: x.amount for x in rows}

    @staticmethod
    def records_from_insurance_transfer(report_date):
        """保险基金划转到Admin账号的币种记录"""
        admin_user_id = config['OFFICIAL_ADMIN_USER_ID']
        transfer_model = InsuranceExchangeTransferOutHistory
        rows = transfer_model.query.filter(
            transfer_model.report_date == report_date,
            transfer_model.status == transfer_model.Status.FINISHED,
            transfer_model.to_user_id == admin_user_id,
            transfer_model.asset.in_(BIG_COINS)
        ).all()
        return {x.asset: x.amount for x in rows}

    @staticmethod
    def records_from_cet_exchange(report_date):
        """CET回购账户划转到Admin账号的币种记录"""
        cet_model = DailyCetExchangeHistory
        admin_user_id = config['OFFICIAL_ADMIN_USER_ID']
        rows = cet_model.query.filter(
            cet_model.report_date == report_date,
            cet_model.user_id == admin_user_id,
            cet_model.status == cet_model.StatusType.FINISHED,
            cet_model.asset != 'CET'
        ).all()
        return {x.asset: x.amount for x in rows}


class MonthlyAdminNetIncomeReporter(BaseMonthlyReporter):
    daily_model = DailyAdminNetIncomeReport
    monthly_model = MonthlyAdminNetIncomeReport

    def get_first_month(self):
        return None

    def run(self, start_month: date, end_month: date):
        query = self.daily_model.query.filter(
            self.daily_model.report_date >= start_month,
            self.daily_model.report_date < end_month
        ).with_entities(
            self.daily_model.asset,
            func.sum(self.daily_model.amount).label('month_amount'),
            func.sum(self.daily_model.usd).label('month_usd'),
        ).group_by(
            self.daily_model.asset
        )
        for v in query:
            record = self.monthly_model.get_or_create(report_date=start_month, asset=v.asset)
            record.amount = v.month_amount
            record.usd = v.month_usd
            db.session.add(record)
        db.session.commit()


class MonthlyOperatingExpensesReporter(BaseMonthlyReporter):
    daily_model = MonthlyOperatingExpensesReport
    monthly_model = MonthlyOperatingExpensesReport

    def get_first_month(self):
        return date(2024, 1, 1)

    def _get_coupon_record(self, start_month: date, end_month: date):
        total_usd = CouponExpenditureStatistics.query.filter(
            CouponExpenditureStatistics.report_date >= start_month,
            CouponExpenditureStatistics.report_date < end_month,
            CouponExpenditureStatistics.business_party == CouponApplyBusinessParty.ALL,
        ).with_entities(
            func.sum(CouponExpenditureStatistics.expenditure_usd).label("total_usd")
        ).scalar() or Decimal(0)

        new_row = self.monthly_model.get_or_create(
            report_date=start_month,
            type=self.monthly_model.Type.COUPON
        )
        new_row.expenses_usd = total_usd
        db.session.add(new_row)
        db.session.flush()
        return new_row

    def _get_trade_rank_record(self, start_month: date, end_month: date):
        activities = TradeRankActivity.query.filter(
            TradeRankActivity.funding_source == TradeRankActivity.FundingSource.COMPANY_EXPENSES,
            TradeRankActivity.status == TradeRankActivity.Status.FINISHED,
        ).all()
        activity_ids = [r.id for r in activities]
        gifts = TradeRankActivityUserInfo.query.filter(
            TradeRankActivityUserInfo.trade_activity_id.in_(activity_ids),
            TradeRankActivityUserInfo.report_at >= start_month,
            TradeRankActivityUserInfo.report_at < end_month
        ).group_by(
            TradeRankActivityUserInfo.gift_asset
        ).with_entities(
            TradeRankActivityUserInfo.gift_asset,
            func.sum(TradeRankActivityUserInfo.gift_amount).label('gift_amount')
        ).all()

        total_usd = Decimal(0)
        prices = PriceManager.assets_to_usd()
        for r in gifts:
            total_usd += prices.get(r.gift_asset, Decimal('0')) * r.gift_amount
        new_row = self.monthly_model.get_or_create(
            report_date=start_month,
            type=self.monthly_model.Type.TRADE_RANK
        )
        new_row.expenses_usd = total_usd
        db.session.add(new_row)
        db.session.flush()
        return new_row

    def _get_airdrop_record(self, start_month: date, end_month: date):
        activities = AirdropActivity.query.filter(
            AirdropActivity.funding_source == AirdropActivity.FundingSource.COMPANY_EXPENSES,
            AirdropActivity.status.in_([AirdropActivity.StatusType.ONLINE, AirdropActivity.StatusType.FINISHED]),
        ).all()
        activity_ids = [r.id for r in activities]
        gifts = AirdropActivityRewardHistory.query.filter(
            AirdropActivityRewardHistory.airdrop_activity_id.in_(activity_ids),
            AirdropActivityRewardHistory.created_at >= start_month,
            AirdropActivityRewardHistory.created_at < end_month,
            AirdropActivityRewardHistory.status == AirdropActivityRewardHistory.Status.FINISHED,
        ).group_by(
            AirdropActivityRewardHistory.asset
        ).with_entities(
            AirdropActivityRewardHistory.asset,
            func.sum(AirdropActivityRewardHistory.amount).label('amount')
        ).all()

        total_usd = Decimal(0)
        prices = PriceManager.assets_to_usd()
        for r in gifts:
            total_usd += prices.get(r.asset, Decimal('0')) * r.amount
        new_row = self.monthly_model.get_or_create(
            report_date=start_month,
            type=self.monthly_model.Type.AIRDROP
        )
        new_row.expenses_usd = total_usd
        db.session.add(new_row)
        db.session.flush()
        return new_row

    def _get_dibs_record(self, start_month: date, end_month: date):
        activities = DiscountActivity.query.filter(
            DiscountActivity.status == DiscountActivity.StatusType.FINISHED,
        ).all()
        activity_ids = [r.id for r in activities]
        filter_activity_ids = []
        gifts = DiscountActivityRewardHistory.query.filter(
            DiscountActivityRewardHistory.discount_activity_id.in_(activity_ids)
        ).group_by(
            DiscountActivityRewardHistory.discount_activity_id
        ).with_entities(
            DiscountActivityRewardHistory.discount_activity_id,
            func.min(DiscountActivityRewardHistory.created_at).label('created_at')
        ).all()
        for r in gifts:
            if date_to_datetime(end_month) > r.created_at >= date_to_datetime(start_month):
                filter_activity_ids.append(r.discount_activity_id)

        system_order_query = DiscountActivitySystemOrder.query.filter(
            DiscountActivitySystemOrder.discount_activity_id.in_(filter_activity_ids),
            DiscountActivitySystemOrder.status == DiscountActivitySystemOrder.Status.FINISHED,
        ).with_entities(
            DiscountActivitySystemOrder.discount_activity_id,
            func.sum(DiscountActivitySystemOrder.source_asset_traded_amount).label("source_asset_traded_amount"),
        ).group_by(
            DiscountActivitySystemOrder.discount_activity_id
        ).all()
        system_order_map = {i.discount_activity_id: i.source_asset_traded_amount for i in system_order_query}

        total_usd = Decimal(0)
        for r in activities:
            if r.id not in filter_activity_ids:
                continue
            usd = r.total_amount * PriceManager.asset_to_usd(r.asset) * r.discount_type.value
            if amount := system_order_map.get(r.id):
                usd = amount * PriceManager.asset_to_usd(DiscountActivity.PAY_ASSET)
            total_usd += usd

        new_row = self.monthly_model.get_or_create(
            report_date=start_month,
            type=self.monthly_model.Type.DIBS
        )
        new_row.expenses_usd = total_usd
        db.session.add(new_row)
        db.session.flush()
        return new_row

    def _get_activity_record(self, start_month: date, end_month: date):
        rows = UpdateAssetBalance.query.filter(
            UpdateAssetBalance.status == UpdateAssetBalance.Status.FINISHED,
            UpdateAssetBalance.type == UpdateAssetBalance.Type.OPERATION_EXPENSE,
            UpdateAssetBalance.updated_at >= start_month,
            UpdateAssetBalance.updated_at < end_month,
            UpdateAssetBalance.amount < Decimal(0),
        ).group_by(
            UpdateAssetBalance.asset
        ).with_entities(
            UpdateAssetBalance.asset,
            func.sum(UpdateAssetBalance.amount).label('amount')
        ).all()

        total_usd = Decimal(0)
        prices = PriceManager.assets_to_usd()
        for r in rows:
            total_usd += prices.get(r.asset, Decimal('0')) * abs(r.amount)
        new_row = self.monthly_model.get_or_create(
            report_date=start_month,
            type=self.monthly_model.Type.ACTIVITY
        )
        new_row.expenses_usd = total_usd
        db.session.add(new_row)
        db.session.flush()
        return new_row

    def _get_ambassador_record(self, start_month: date, end_month: date):
        activities = OperationAmbassadorActivity.query.with_entities(
            OperationAmbassadorActivity.id,
            OperationAmbassadorActivity.gift_asset,
        ).filter(
            OperationAmbassadorActivity.status == OperationAmbassadorActivity.Status.FINISHED,
        ).all()
        activity_mapping = {r.id: r.gift_asset for r in activities}
        gifts = AmbassadorActivityUserInfo.query.filter(
            AmbassadorActivityUserInfo.activity_id.in_(activity_mapping.keys()),
            AmbassadorActivityUserInfo.report_at >= start_month,
            AmbassadorActivityUserInfo.report_at < end_month
        ).group_by(
            AmbassadorActivityUserInfo.activity_id
        ).with_entities(
            AmbassadorActivityUserInfo.activity_id,
            func.sum(AmbassadorActivityUserInfo.gift_amount).label('gift_amount'),
        ).all()
        total_usd = Decimal(0)
        prices = PriceManager.assets_to_usd()
        for r in gifts:
            gift_asset = activity_mapping[r.activity_id]
            total_usd += prices.get(gift_asset, Decimal('0')) * r.gift_amount

        new_row = self.monthly_model.get_or_create(
            report_date=start_month,
            type=self.monthly_model.Type.AMBASSADOR
        )
        new_row.expenses_usd = total_usd
        db.session.add(new_row)
        db.session.flush()
        return new_row

    def _get_accident_record(self, start_month: date, end_month: date):
        user_id = config['OFFICIAL_ADMIN_USER_ID']
        rows = UpdateAssetBalance.query.filter(
            UpdateAssetBalance.status == UpdateAssetBalance.Status.FINISHED,
            UpdateAssetBalance.type == UpdateAssetBalance.Type.ACCIDENT_EXPENSE,
            UpdateAssetBalance.user_id == user_id,
            UpdateAssetBalance.updated_at >= start_month,
            UpdateAssetBalance.updated_at < end_month,
            UpdateAssetBalance.amount < Decimal(0),
        ).group_by(
            UpdateAssetBalance.asset
        ).with_entities(
            UpdateAssetBalance.asset,
            func.sum(UpdateAssetBalance.amount).label('amount')
        ).all()

        total_usd = Decimal(0)
        prices = PriceManager.assets_to_usd()
        for r in rows:
            total_usd += prices.get(r.asset, Decimal('0')) * abs(r.amount)
        new_row = self.monthly_model.get_or_create(
            report_date=start_month,
            type=self.monthly_model.Type.ACCIDENT
        )
        new_row.expenses_usd = total_usd
        db.session.add(new_row)
        db.session.flush()
        return new_row

    def _get_business_record(self, start_month: date, end_month: date):
        user_total_amount = MonthlyBusinessUserReferralReport.query.filter(
            MonthlyBusinessUserReferralReport.report_date >= start_month,
            MonthlyBusinessUserReferralReport.report_date < end_month,
        ).with_entities(
            func.sum(MonthlyBusinessUserReferralReport.refer_total_amount).label("refer_total_amount")
        ).scalar() or Decimal(0)
        team_total_amount = MonthlyBusinessTeamReferralReport.query.filter(
            MonthlyBusinessTeamReferralReport.report_date >= start_month,
            MonthlyBusinessTeamReferralReport.report_date < end_month,
        ).with_entities(
            func.sum(MonthlyBusinessTeamReferralReport.refer_total_amount).label("refer_total_amount")
        ).scalar() or Decimal(0)
        pr_total_amount = MonthlyBusinessPrReferralReport.query.filter(
            MonthlyBusinessPrReferralReport.report_date >= start_month,
            MonthlyBusinessPrReferralReport.report_date < end_month,
        ).with_entities(
            func.sum(MonthlyBusinessPrReferralReport.refer_total_amount).label("refer_total_amount")
        ).scalar() or Decimal(0)

        total_amount = user_total_amount + team_total_amount + pr_total_amount
        total_usd = total_amount * PriceManager.asset_to_usd(BusReferDetailHelper.REFER_ASSET)
        new_row = self.monthly_model.get_or_create(
            report_date=start_month,
            type=self.monthly_model.Type.BUSINESS
        )
        new_row.expenses_usd = total_usd
        db.session.add(new_row)
        db.session.flush()
        return new_row
    
    def _get_ambassador_package_record(self, start_month: date, end_month: date):
        rows = PackageSettlementHistory.query.filter(
            PackageSettlementHistory.settlement_time == end_month,
            PackageSettlementHistory.settled_amount > 0,
        ).with_entities(
            PackageSettlementHistory.asset,
            PackageSettlementHistory.settled_amount,
        ).all()
        total_usd = Decimal(0)
        prices = PriceManager.assets_to_usd()
        for r in rows:
            total_usd += prices.get(r.asset, Decimal('0')) * r.settled_amount
        new_row = self.monthly_model.get_or_create(
            report_date=start_month,
            type=self.monthly_model.Type.AMBASSADOR_PACKAGE
        )
        new_row.expenses_usd = total_usd
        db.session.add(new_row)
        db.session.flush()
        return new_row

    def _get_all_record(self, start_month: date, rows):
        total_usd = Decimal()
        for row in rows:
            total_usd += row.expenses_usd
        new_row = self.monthly_model.get_or_create(
            report_date=start_month, type=self.monthly_model.Type.ALL
        )
        new_row.expenses_usd = total_usd
        db.session.add(new_row)
        db.session.flush()

    def run(self, start_month: date, end_month: date):
        new_rows = [
            self._get_coupon_record(start_month, end_month),
            self._get_trade_rank_record(start_month, end_month),
            self._get_airdrop_record(start_month, end_month),
            self._get_dibs_record(start_month, end_month),
            self._get_activity_record(start_month, end_month),
            self._get_ambassador_record(start_month, end_month),
            self._get_accident_record(start_month, end_month),
            self._get_business_record(start_month, end_month),
            self._get_ambassador_package_record(start_month, end_month)
        ]
        self._get_all_record(start_month, new_rows)
        db.session.commit()


@celery_task
@lock_call(with_args=True)
def run_daily_income_task(
        start_time: str,
        end_time: str,
        income_type: int):
    start = str_to_datetime(start_time)
    end = str_to_datetime(end_time)
    IncomeGenerator(start, end, IncomeType(income_type)).dispatch()


class IncomeHistoryReporter(object):

    def __init__(self, start_time: datetime, end_time: datetime):
        self.start_time = start_time
        self.end_time = end_time

    def run(self):
        if DailyIncomeCache(self.start_time).is_finish():
            return True
        for v in IncomeType:
            if v in DailyIncomeCache.EXCLUDE_INCOME_TYPES:
                continue
            run_daily_income_task.delay(datetime_to_str(self.start_time),
                                        datetime_to_str(self.end_time),
                                        v.value)


class BaseProcessor(object):
    def __init__(self, report_date: date, cache_class: type):
        self.report_date = report_date
        self.cache = cache_class(self.report_date)

    def check_operation_finish(self, operation: Enum):
        if self.cache.get_bit(operation.value):
            return True
        return False

    def set_operation_finish(self, operation: BalanceOperationType):
        self.cache.set_bit(operation.value, True)

    def process(self):
        if self.cache.is_finish():
            return True
        for v in self.cache.enum:
            if self.check_operation_finish(v):
                continue
            else:
                getattr(self, v.name.lower())()
                self.set_operation_finish(v)


class IncomeBalanceProcessor(BaseProcessor):

    @classmethod
    def get_balance_record(cls, asset: str):
        record = AssetIncomeBalance.query.filter(
            AssetIncomeBalance.asset == asset
        ).first()
        if not record:
            r = AssetIncomeBalance(
                asset=asset,
                balance=Decimal()
            )
            db.session.add(r)
            db.session.commit()
            return r
        return record

    def process_realtime_transfer_history(self):
        # get from server snapshot
        if DailyRealTimeIncomeTransferHistory.query.filter(
                DailyRealTimeIncomeTransferHistory.report_date == self.report_date
        ).first():
            return
        today_ts = today_timestamp_utc()
        spot_table = TradeLogDB.slice_balance_table(today_ts)
        perpetual_table = PerpetualLogDB.slice_balance_table(today_ts)
        spot_balance_result = defaultdict(Decimal)
        perpetual_balance_result = defaultdict(Decimal)
        if not spot_table or not perpetual_table:
            current_app.logger.warning(f"{today_ts} spot or perpetual table doesn't exist, use web snapshot")
            snapshot_query = SystemAccountSnapshotHistory.query.filter(
                SystemAccountSnapshotHistory.report_date == self.report_date
            ).all()
            spot_record_data = {}
            perpetual_record_data = {}
            for _r in snapshot_query:
                if _r.system_type == SystemAccountSnapshotHistory.SystemType.SPOT:
                    spot_record_data = json.loads(_r.assets_data)
                if _r.system_type == SystemAccountSnapshotHistory.SystemType.PERPETUAL:
                    perpetual_record_data = json.loads(_r.assets_data)
            if not spot_record_data or not perpetual_record_data:
                raise ValueError(
                    f"{today_ts} spot or perpetual table and web snapshot doesn't exist")
            for asset, _balance in spot_record_data.items():
                if Decimal(_balance) == Decimal():
                    continue
                spot_balance_result[asset] += \
                    quantize_amount(Decimal(_balance), PrecisionEnum.COIN_PLACES)
            for asset, _balance in perpetual_record_data.items():
                if Decimal(_balance) == Decimal():
                    continue
                perpetual_balance_result[asset] += quantize_amount(Decimal(_balance),
                                                                   PrecisionEnum.COIN_PLACES)

        else:
            spot_rows = spot_table.select(
                *['asset', 'SUM(balance) balance_sum'],
                where=f'`user_id` = {SPOT_SYSTEM_INCOME_USER_ID}',
                group_by="asset",
            )
            perpetual_rows = perpetual_table.select(
                *['asset', 'SUM(balance) balance_sum'],
                where=f'`user_id` = {PERPETUAL_SYSTEM_INCOME_USER_ID}',
                group_by="asset",
            )
            for (asset, _balance) in spot_rows:
                if _balance == Decimal():
                    continue
                spot_balance_result[asset] += _balance
            for (asset, _balance) in perpetual_rows:
                if _balance == Decimal():
                    continue
                perpetual_balance_result[asset] += quantize_amount(_balance, PrecisionEnum.COIN_PLACES)
        records = []

        for asset, _balance in spot_balance_result.items():
            if not is_asset_online(asset):
                continue
            r = DailyRealTimeIncomeTransferHistory(
                report_date=self.report_date,
                asset=asset,
                amount=_balance,
                business_type=DailyRealTimeIncomeTransferHistory.BusinessType.SPOT,
                status=DailyRealTimeIncomeTransferHistory.StatusType.CREATED,
            )
            records.append(r)
        for asset, _balance in perpetual_balance_result.items():
            if not is_asset_online(asset):
                continue
            r = DailyRealTimeIncomeTransferHistory(
                report_date=self.report_date,
                asset=asset,
                amount=_balance,
                business_type=DailyRealTimeIncomeTransferHistory.BusinessType.PERPETUAL,
                status=DailyRealTimeIncomeTransferHistory.StatusType.CREATED,
            )
            records.append(r)
        if len(records) > 0:
            db.session.add_all(records)
            db.session.commit()

    def process_realtime_transfer_balance(self):
        client = ServerClient()
        p_client = PerpetualServerClient()
        q = DailyRealTimeIncomeTransferHistory.query.filter(
            DailyRealTimeIncomeTransferHistory.report_date ==
            self.report_date,
            DailyRealTimeIncomeTransferHistory.status ==
            DailyRealTimeIncomeTransferHistory.StatusType.CREATED,
        ).all()
        for v in q:
            if v.business_type == DailyRealTimeIncomeTransferHistory.BusinessType.PERPETUAL:
                p_client.add_user_balance(
                    PERPETUAL_SYSTEM_INCOME_USER_ID,
                    v.asset,
                    str(-v.amount),
                    BalanceBusiness.REALTIME_TRANSFER,
                    v.id
                )
            else:
                client.add_user_balance(
                    SPOT_SYSTEM_INCOME_USER_ID,
                    v.asset,
                    str(-v.amount),
                    BalanceBusiness.REALTIME_TRANSFER,
                    v.id
                )
            v.status = DailyRealTimeIncomeTransferHistory.StatusType.FINISHED
            db.session.commit()

    def process_income(self):
        q = DailyIncomeHistory.query.filter(
            DailyIncomeHistory.report_date == self.report_date,
            DailyIncomeHistory.income_type.in_(
                [IncomeType.INVESTMENT_PAY, IncomeType.BROKER_PAY,
                 IncomeType.REFER_PAY, IncomeType.MAKER_CASHBACK_PAY, 
                 IncomeType.STAKING_PAY, IncomeType.STAKING_INTEREST,
                 IncomeType.WALLET_ASSET_REWARD, IncomeType.FIXED_EXCHANGE_LOSS,
                 ]
            )
        )
        transfer_q = DailyRealTimeIncomeTransferHistory.query.filter(
            DailyRealTimeIncomeTransferHistory.report_date == self.report_date,
        ).group_by(
            DailyRealTimeIncomeTransferHistory.asset
        ).with_entities(
            DailyRealTimeIncomeTransferHistory.asset,
            func.sum(DailyRealTimeIncomeTransferHistory.amount).label("total_amount")
        )
        assets = set([v.asset for v in q] + [v.asset for v in transfer_q])
        balance_record_map = {
            v: self.get_balance_record(v)
            for v in assets
        }
        for v in transfer_q:
            total_balance_record = balance_record_map[v.asset]
            record = DailyAssetIncomeBalanceHistory(
                report_date=self.report_date,
                asset=v.asset,
                amount=v.total_amount,
                # 变动后的余额
                balance=total_balance_record.balance + v.total_amount,
                income_type=IncomeType.REALTIME_TRANSFER
            )
            total_balance_record.balance += v.total_amount
            db.session.add(record)

        for v in q:
            total_balance_record = balance_record_map[v.asset]
            record = DailyAssetIncomeBalanceHistory(
                report_date=self.report_date,
                asset=v.asset,
                amount=v.amount,
                # 变动后的余额
                balance=total_balance_record.balance + v.amount,
                income_type=v.income_type
            )
            total_balance_record.balance += v.amount
            db.session.add(record)
        db.session.commit()

    def process_transfer(self):
        q = AssetIncomeBalance.query.all()
        for v in q:
            if not is_asset_online(v.asset):
                continue
            if v.balance <= Decimal():
                continue

            daily_transfer_record = DailyIncomeHistory(
                report_date=self.report_date,
                asset=v.asset,
                amount=-v.balance,
                income_type=IncomeType.INCOME_TRANSFER_OUT
            )
            balance_transfer_record = DailyAssetIncomeBalanceHistory(
                report_date=self.report_date,
                asset=v.asset,
                amount=-v.balance,
                balance=Decimal(),
                income_type=IncomeType.INCOME_TRANSFER_OUT
            )
            v.balance = Decimal()
            db.session.add(daily_transfer_record)
            db.session.add(balance_transfer_record)
            db.session.commit()

    def process_balance(self):
        q = DailyIncomeHistory.query.filter(
            DailyIncomeHistory.report_date == self.report_date,
            DailyIncomeHistory.income_type == IncomeType.INCOME_TRANSFER_OUT
        )
        small_coin_user_id = config['SMALL_COIN_EXCHANGE_USER_ID']
        client = ServerClient(current_app.logger)
        for v in q:
            # 检查是否已经划转成功
            record = DailyIncomeTransferHistory.query.filter(
                DailyIncomeTransferHistory.report_date == self.report_date,
                DailyIncomeTransferHistory.user_id == small_coin_user_id,
                DailyIncomeTransferHistory.asset == v.asset,
                DailyIncomeTransferHistory.transfer_type ==
                DailyIncomeTransferHistory.TransferType.SMALL_COIN_TRANSFER
            ).first()
            if not record:
                record = DailyIncomeTransferHistory(
                    report_date=self.report_date,
                    user_id=small_coin_user_id,
                    asset=v.asset,
                    amount=abs(v.amount),
                    transfer_type=
                    DailyIncomeTransferHistory.TransferType.SMALL_COIN_TRANSFER,
                    status=DailyIncomeTransferHistory.StatusType.CREATE
                )
                db.session.add(record)
                db.session.commit()

            if record.status == DailyIncomeTransferHistory.StatusType.CREATE:
                # 未划转或者划转失败则重试
                try:
                    client.add_user_balance(
                        user_id=small_coin_user_id,
                        asset=v.asset,
                        amount=abs(v.amount),
                        business=BalanceBusiness.SMALL_COIN_TRANSFER,
                        business_id=record.id,
                        detail={
                            "remark":
                                f"income transfer to small coin"
                                f" user {small_coin_user_id}"
                        },
                    )
                    record.status = DailyIncomeTransferHistory.StatusType.PASS
                    db.session.commit()
                except ServerClient.BadResponse:
                    current_app.logger.error(
                        f"income transfer to small coin user account "
                        f"{v.asset}"
                        f" {abs(v.amount)} {small_coin_user_id} error"
                    )
                    raise
        balance_result = client.get_user_balances(small_coin_user_id)
        # record asset to cache
        cache = DailySmallCoinExchangeStartCache(self.report_date)
        for asset, balance in balance_result.items():
            if Decimal(balance['available']) + Decimal(balance['frozen']) > Decimal():
                cache.hset(asset, amount_to_str(Decimal(balance['available']) + Decimal(balance['frozen'])))


class SmallCoinUserExchangeHelper(object):

    @classmethod
    def get_market_mapping(cls):
        coin_market = defaultdict(list)
        cache = MarketCache.online_markets_detail()
        for market, detail in cache.items():
            if detail['base_asset'] not in BIG_COINS and \
                    detail['quote_asset'] in BIG_COINS:
                coin_market[detail['base_asset']].append(detail)
        for small_coin in coin_market:
            market_list = coin_market[small_coin]
            coin_market[small_coin] = sorted(
                market_list, key=lambda x:
                AssetComparator(x['quote_asset']))
        return coin_market

    @classmethod
    def cancel_market_orders(cls, user_id: int):
        client = ServerClient(current_app.logger)
        client.cancel_user_all_order(user_id, -1, None)
        try:
            p_client = PerpetualServerClient(current_app.logger)
            p_client.cancel_batch_orders(user_id, None, [])
        except Exception as e:
            current_app.logger.warning(f"cancel user {user_id} err: {e!r}")


class LossBalanceHelper(object):
    order_assets = ['USDT', 'BTC', 'ETH', 'BCH']

    @classmethod
    def get_loss_asset_data(cls):
        balance_query = AssetIncomeBalance.query.filter(
            AssetIncomeBalance.balance < Decimal('0')
        ).all()
        loss_asset_data = {
            v.asset: v.balance for v in balance_query
        }
        return loss_asset_data

    @classmethod
    def get_asset_market_mapping(cls, assets: List):
        coin_market_mapping = defaultdict(dict)
        cache = MarketCache.online_markets_detail()
        order_asset_list = ['USDT', 'BTC', 'ETH', 'BCH']
        for market_cache in cache.values():
            for asset in assets:
                if asset != market_cache['base_asset']:
                    continue
                if market_cache['quote_asset'] not in order_asset_list:
                    continue
                asset_index = order_asset_list.index(market_cache['quote_asset'])
                if asset not in coin_market_mapping or \
                        coin_market_mapping[asset]['asset_index'] > asset_index:
                    coin_market_mapping[asset] = dict(market_cache, asset_index=asset_index)
        return coin_market_mapping

    @classmethod
    def get_small_exchange_user_assets_balance(cls, assets: List):
        small_coin_user_id = config['SMALL_COIN_EXCHANGE_USER_ID']
        client = ServerClient()
        balance_result = client.get_user_balances(small_coin_user_id)
        asset_balances = {}
        for asset, balance in balance_result.items():
            if asset in assets:
                asset_balances[asset] = Decimal(balance['available'])
        return asset_balances


class SmallCoinTransferBuyBackProcessor(BaseProcessor):

    def loss_pre_process(self):
        operations = [
            self.cache.enum.PROCESS_CANCEL_ORDER,
            self.cache.enum.PROCESS_EXCHANGE_REPORT,
            self.cache.enum.PROCESS_LOSS_BALANCE_PUT_ORDER,
        ]
        for operation in operations:
            if self.check_operation_finish(operation):
                continue
            else:
                getattr(self, operation.name.lower())()
                self.set_operation_finish(operation)

    def is_loss_put_order_finish(self):
        operations = [
            self.cache.enum.PROCESS_CANCEL_ORDER,
            self.cache.enum.PROCESS_EXCHANGE_REPORT,
            self.cache.enum.PROCESS_LOSS_BALANCE_PUT_ORDER,
        ]
        for operation in operations:
            if not self.check_operation_finish(operation):
                return False
        return True

    def process_cancel_order(self):
        # cancel all order.
        small_coin_user_id = config['SMALL_COIN_EXCHANGE_USER_ID']
        SmallCoinUserExchangeHelper.cancel_market_orders(small_coin_user_id)
        client = ServerClient(current_app.logger)
        retry = False
        # 重新校验
        balance_result = client.get_user_balances(small_coin_user_id)
        for asset, balance in balance_result.items():
            if Decimal(balance['frozen']) > Decimal():
                retry = True
                break
        if retry:
            return self.process_cancel_order()
        return True

    def save_cache(self):
        small_coin_user_id = config['SMALL_COIN_EXCHANGE_USER_ID']
        client = ServerClient(current_app.logger)
        cache = DailySmallCoinExchangeFinishCache(self.report_date)
        balance_result = client.get_user_balances(small_coin_user_id)
        for asset, balance in balance_result.items():
            if Decimal(balance['available']) + Decimal(balance['frozen']) > Decimal():
                cache.hset(asset,
                           amount_to_str(Decimal(balance['available']) +
                                         Decimal(balance['frozen'])))

    def save_exchange_report(self):
        small_coin_user_id = config['SMALL_COIN_EXCHANGE_USER_ID']
        asset_dict = AssetPrice.get_close_price_map(self.report_date)
        start_cache = DailySmallCoinExchangeStartCache(self.report_date)
        end_cache = DailySmallCoinExchangeFinishCache(self.report_date)
        merge_asset = set(start_cache.hkeys() + end_cache.hkeys())
        for asset in merge_asset:
            q = DailySmallCoinExchangeReport.query.filter(
                DailySmallCoinExchangeReport.asset == asset,
                DailySmallCoinExchangeReport.report_date == self.report_date
            ).first()
            if q:
                continue

            amount = Decimal(end_cache.hget(asset) or Decimal()) - \
                     Decimal(start_cache.hget(asset) or Decimal())
            if amount != Decimal():
                record = DailySmallCoinExchangeReport(
                    report_date=self.report_date,
                    user_id=small_coin_user_id,
                    asset=asset,
                    amount=amount,
                    usd=asset_dict.get(asset, Decimal()) * amount
                )
                income_record = DailyIncomeHistory(
                    report_date=self.report_date,
                    asset=asset,
                    amount=amount,
                    income_type=IncomeType.PLATFORM_EXCHANGE
                    if amount > Decimal() else IncomeType.PLATFORM_EXCHANGE_PAY
                )
                db.session.add(record)
                db.session.add(income_record)
        db.session.commit()

    def process_exchange_report(self):
        self.save_cache()
        self.save_exchange_report()

    def process_loss_balance_put_order(self):
        loss_asset_data = LossBalanceHelper.get_loss_asset_data()
        if not loss_asset_data:
            return
        assets = list(loss_asset_data.keys())
        coin_market_mapping = LossBalanceHelper.get_asset_market_mapping(assets)
        asset_balances = LossBalanceHelper.get_small_exchange_user_assets_balance(
            LossBalanceHelper.order_assets
        )
        small_coin_user_id = config['SMALL_COIN_EXCHANGE_USER_ID']
        rest_asset_balances = {asset: balance for asset, balance in asset_balances.items()}
        c = ServerClient()
        for asset, loss_amount in loss_asset_data.items():
            if asset not in coin_market_mapping:
                continue
            market = coin_market_mapping[asset]['name']
            cost_asset = coin_market_mapping[asset]['quote_asset']
            cost_asset_precision = coin_market_mapping[asset]['quote_asset_precision']
            loss_asset = asset
            loss_asset_precision = coin_market_mapping[asset]['base_asset_precision']
            price = quantize_amount(c.market_last(market=market), cost_asset_precision)
            if price <= Decimal('0'):
                continue
            order_price = quantize_amount(price * Decimal('1.01'), cost_asset_precision)
            buy_amount = quantize_amount(Decimal('1.01') * abs(loss_amount), loss_asset_precision)
            need_cost_amount = quantize_amount(order_price * buy_amount, cost_asset_precision)
            rest_amount = rest_asset_balances.get(cost_asset, Decimal())
            asset_conf = try_get_asset_config(asset)
            if asset_conf is None:
                continue
            min_order_amount = asset_conf.min_order_amount
            if rest_amount < Decimal('0'):
                continue
            if need_cost_amount < rest_amount:
                order_amount = buy_amount
                deduct_amount = need_cost_amount
            else:
                order_amount = rest_amount / order_price
                deduct_amount = rest_amount
            if order_amount < min_order_amount:
                continue
            record = DailyLossOrderHistory(
                report_date=self.report_date,
                loss_asset=loss_asset,
                cost_asset=cost_asset,
                market=market,
                cost_amount=deduct_amount,
                loss_amount=loss_amount,
                order_id=0
            )
            db.session_add_and_commit(record)
            order = c.put_limit_order(
                user_id=small_coin_user_id,
                market=market,
                side=OrderSideType.BUY.value,
                amount=amount_to_str(order_amount, loss_asset_precision),
                price=amount_to_str(order_price, cost_asset_precision),
                taker_fee_rate=str(0),
                maker_fee_rate=str(0),
                source='system',
                fee_asset='CET',
                fee_discount=str(0),
                account_id=0,
                option=OrderOption.HIDE
            )
            rest_asset_balances[cost_asset] -= deduct_amount
            record.order_id = order["id"]
            db.session.commit()

    def process_loss_balance_report(self):
        # 五分钟之后进行撤单
        small_coin_user_id = config['SMALL_COIN_EXCHANGE_USER_ID']
        q = DailyLossOrderHistory.query.filter(
            DailyLossOrderHistory.report_date == self.report_date
        ).all()
        cost_data = defaultdict(Decimal)
        loss_data = defaultdict(Decimal)
        c = ServerClient()
        # cancel market order.
        for v in q:
            c.cancel_user_all_order(small_coin_user_id, -1, v.market)
        # 等待五秒，保证能在finish order detail里面查到
        time.sleep(5)
        # record order data.
        for v in q:
            v: DailyLossOrderHistory
            if v.order_id == 0:
                continue
            r = c.finished_order_detail(user_id=small_coin_user_id, order_id=v.order_id)
            if not r:
                continue
            loss_data[v.loss_asset] += Decimal(r['deal_stock'])
            cost_data[v.cost_asset] += Decimal(r['deal_money'])
        # format data with price precision
        for asset in loss_data:
            loss_data[asset] = quantize_amount(loss_data[asset], PrecisionEnum.COIN_PLACES)
        for asset in cost_data:
            cost_data[asset] = quantize_amount(cost_data[asset], PrecisionEnum.COIN_PLACES)
        # 全部未成交
        if sum(loss_data.values()) == Decimal() and sum(cost_data.values()) == Decimal():
            return
        records = []
        price_map = AssetPrice.get_close_price_map(self.report_date)
        for loss_asset, amount in loss_data.items():
            if amount > Decimal('0'):
                asset_usd = price_map.get(loss_asset, 0)
                usd = quantize_amount(asset_usd * amount, PrecisionEnum.CASH_PLACES)
                records.append(DailyLossProcessHistory(
                    process_type=DailyLossProcessHistory.ProcessType.BUY_FROM_MARKET,
                    report_date=self.report_date,
                    asset=loss_asset,
                    amount=amount,
                    usd=usd
                ))

        for cost_asset, amount in cost_data.items():
            if amount > Decimal('0'):
                asset_usd = price_map.get(cost_asset, 0)
                usd = quantize_amount(asset_usd * amount, PrecisionEnum.CASH_PLACES)
                records.append(DailyLossProcessHistory(
                    process_type=DailyLossProcessHistory.ProcessType.SELL_TO_MARKET,
                    report_date=self.report_date,
                    asset=cost_asset,
                    amount=amount,
                    usd=usd
                ))
        db.session.add_all(records)
        db.session.commit()
        income_balance_query = AssetIncomeBalance.query.filter(
            AssetIncomeBalance.asset.in_(list(loss_data.keys()))
        ).all()
        income_balance_map = {e.asset: e for e in income_balance_query}
        for asset, record in income_balance_map.items():
            if loss_data[asset] > Decimal():
                record.balance += loss_data[asset]
                history = DailyAssetIncomeBalanceHistory(
                        report_date=self.report_date,
                        asset=asset,
                        amount=loss_data[asset],
                        balance=record.balance,
                        income_type=IncomeType.LOSS_BALANCE
                    )
                db.session_add_and_commit(history)
                # 销毁亏损购入资产
                c.add_user_balance(user_id=small_coin_user_id,
                                   asset=asset,
                                   amount=str(-history.amount),
                                   business=BalanceBusiness.LOSS_BALANCE.value,
                                   business_id=history.id,
                                   detail={'remark': f'loss balance {asset} {-history.amount}'}
                )

    def process_transfer_report(self):
        small_coin_user_id = config['SMALL_COIN_EXCHANGE_USER_ID']
        buy_back_user_id = config['CET_BUYBACK_USER_ID']
        admin_user_id = config['OFFICIAL_ADMIN_USER_ID']
        asset_price_dict = AssetPrice.get_close_price_map(self.report_date)

        config_percent = BusinessSettings.cet_buy_back_percent
        if config_percent <= Decimal('0') or config_percent > Decimal('1'):
            # 配置有错误，取0.5
            config_percent = Decimal('0.5')

        client = ServerClient(current_app.logger)

        # get all big coin balance.
        balance_data = client.get_user_balances(small_coin_user_id)

        balance_asset_dict = defaultdict(Decimal)
        for asset in BIG_COINS:
            balance_asset_dict[asset] = Decimal(
                balance_data.get(
                    asset, {}
                ).get('available', Decimal())
            )

        income_dict = defaultdict(lambda: {
            "amount": Decimal(),
            "usd": Decimal()
        })

        for asset, amount in balance_asset_dict.items():
            income_dict[asset] = {
                'amount': amount,
                "usd": amount * asset_price_dict[asset]
            }
        total_usd = sum([v['usd'] for v in income_dict.values()])
        if total_usd <= Decimal():
            return
        cet_usd = income_dict['CET']['usd']

        if income_dict['CET']['amount'] > Decimal():
            cet_rate = income_dict['CET']['usd'] / income_dict['CET']['amount']
        else:
            cet_rate = PriceManager.asset_to_usd('CET')

        # （平台昨天收入市值* 配置比例 - CET收入市值）/（平台昨天收入市值-CET收入市值）
        percent = (total_usd * config_percent - cet_usd) / (total_usd - cet_usd)

        client = ServerClient(current_app.logger)
        time.sleep(1)
        # get all big coin balance.
        balance_data = client.get_user_balances(small_coin_user_id)

        balance_asset_dict = defaultdict(Decimal)
        for asset in BIG_COINS:
            balance_asset_dict[asset] = Decimal(
                balance_data.get(
                    asset, {}
                ).get('available', Decimal())
            )
        transfer_buy_back_cet = total_usd * config_percent / cet_rate \
            if percent <= Decimal() else balance_asset_dict['CET']
        transfer_admin_cet = balance_asset_dict['CET'] - transfer_buy_back_cet
        transfer_asset_dict = {
            'CET': {
                'total': balance_asset_dict['CET'],
                'admin': transfer_admin_cet,
                'buy_back': transfer_buy_back_cet
            }
        }
        for asset in (BIG_COINS - {'CET'}):
            asset_amount = balance_asset_dict[asset]
            transfer_asset_dict[asset] = {
                'total': asset_amount,
                'buy_back':
                    percent * asset_amount
                    if percent >= Decimal() else Decimal(),
                'admin': asset_amount - percent * asset_amount
                if percent >= Decimal() else asset_amount
            }
        for asset, transfer_result in transfer_asset_dict.items():
            exchange_user_record = \
                DailyIncomeTransferHistory(
                    report_date=self.report_date,
                    user_id=small_coin_user_id,
                    asset=asset,
                    amount=-transfer_result['total'],
                    transfer_type=DailyIncomeTransferHistory.TransferType
                        .INCOME_TOTAL_TRANSFER,
                    status=DailyIncomeTransferHistory.StatusType.CREATE
                )
            buy_back_user_record = DailyIncomeTransferHistory(
                report_date=self.report_date,
                user_id=buy_back_user_id,
                asset=asset,
                amount=transfer_result['buy_back'],
                transfer_type=DailyIncomeTransferHistory.TransferType
                    .BUYBACK_TRANSFER,
                status=DailyIncomeTransferHistory.StatusType.CREATE
            )
            admin_user_record = DailyIncomeTransferHistory(
                report_date=self.report_date,
                user_id=admin_user_id,
                asset=asset,
                amount=transfer_result['admin'],
                transfer_type=DailyIncomeTransferHistory.TransferType
                    .INCOME_TO_ADMIN_TRANSFER,
                status=DailyIncomeTransferHistory.StatusType.CREATE
            )
            db.session.add(
                exchange_user_record
            )
            db.session.add(
                buy_back_user_record
            )
            db.session.add(
                admin_user_record
            )
            db.session.commit()

    def _process_balance(self, user_id: int,
                         transfer_type: DailyIncomeTransferHistory.TransferType,
                         amount_type_func: Callable):
        client = ServerClient(current_app.logger)
        records = DailyIncomeTransferHistory.query.filter(
            DailyIncomeTransferHistory.report_date == self.report_date,
            DailyIncomeTransferHistory.transfer_type == transfer_type,
            DailyIncomeTransferHistory.user_id == user_id
        )
        for record in records:
            final_amount = amount_type_func(record.amount)
            if record.amount == Decimal():
                record.status = DailyIncomeTransferHistory.StatusType.PASS
                db.session.commit()
                continue
            try:
                client.add_user_balance(
                    user_id=user_id,
                    asset=record.asset,
                    amount=str(final_amount),
                    business=BalanceBusiness[transfer_type.name],
                    business_id=record.id,
                    detail={
                        "remark":
                            f"transfer {user_id} type"
                            f"{transfer_type}"
                    },
                )
                record.status = DailyIncomeTransferHistory.StatusType.PASS
                db.session.commit()
            except ServerClient.BadResponse:
                current_app.logger.error(
                    f"transfer {user_id} type {transfer_type}"
                    f"{record.asset} {abs(final_amount)} "
                )

    def process_exchange_account(self):
        small_coin_user_id = config['SMALL_COIN_EXCHANGE_USER_ID']
        self._process_balance(small_coin_user_id,
                              DailyIncomeTransferHistory.TransferType.INCOME_TOTAL_TRANSFER,
                              lambda x: -abs(x)
                              )

    def process_buy_back_account(self):
        buy_back_user_id = config['CET_BUYBACK_USER_ID']
        self._process_balance(buy_back_user_id,
                              DailyIncomeTransferHistory.TransferType.BUYBACK_TRANSFER,
                              lambda x: abs(x)
                              )

    def process_admin_account(self):
        admin_user_id = config['OFFICIAL_ADMIN_USER_ID']
        self._process_balance(admin_user_id,
                              DailyIncomeTransferHistory.TransferType.INCOME_TO_ADMIN_TRANSFER,
                              lambda x: abs(x)
                              )

    def process_buy_back_cache(self):
        buy_back_user_id = config['CET_BUYBACK_USER_ID']
        client = ServerClient(current_app.logger)
        balance_result = client.get_user_balances(buy_back_user_id)
        cache = DailyBuyBackStartCache(self.report_date)
        for asset, balance in balance_result.items():
            if asset in BIG_COINS:
                cache.hset(asset, amount_to_str(Decimal(balance['available'])))

    def process_cet_exchange_from_account(self):
        if not BusinessSettings.cet_exchange_user_id:
            return
        exchange_user = User.query.filter(
            User.id == BusinessSettings.cet_exchange_user_id).first()
        if not exchange_user:
            return
        if not Decimal('0') < BusinessSettings.cet_exchange_rate <= Decimal('1'):
            return
        client = ServerClient(current_app.logger)
        exchange_rate = BusinessSettings.cet_exchange_rate
        exchange_user_balances = client.get_user_balances(exchange_user.id)
        buy_back_user_id = config['CET_BUYBACK_USER_ID']
        buyback_user_balances = client.get_user_balances(buy_back_user_id)
        exchange_assets = ['USDT', 'BTC', "ETH", "BCH"]
        _asset_rates = {asset: PriceManager.asset_to_usd(asset) for asset in exchange_assets}
        _asset_rates["CET"] = PriceManager.asset_to_usd("CET")
        total_buyback_usd = Decimal()
        for asset, d in buyback_user_balances.items():
            if asset in exchange_assets:
                total_buyback_usd += _asset_rates[asset] * Decimal(d['available'])

        transfer_history = DailyIncomeTransferHistory.query.filter(
            DailyIncomeTransferHistory.asset == 'CET',
            DailyIncomeTransferHistory.report_date == self.report_date,
            DailyIncomeTransferHistory.transfer_type ==
            DailyIncomeTransferHistory.TransferType.BUYBACK_TRANSFER).first()
        transfer_cet = abs(transfer_history.amount) if transfer_history else Decimal()
        total_buyback_usd += _asset_rates["CET"] * Decimal(transfer_cet)

        need_exchange_usd = total_buyback_usd * exchange_rate
        account_cet_amount = Decimal(exchange_user_balances.get('CET', {}).get("available", Decimal()))
        left_exchange_cet_amount = account_cet_amount
        _asset_balances = defaultdict(Decimal)
        for asset in exchange_assets:
            _asset_balances[asset] = Decimal(
                buyback_user_balances.get(asset, {}).get('available', Decimal())
            )
        c = ServerClient()

        class ExchangeInfo(NamedTuple):
            user_id: int
            cost_asset: str
            income_asset: str
            cost_amount: Decimal
            price: Decimal
            cost_usd: Decimal
            income_amount: Decimal

        exchange_infos = []

        cet_min_order_amount = get_asset_config("CET").min_order_amount
        for _asset in exchange_assets:
            market = f"CET{_asset}"
            market_dict = MarketCache(market).dict
            quote_precision = market_dict["quote_asset_precision"]
            base_precision = market_dict["base_asset_precision"]
            if market not in MarketCache.list_online_markets():
                continue
            # 添加一个阈值,防止计算误差导致其他币种兑换
            if need_exchange_usd > Decimal('0.1') and left_exchange_cet_amount >= cet_min_order_amount:
                price = c.market_last(market)
                can_exchange_amount = quantize_amount(
                    min(_asset_balances[_asset] / price,
                        left_exchange_cet_amount,
                        need_exchange_usd / _asset_rates[_asset] / price),
                    base_precision)
                cost_quote_amount = quantize_amount(can_exchange_amount * price, quote_precision)
                cost_usd = quantize_amount(cost_quote_amount * _asset_rates[_asset], PrecisionEnum.CASH_PLACES)
                if can_exchange_amount == Decimal('0') or cost_usd == Decimal('0') \
                        or cost_quote_amount == Decimal('0'):
                    continue
                exchange_infos.append(
                    ExchangeInfo(
                        user_id=buy_back_user_id,
                        cost_asset=_asset,
                        income_asset="CET",
                        cost_amount=cost_quote_amount,
                        price=price,
                        cost_usd=cost_usd,
                        income_amount=can_exchange_amount
                    )
                )
                exchange_infos.append(
                    ExchangeInfo(
                        user_id=exchange_user.id,
                        cost_asset="CET",
                        income_asset=_asset,
                        cost_amount=can_exchange_amount,
                        price=price,
                        cost_usd=cost_usd,
                        income_amount=cost_quote_amount
                    )
                )
                need_exchange_usd = max(need_exchange_usd - cost_usd, Decimal('0'))
                left_exchange_cet_amount = max(left_exchange_cet_amount - can_exchange_amount, Decimal('0'))

        class ExchangeDBInfo(SimpleNamespace):
            user_id: int = 0
            asset: str = ''
            amount: Decimal = Decimal()
            price: Decimal = Decimal()
            usd: Decimal = Decimal()
        record_maps = defaultdict(lambda: ExchangeDBInfo())
        for info in exchange_infos:
            record_maps[(info.user_id, info.cost_asset)].user_id = info.user_id
            record_maps[(info.user_id, info.cost_asset)].asset = info.cost_asset
            record_maps[(info.user_id, info.cost_asset)].price = info.price
            record_maps[(info.user_id, info.cost_asset)].amount -= abs(info.cost_amount)
            record_maps[(info.user_id, info.cost_asset)].usd += info.cost_usd

            record_maps[(info.user_id, info.income_asset)].user_id = info.user_id
            record_maps[(info.user_id, info.income_asset)].asset = info.income_asset
            record_maps[(info.user_id, info.income_asset)].price = info.price
            record_maps[(info.user_id, info.income_asset)].amount += abs(info.income_amount)
            record_maps[(info.user_id, info.income_asset)].usd += info.cost_usd
        records = []
        for p, info in record_maps.items():
            user_id, asset = p
            records.append(
                DailyCetExchangeHistory(
                    report_date=self.report_date,
                    user_id=user_id,
                    asset=asset,
                    amount=info.amount,
                    price=info.price,
                    usd=info.usd,
                    status=DailyCetExchangeHistory.StatusType.CREATED
                ))
        db.session.add_all(records)
        db.session.commit()

    def process_cet_exchange_balances(self):
        records = DailyCetExchangeHistory.query.filter(
            DailyCetExchangeHistory.report_date == self.report_date,
            DailyCetExchangeHistory.status == DailyCetExchangeHistory.StatusType.CREATED,
        ).all()
        deduct_records = [_record for _record in records if _record.amount < Decimal()]
        add_balance_records = [_record for _record in records if _record.amount > Decimal()]
        c = ServerClient(current_app.logger)

        for _record in deduct_records:
            c.add_user_balance(
                user_id=_record.user_id,
                asset=_record.asset,
                amount=_record.amount,
                business=BalanceBusiness.BUYBACK_EXCHANGE,
                business_id=_record.id,
                account_id=SPOT_ACCOUNT_ID)
            _record.status = DailyCetExchangeHistory.StatusType.FINISHED
            db.session.commit()
            if _record.asset == "CET":
                continue
            market = f"CET{_record.asset}"
            market_cache = MarketCache(market).dict
            precision = market_cache["base_asset_precision"]
            _self_deal_price = c.market_last(market)
            _amount = abs(quantize_amount(_record.amount/_self_deal_price, precision))
            try:
                c.market_self_deal(market=market, amount=_amount,
                                   price=_self_deal_price,
                                   side=OrderSideType.BUY)
                current_app.logger.info(
                    f"market self deal {market} price: {_self_deal_price} amount: {_amount}"
                )
            except Exception as e:
                current_app.logger.warning(
                    f"market self deal {market} price: {_self_deal_price} amount: {_amount} err: {e!r}"
                )
        for _record in add_balance_records:
            c.add_user_balance(
                user_id=_record.user_id,
                asset=_record.asset,
                amount=_record.amount,
                business=BalanceBusiness.BUYBACK_EXCHANGE,
                business_id=_record.id,
                account_id=SPOT_ACCOUNT_ID)
            _record.status = DailyCetExchangeHistory.StatusType.FINISHED
            db.session.commit()

@scheduled(crontab(hour='1,3', minute='30-40'))
def run_daily_income_schedule():
    """
    每天01:30至01:40（每分钟重试一次），
    将对应币种昨天的所有收入累积（包括：
    币币手续费收入、合约手续费收入、提现手续费收入、钱包手续费支出、
    杠杆利息收入、理财支出、推荐返佣支出、红包收入、碎币兑换收入、碎币兑换支出）算到“最新盈利余额”
    """
    today = convert_datetime(now(), 'day')
    yesterday = today - timedelta(days=1)
    tool = IncomeHistoryReporter(
        yesterday,
        today
    )
    tool.run()


@scheduled(crontab(hour='1,3', minute='35-39'))
@lock_call()
def asset_balance_transfer_schedule():
    today = convert_datetime(now(), 'day')
    yesterday = today - timedelta(days=1)
    if not DailyIncomeCache(yesterday.date()).is_finish():
        return
    tool = IncomeBalanceProcessor(yesterday.date(),
                                  DailyBalanceIncomeCache
                                  )
    tool.process()


@scheduled(crontab(hour='1,3', minute="40,43,46,49,52,54,56,58"))
@lock_call()
def sell_small_coin_schedule():
    yesterday = convert_datetime(now(), 'day') - timedelta(days=1)
    # 如果小币兑换相关流程完成，重试时直接返回
    if DailyTransferToBuyBackProcessCache(yesterday.date()).is_pre_finish():
        return
    if not DailyIncomeCache(yesterday.date()).is_finish():
        return
    if not DailyBalanceIncomeCache(yesterday.date()).is_finish():
        return
    small_coin_user_id = config['SMALL_COIN_EXCHANGE_USER_ID']
    # 查询账户资金
    client = ServerClient(current_app.logger)
    # 先撤销所有挂单
    coin_market = SmallCoinUserExchangeHelper.get_market_mapping()
    SmallCoinUserExchangeHelper.cancel_market_orders(small_coin_user_id)

    balance_result = client.get_user_balances(small_coin_user_id)

    # 重新下单
    for asset, balance in balance_result.items():
        # 只循环小币种
        if asset in BIG_COINS:
            continue

        if Decimal(balance['available']) == Decimal():
            continue

        if not (asset_conf := try_get_asset_config(asset)):
            continue

        if Decimal(balance['available']) < asset_conf.min_order_amount:
            continue

        sell_markets = coin_market.get(asset)
        if not sell_markets:
            continue

        for sell_market in sell_markets:
            try:
                status = client.get_market_maintain_status(sell_market['name'])['status']
                if status in (MarketStatusType.MARKET_STATUS_PROTECTION, MarketStatusType.MARKET_STATUS_STOP):
                    continue
                last_orders = client.market_deals(market=sell_market['name'],
                                                  limit=1,
                                                  last_id=0)
                if not last_orders or len(last_orders) == 0:
                    continue
            except ServiceUnavailable:
                continue

            last_order = last_orders[0]
            sell_order_price = quantize_amount(
                Decimal(
                    last_order['price']) * SMALL_EXCHANGE_PRICE_PERCENT,
                sell_market['quote_asset_precision'])
            amount = quantize_amount(Decimal(balance['available']),
                                     sell_market['base_asset_precision'])

            try:
                client.put_limit_order(
                    user_id=small_coin_user_id,
                    market=sell_market['name'],
                    side=OrderSideType.SELL.value,
                    amount=str(amount),
                    price=str(sell_order_price),
                    taker_fee_rate=str(0),
                    maker_fee_rate=str(0),
                    source='system',
                    fee_asset=None,
                    fee_discount=str(0),
                    account_id=0,
                    option=OrderOption.HIDE
                )
                break
            except Exception as e:
                current_app.logger.warning(f"sell small coin schedule put limit order in {sell_market['name']} error {e!r}")
                continue


@scheduled(crontab(hour='2,4', minute='3'))
@lock_call()
def loss_balance_put_order_schedule():
    today = convert_datetime(now(), 'day')
    yesterday = today - timedelta(days=1)

    if not DailyIncomeCache(yesterday.date()).is_finish():
        return
    if not DailyBalanceIncomeCache(yesterday.date()).is_finish():
        return
    processor = SmallCoinTransferBuyBackProcessor(
        yesterday,
        DailyTransferToBuyBackProcessCache)
    processor.loss_pre_process()


@scheduled(crontab(hour='2,4', minute="10,12,14"))
@lock_call()
def small_coin_exchange_transfer_schedule():
    today = convert_datetime(now(), 'day')
    yesterday = today - timedelta(days=1)

    if not DailyIncomeCache(yesterday.date()).is_finish():
        return
    if not DailyBalanceIncomeCache(yesterday.date()).is_finish():
        return
    processor = SmallCoinTransferBuyBackProcessor(
        yesterday,
        DailyTransferToBuyBackProcessCache)

    if not processor.is_loss_put_order_finish():
        return
    processor.process()


class BuyBackHelper(object):

    # 下单次数
    RETRY_TIME = 6 * 10
    # 每次下单价格递增
    BUY_BACK_PRICE_PERCENT = Decimal('1.002')
    # 以初次下单盘口价格为基准
    MAX_PRICE_PERCENT = Decimal('1.1')

    # 由于下单的时间间隔不是恒等于RETRY_DELTA_SECONDS,这里留一定的余量
    TOLERANCE_SECONDS = 60 * 1
    RETRY_DELTA_SECONDS = 600

    @classmethod
    def get_buy_back_market(cls):
        return [MarketCache('CETUSDT').dict]

    @classmethod
    def pre_check(cls, check_date: date) -> bool:
        # 缓存在 CACHE_EXPIRED_TIME 过期
        # 如果已经生成了回购报表则直接返回
        if DailyBuyBackFinishCache(check_date).exists():
            return False
        check_caches = [
            DailyIncomeCache,
            DailyBalanceIncomeCache,
            DailyTransferToBuyBackProcessCache
        ]
        for cache in check_caches:
            if not cache(check_date).is_finish():
                return False
        return True

    @classmethod
    def increase_order_count(cls, check_date: date):
        ts = current_timestamp(to_int=True)
        cache = DailyBuyBackCountCache(check_date)
        cache_data = cache.read()
        if len(cache_data) == 0:
            cache.rpush(str(ts))
        else:
            last_ts = int(cache_data[-1])
            min_ts = cls.RETRY_DELTA_SECONDS - cls.TOLERANCE_SECONDS
            if min_ts < ts - last_ts:
                cache.rpush(str(ts))
            else:
                current_app.logger.warning("buy back order count exceeded max tolerances")
                # TODO: 暂时不计入下单次数

    @classmethod
    def check_buyback_finished_by_balances(cls) -> bool:
        # 检查资产数量，如果大于最小下单量表示还未回购完成
        _big_assets = set(BIG_COINS) - {'CET'}
        buy_back_user_id = config['CET_BUYBACK_USER_ID']
        market_list = cls.get_buy_back_market()
        _assets = set()
        for _market_detail in market_list:
            if _market_detail['quote_asset'] in _big_assets:
                _assets.add(_market_detail['quote_asset'])

        client = ServerClient()
        balance_result = client.get_user_balances(buy_back_user_id)
        for _asset in _assets:
            _asset_balance = balance_result.get(_asset, {})
            available = Decimal(_asset_balance.get('available', Decimal()))
            frozen = Decimal(_asset_balance.get('frozen', Decimal()))
            asset_conf = try_get_asset_config(_asset)
            if not asset_conf:
                return False
            if available + frozen > asset_conf.min_order_amount:
                return False
        return True

    @classmethod
    def get_order_count_data(cls, check_date: date) -> list:
        cache = DailyBuyBackCountCache(check_date)
        result = [int(v) for v in cache.read()]
        # 这里实际传入的是昨天的日期，需要+1天
        start_ts = int(date_to_datetime(check_date + timedelta(days=1)).timestamp())
        end_ts = start_ts + 86400
        filter_result = [_ts for _ts in result if start_ts < _ts < end_ts]
        return filter_result

    @classmethod
    def check_buyback_finished_by_order_count(cls, check_date: date, check_ts: int) -> bool:
        filter_result = cls.get_order_count_data(check_date)
        if not filter_result:
            return False

        return (len(filter_result) >= cls.RETRY_TIME and
                check_ts - max(filter_result) > cls.RETRY_DELTA_SECONDS - cls.TOLERANCE_SECONDS)

    @classmethod
    def check_buyback_finished(cls, check_date: date, check_ts: int) -> bool:
        if cls.check_buyback_finished_by_balances():
            return True
        if cls.check_buyback_finished_by_order_count(check_date, check_ts):
            return True
        return False

    @classmethod
    def get_last_order_price(cls, price_date: date, market: str) -> Decimal | None:
        c = DailyBuyBackOrderPriceCache(price_date, market)
        if value := c.read():
            return Decimal(value)

        client = ServerClient()
        last_orders = client.market_deals(market=market,
                                          limit=1,
                                          last_id=0)
        if not last_orders or len(last_orders) == 0:
            current_app.logger.warning(f"{market} not found last price")
            raise ValueError
        last_order = last_orders[0]
        market_detail = MarketCache(market).dict
        price = quantize_amount(
            Decimal(last_order['price']),
            market_detail['quote_asset_precision'])
        return price

    @classmethod
    def set_current_order_price(cls, price_date: date, market: str, price: Decimal):
        c = DailyBuyBackOrderPriceCache(price_date, market)
        c.set(amount_to_str(price), ex=CACHE_EXPIRED_TIME)

    @classmethod
    def get_max_price(cls, price_date: date, market: str) -> Decimal | None:
        """如果不存在则设置入缓存"""
        c = DailyBuyBackPriceCache(price_date, market)
        if value := c.read():
            return Decimal(value)

        client = ServerClient()
        last_orders = client.market_deals(market=market,
                                          limit=1,
                                          last_id=0)
        if not last_orders or len(last_orders) == 0:
            current_app.logger.warning(f"{market} not found last price")
            return None
        last_order = last_orders[0]
        market_detail = MarketCache(market).dict
        price = quantize_amount(
            Decimal(last_order['price']) * cls.MAX_PRICE_PERCENT,
            market_detail['quote_asset_precision'])
        c.set(amount_to_str(price), ex=CACHE_EXPIRED_TIME)
        return price
    
    @classmethod
    def get_other_assets_sold(cls, date_: date) -> bool:
        if DailyBuyBackAssetSoldCache(date_).read():
            return True
        return False
    
    @classmethod
    def set_other_assets_sold(cls, date_: date):
        DailyBuyBackAssetSoldCache(date_).set('1', ex=CACHE_EXPIRED_TIME)

    @classmethod
    def get_inital_amount(cls,  price_date: date, asset: str) -> Decimal | None:
        v = DailyBuyBackInitalAmountCache(price_date, asset).read()
        if v:
            return Decimal(v)
        return None

    @classmethod
    def set_inital_amount(cls,  price_date: date, asset: str, amount: Decimal):
        DailyBuyBackInitalAmountCache(price_date, asset).set(amount_to_str(amount), ex=CACHE_EXPIRED_TIME)


@scheduled(crontab(hour='3-20', minute="6, 16, 26, 36, 46, 56"))
@lock_call()
def buy_back_round_schedule():
    """从3点开始，分10次执行，每小时买回1/10。如果某次执行失败，则数量累计到下个小时一起执行"""
    total_round = 10
    start_hour = 3 # 注意跟任务第一次执行的小时数一致
    now_ = now()
    yesterday = (convert_datetime(now_, 'day') - timedelta(days=1)).date()
    hour = now_.hour
    round_ = hour - start_hour
    if round_ < 0:
        return
    round_ = min(round_, total_round - 1)
    # 检查前置流程是否已经完成，或者说回购流程已经走完，直接返回
    if not BuyBackHelper.pre_check(yesterday):
        return
    # 满足终止条件，cet账号资产或者下单次数
    if BuyBackHelper.check_buyback_finished(yesterday, current_timestamp(to_int=True)):
        buy_back_generate_report_schedule()
        return
    # 如果其他币种尚未卖出，先执行卖出。
    if not BuyBackHelper.get_other_assets_sold(yesterday):
        sell_other_assets_schedule()
        return
    # 开始下单, 更新下单缓存
    BuyBackHelper.increase_order_count(yesterday)

    buy_back_user_id = config['CET_BUYBACK_USER_ID']
    client = ServerClient(current_app.logger)
    client.cancel_user_all_order(buy_back_user_id, -1, None)
    market_list = BuyBackHelper.get_buy_back_market()
    for buy_market in market_list:
        asset = buy_market['quote_asset']
        amount = BuyBackHelper.get_inital_amount(yesterday, asset)
        # 当前第一次执行时，设置总的quote asset数量
        if amount is None:
            bs = client.get_user_balances(buy_back_user_id, asset)
            amount = bs[asset]['available']
            BuyBackHelper.set_inital_amount(yesterday, asset, amount)

        keep_amount = amount * (total_round - 1 - round_) / total_round
        put_buy_back_order(yesterday, buy_market, keep_amount, round_ == total_round - 1)


@scheduled(crontab(hour='2', minute="6, 16, 26, 36, 46, 56"))
@lock_call()
def sell_other_assets_schedule():
    yesterday = (convert_datetime(now(), 'day') - timedelta(days=1)).date()
    # 检查前置流程是否已经完成，或者说回购流程已经走完，直接返回
    if not BuyBackHelper.pre_check(yesterday):
        return
    # 满足终止条件，cet账号资产或者下单次数
    if BuyBackHelper.check_buyback_finished(yesterday, current_timestamp(to_int=True)):
        buy_back_generate_report_schedule()
        return
    buy_back_user_id = config['CET_BUYBACK_USER_ID']
    client = ServerClient(current_app.logger)
    client.cancel_user_all_order(buy_back_user_id, -1, None)
    balance_result = client.get_user_balances(buy_back_user_id)
    # 先把其他币卖掉换成USDT
    sell_assets_data = [
        dict(
            source="BTC",
            target="USDT",
            percent=Decimal("0.99"),
        ),
        dict(
            source="ETH",
            target="USDT",
            percent=Decimal("0.99"),
        ),
        dict(
            source="USDC",
            target="USDT",
            percent=Decimal("0.999"),
        )
    ]
    for _data in sell_assets_data:
        _asset = _data["source"]
        _target = _data["target"]
        price_percent = _data["percent"]
        _market = f"{_asset}{_target}"
        if _market not in MarketCache.list_online_markets():
            continue
        market_detail = MarketCache(_market).dict
        available = Decimal(balance_result[_asset]['available'])
        amount = quantize_amount(available,
                                 market_detail['base_asset_precision'])
        if Decimal(amount) == Decimal():
            continue
        last_orders = client.market_deals(market=_market,
                                          limit=1,
                                          last_id=0)
        if not last_orders or len(last_orders) == 0:
            continue
        last_order = last_orders[0]
        sell_order_price = quantize_amount(
            Decimal(
                last_order['price']) * price_percent,
            market_detail['quote_asset_precision'])
        client.put_limit_order(
            user_id=buy_back_user_id,
            market=_market,
            side=OrderSideType.SELL.value,
            amount=str(amount),
            price=str(sell_order_price),
            taker_fee_rate=str(0),
            maker_fee_rate=str(0),
            fee_asset='CET',
            source='system',
            fee_discount=str(0),
            account_id=0,
            option=OrderOption.HIDE | OrderOption.WITHOUT_ORDER_MIN_AMOUNT
        )
    # 只要挂单成功就认为已经卖掉
    BuyBackHelper.set_other_assets_sold(yesterday)


def put_buy_back_order(date_: date, buy_market: dict, keep_amount: Decimal, final: bool):
    buy_back_user_id = config['CET_BUYBACK_USER_ID']
    _market_name = buy_market['name']
    try:
        _last_price = BuyBackHelper.get_last_order_price(date_, _market_name)
    except ValueError:
        return
    max_price = BuyBackHelper.get_max_price(date_, _market_name)
    cal_order_price = quantize_amount(
        Decimal(_last_price * BuyBackHelper.BUY_BACK_PRICE_PERCENT),
        buy_market['quote_asset_precision'])
    buy_order_price = min(max_price, cal_order_price) if max_price else cal_order_price
    BuyBackHelper.set_current_order_price(date_, _market_name, buy_order_price)

    client = ServerClient(current_app.logger)
    asset = buy_market['quote_asset']
    balance = client.get_user_balances(buy_back_user_id, asset)[asset]
    quote_amount = balance['available'] - keep_amount
    amount = quantize_amount(quote_amount / buy_order_price,
                                buy_market['base_asset_precision'])
    # 数量太小则不再购买，避免推高后面的下单价格
    min_order_amount = 0 if final else Decimal(10)
    if Decimal(amount) < min_order_amount:
        return
    client.put_limit_order(
        user_id=buy_back_user_id,
        market=buy_market['name'],
        side=OrderSideType.BUY.value,
        amount=str(amount),
        price=str(buy_order_price),
        taker_fee_rate=str(0),
        maker_fee_rate=str(0),
        fee_asset='CET',
        source='system',
        fee_discount=str(0),
        account_id=0,
        option=OrderOption.HIDE | OrderOption.WITHOUT_ORDER_MIN_AMOUNT
    )


@lock_call()
def buy_back_generate_report_schedule():
    yesterday = convert_datetime(now(), 'day') - timedelta(days=1)
    if not BuyBackHelper.pre_check(yesterday.date()):
        return
    buy_back_user_id = config['CET_BUYBACK_USER_ID']
    client = ServerClient(current_app.logger)
    client.cancel_user_all_order(buy_back_user_id, -1, None)
    balance_result = client.get_user_balances(buy_back_user_id)
    end_cache = DailyBuyBackFinishCache(yesterday.date())

    # calculate spend assets from order deals
    spent_data = defaultdict(Decimal)
    buy_cet_amount = Decimal()
    start_ts = today_timestamp_utc()
    end_ts = current_timestamp(to_int=True)

    def fetch_user_spot_deals(
            _user_id: int, _start_ts: int, _end_ts: int
    ) -> list:
        has_next_page = True
        deals_data = []
        _page = 1
        while has_next_page:
            _deals = client.market_user_deals(
                user_id=_user_id,
                market='',
                side=0,
                start_time=_start_ts,
                end_time=_end_ts,
                page=_page,
                limit=50
            )
            deals_data.extend(_deals)
            has_next_page = _deals.has_next
            _page += 1
        return deals_data

    market_details = MarketCache.online_markets_detail()
    user_deals_data = fetch_user_spot_deals(
        buy_back_user_id, start_ts, end_ts)
    for deal in user_deals_data:
        base_asset = market_details[deal['market']]["base_asset"]
        quote_asset = market_details[deal['market']]["quote_asset"]
        if base_asset == "CET" and quote_asset == "USDT" and deal["side"] == OrderSideType.BUY:
            spent_data[quote_asset] += Decimal(deal["deal"])
            buy_cet_amount += Decimal(deal["amount"])
        if base_asset != "CET" and quote_asset == "USDT" and deal["side"] == OrderSideType.SELL:
            spent_data[base_asset] += Decimal(deal["amount"])
            spent_data[quote_asset] -= Decimal(deal["deal"])

    ex_q = DailyCetExchangeHistory.query.filter(
        DailyCetExchangeHistory.report_date == yesterday.date(),
        DailyCetExchangeHistory.user_id == buy_back_user_id,
    ).with_entities(func.sum(DailyCetExchangeHistory.amount).label("total_amount"),
                    DailyCetExchangeHistory.asset,
                    ).group_by(
        DailyCetExchangeHistory.asset
    ).all()

    for _ex_data in ex_q:
        if _ex_data.asset == "CET":
            buy_cet_amount += _ex_data.total_amount
        else:
            spent_data[_ex_data.asset] += abs(_ex_data.total_amount)

    for coin, balance in balance_result.items():
        if coin in BIG_COINS:
            end_cache.hset(coin, amount_to_str(Decimal(balance['available'])))

    for asset in BIG_COINS:
        q = DailyCetBuyBackReport.query.filter(
            DailyCetBuyBackReport.asset == asset,
            DailyCetBuyBackReport.report_date == yesterday.date()
        ).first()
        report_type = DailyCetBuyBackReport.ReportType.BUY_BACK \
            if asset == 'CET' else DailyCetBuyBackReport.ReportType.SPEND
        if not q:
            # 花费数据库存为负值
            amount = -spent_data[asset] if asset != 'CET' else buy_cet_amount
            record = DailyCetBuyBackReport(
                report_date=yesterday.date(),
                user_id=buy_back_user_id,
                asset=asset,
                amount=amount,
                usd=quantize_amount(
                    amount * PriceManager.asset_to_usd(asset),
                    PrecisionEnum.COIN_PLACES),
                report_type=report_type
            )
            db.session.add(record)
    transfer_record = DailyIncomeTransferHistory.query.filter(
        DailyIncomeTransferHistory.report_date == yesterday.date(),
        DailyIncomeTransferHistory.transfer_type ==
        DailyIncomeTransferHistory.TransferType.BUYBACK_TRANSFER,
        DailyIncomeTransferHistory.asset == 'CET'
    ).first()

    # add transfer record.
    transfer_query = DailyCetBuyBackReport.query.filter(
        DailyCetBuyBackReport.report_date == yesterday.date(),
        DailyCetBuyBackReport.user_id == buy_back_user_id,
        DailyCetBuyBackReport.asset == 'CET',
        DailyCetBuyBackReport.report_type ==
        DailyCetBuyBackReport.ReportType.TRANSFER
    ).first()
    if transfer_record and not transfer_query:
        record = DailyCetBuyBackReport(
            report_date=yesterday.date(),
            user_id=buy_back_user_id,
            asset=transfer_record.asset,
            amount=abs(transfer_record.amount),
            usd=quantize_amount(
                abs(transfer_record.amount) * PriceManager.asset_to_usd(
                    transfer_record.asset),
                PrecisionEnum.COIN_PLACES),
            report_type=DailyCetBuyBackReport.ReportType.TRANSFER
        )
        db.session.add(record)
    db.session.commit()
    all_caches = [
        DailyIncomeCache, DailyBalanceIncomeCache,
        DailySmallCoinExchangeStartCache, DailySmallCoinExchangeFinishCache,
        DailyTransferToBuyBackProcessCache, DailyBuyBackStartCache,
        DailyBuyBackFinishCache,
        DailyBuyBackCountCache
    ]
    for cache_cls in all_caches:
        cache_cls(yesterday.date()).expire(CACHE_EXPIRED_TIME)


@scheduled(crontab(hour='13-16', minute="20"))
@lock_call()
def check_buy_back_report_generate_schedule():
    yesterday = convert_datetime(now(), 'day') - timedelta(days=1)
    if not DailyCetBuyBackReport.query.filter(
        DailyCetBuyBackReport.report_date == yesterday
    ).first():
        text = "今日cet回购报表生成异常，请排查是否有问题"
        send_alert_notice(text,
                          config["ADMIN_CONTACTS"]["web_notice"],
                          at=config["ADMIN_CONTACTS"]["slack_at"].get("web_notice"))



@scheduled(crontab(hour='2,4', minute="20-30"))
@lock_call()
def income_report_schedule():
    yesterday = convert_datetime(now(), 'day') - timedelta(days=1)
    check_caches = [
        DailyIncomeCache,
        DailyBalanceIncomeCache,
        DailyTransferToBuyBackProcessCache
    ]
    for cache in check_caches:
        if not cache(yesterday.date()).is_finish():
            return
    # 全站收入报表
    DailyIncomeReporter().dispatch()
    MonthlyIncomeReporter().dispatch(include_curr_month=True)
    # 实收报表
    DailyRealIncomeReporter().dispatch()
    MonthlyRealIncomeReporter().dispatch(include_curr_month=True)
    # Admin账号净收入报表
    DailyAdminNetIncomeReporter().dispatch()
    MonthlyAdminNetIncomeReporter().dispatch(include_curr_month=True)


@scheduled(crontab(minute=40, hour="8-10", day_of_month=1))
@lock_call()
def update_monthly_operating_expenses_report_schedule():
    MonthlyOperatingExpensesReporter().dispatch()


def init_destroy_infos():
    """
    销毁数据初始化
    :return:
    """
    init_history = [
        (
            date(2020, 4, 1),
            '2020年第一季度',
            Decimal('3210455698'),
            Decimal('5842177609.53'),
            Decimal('210455698'),
            Decimal('4157822391.02'),
            Decimal('19182420'),
            Decimal('167462.53')),
        (
            date(2020, 1, 2),
            '2019年第四季度',
            Decimal('0'),
            Decimal('5861360029.53'),
            Decimal('0'),
            Decimal('4138639971.02'),
            Decimal('7489008'),
            Decimal('100412.62')),
        (date(2019, 10, 8),
         '2019年第三季度',
         Decimal('0'),
         Decimal('5868849037.53'),
         Decimal('0'),
         Decimal('4131150963.02'),
         Decimal('8826233.6'),
         Decimal('158386.76')),
        (date(2019, 7, 1), '2019年第二季度', Decimal('0'), Decimal('5877675270.53'),
         Decimal('0'), Decimal('4122324729.38257'), Decimal('10210199.44'),
         Decimal('224614.18')),
        (date(2019, 4, 1), '2019年第一季度', Decimal('0'), Decimal('5887885469.97'),
         Decimal('0'), Decimal('4112114529.89577'), Decimal('17242536.9'),
         Decimal('280794.71')),
        (date(2019, 1, 31), '团队持有销毁', Decimal('0'), Decimal('5905128007'),
         Decimal('0'), Decimal('4094871993'), Decimal('4000000000'),
         Decimal('24404000')),
        (date(2018, 10, 1), '2018年9月', Decimal('0'), Decimal('9905128008'),
         Decimal('0'), Decimal('94871992'), Decimal('11651200'),
         Decimal('422099.67')),
        (date(2018, 9, 1), '2018年8月', Decimal('0'), Decimal('9916779208'),
         Decimal('0'), Decimal('83220792'), Decimal('15171262'),
         Decimal('550716.81')),
        (date(2018, 8, 1), '2018年7月', Decimal('0'), Decimal('9931950470'),
         Decimal('0'), Decimal('68047160'), Decimal('30195981'),
         Decimal('2356796.32')),
        (date(2018, 7, 1), '2018年6月', Decimal('0'), Decimal('9962146451'),
         Decimal('0'), Decimal('37851179'), Decimal('37851179'),
         Decimal('1975831.54')),
    ]

    for history in reversed(init_history):
        new_destroy = CetDestroyReport()
        new_destroy.report_date = history[0]
        new_destroy.content = history[1]
        new_destroy.liquidity = history[2]
        new_destroy.total_amount = history[3]
        new_destroy.ready_destroy_amount = history[4]
        new_destroy.already_destroy_amount = history[5]
        new_destroy.current_destroy_amount = history[6]
        new_destroy.destroy_value = history[7]
        db.session.add(new_destroy)
    db.session.commit()


def cet_destroy_report_business(report_date: date):
    start_date = report_date + relativedelta(months=-1)
    last_month_last_day = report_date - relativedelta(days=1)
    is_finished = DailyCetBuyBackReport.query.filter(
        DailyCetBuyBackReport.report_date == last_month_last_day,
    ).first()
    if not is_finished:
        current_app.logger.warning(f"{last_month_last_day} cet buy back report not finished")
        return

    daily_record = DailyCetBuyBackReport.query.filter(
        DailyCetBuyBackReport.report_date >= start_date,
        DailyCetBuyBackReport.report_date < report_date,
        DailyCetBuyBackReport.asset == 'CET',
        DailyCetBuyBackReport.report_type.in_(
            [
                DailyCetBuyBackReport.ReportType.BUY_BACK,
                DailyCetBuyBackReport.ReportType.TRANSFER
            ]
        )
        ).with_entities(
        func.sum(DailyCetBuyBackReport.amount).label('amount'),
        func.sum(DailyCetBuyBackReport.usd).label('value')
    ).first()

    daily_amount = daily_record.amount or Decimal() \
        if daily_record else Decimal('0')
    daily_value = daily_record.value or Decimal() \
        if daily_record else Decimal('0')

    last_record = CetDestroyReport.query.order_by(
        CetDestroyReport.report_date.desc()
    ).first()

    if last_record:
        before_destroy = last_record.already_destroy_amount
    else:
        before_destroy = Decimal('0')

    cache = CetCirculationCache()
    cache.reload()
    value = cache.read_aside()
    content = f'{start_date.year}年第{start_date.month}月'
    liquidity = Decimal(value.get('remain', Decimal('0')))
    total_amount = Decimal(value.get('total_supply', Decimal('0')))
    ready_destroy_amount = liquidity - Decimal('3000000000')
    current_destroy_amount = daily_amount
    destroy_value = daily_value
    already_destroy_amount = current_destroy_amount + before_destroy

    new_destroy = CetDestroyReport()
    new_destroy.report_date = report_date
    new_destroy.content = content
    new_destroy.liquidity = Decimal(liquidity)
    new_destroy.total_amount = Decimal(total_amount)
    new_destroy.ready_destroy_amount = Decimal(
        ready_destroy_amount)
    new_destroy.already_destroy_amount = Decimal(
        already_destroy_amount)
    new_destroy.current_destroy_amount = Decimal(
        current_destroy_amount)
    new_destroy.destroy_value = Decimal(destroy_value)
    db.session.add(new_destroy)
    db.session.commit()


@scheduled(crontab(hour='13,15', minute="20-30", day_of_month='1-3'))
@lock_call()
def cet_destroy_report_schedule():
    today = date.today()
    this_month_first_day = date(year=today.year, month=today.month, day=1)
    last_record = CetDestroyReport.query.order_by(
        CetDestroyReport.report_date.desc()).first()

    if last_record:
        if last_record.report_date == this_month_first_day:
            return
    else:
        init_destroy_infos()
        return
    cet_destroy_report_business(this_month_first_day)
