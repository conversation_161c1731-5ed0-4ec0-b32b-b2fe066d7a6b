# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from typing import Optional

from pyroaring import BitMap

from app.models.investment import UserDayInterestHistory
from app.models.monthly import MonthlyInvestmentReport, MonthlySiteInvestmentReport

from .base import BaseReporter
from app.business.investment import DailyInvestmentReporter
from app.models import (
    DailyInvestmentReport, DailySiteInvestmentReport,
    db,
)
from app.utils.date_ import date_to_datetime
from ...utils import quantize_amount


class InvestmentReporter(BaseReporter):
    model = DailyInvestmentReport

    def get_first_date(self) -> Optional[date]:
        model = UserDayInterestHistory
        records = (
            model.query.filter(
                model.status == model.Status.SUCCESS,
            )
            .order_by(model.report_date.asc())
            .first()
        )
        return records.report_date if records else None

    def run(self, start_date: date, end_date: date):
        DailyInvestmentReporter.daily_user_investment_interest_report(
                date_to_datetime(start_date),
                date_to_datetime(end_date)
                )


class SiteInvestmentReporter(BaseReporter):
    model = DailySiteInvestmentReport

    def get_first_date(self) -> Optional[date]:
        record = DailyInvestmentReport.query.order_by(
            DailyInvestmentReport.report_date.asc()
        ).first()
        return record.report_date if record else None

    def run(self, start_date: date, end_date: date):
        day_model = DailyInvestmentReport
        q = day_model.query.filter(day_model.report_date == start_date).all()
        if not q:
            return

        yes_rows = (
            day_model.query.filter(day_model.report_date == start_date - timedelta(days=1))
            .with_entities(
                day_model.asset,
                day_model.site_history_user_bitmap,
                day_model.site_history_interest_user_bitmap,
            )
            .all()
        )
        yas_asset_map = {row.asset: row for row in yes_rows}

        total_usd = Decimal()
        total_invest_usd = Decimal()

        site_cur_user_bitmap = BitMap()
        site_cur_interest_user_bitmap = BitMap()

        his_user_bitmap = BitMap()
        his_interest_user_bitmap = BitMap()
        yes_his_user_bitmap = BitMap()
        yes_his_interest_user_bitmap = BitMap()

        interest_map = defaultdict(Decimal)
        interest_fields = DailyInvestmentReport.interest_fields()

        for row in q:
            total_usd += row.usd
            total_invest_usd += row.investment_interest_usd
            site_cur_user_bitmap.update(BitMap.deserialize(row.site_cur_user_bitmap) if row.site_cur_user_bitmap else BitMap([]))
            site_cur_interest_user_bitmap.update(
                BitMap.deserialize(row.site_cur_interest_user_bitmap) if row.site_cur_interest_user_bitmap else BitMap([])
            )

            his_user_bitmap.update(BitMap.deserialize(row.site_history_user_bitmap) if row.site_history_user_bitmap else BitMap([]))
            his_interest_user_bitmap.update(
                BitMap.deserialize(row.site_history_interest_user_bitmap) if row.site_history_interest_user_bitmap else BitMap([])
            )

            yes_row = yas_asset_map.get(row.asset)
            yes_his_user_bitmap.update(BitMap.deserialize(yes_row.site_history_user_bitmap) if yes_row else BitMap([]))
            yes_his_interest_user_bitmap.update(BitMap.deserialize(yes_row.site_history_interest_user_bitmap) if yes_row else BitMap([]))

            for field in interest_fields:
                interest_map[field] += getattr(row, field) or 0

        increase_user_bitmap = his_user_bitmap - yes_his_user_bitmap
        increase_interest_user_bitmap = his_interest_user_bitmap - yes_his_interest_user_bitmap

        record = DailySiteInvestmentReport.get_or_create(report_date=start_date)
        record.usd = total_usd
        record.investment_interest_usd = total_invest_usd
        record.increase_investment_user = len(increase_user_bitmap)
        record.increase_interest_user = len(increase_interest_user_bitmap)
        record.investment_user_count = len(site_cur_user_bitmap)
        record.interest_user_count = len(site_cur_interest_user_bitmap)

        # 更新动态字段
        for key, value in interest_map.items():
            setattr(record, key, value)
        db.session_add_and_commit(record)


def update_monthly_asset_investment_report(start_month, end_month):
    query = DailyInvestmentReport.query.filter(
        DailyInvestmentReport.report_date >= start_month, DailyInvestmentReport.report_date < end_month
    ).all()

    site_cur_user_bitmap = defaultdict(BitMap)
    site_cur_interest_user_bitmap = defaultdict(BitMap)
    asset_data_map = defaultdict(lambda: defaultdict(Decimal))
    fields = [
        "investment_interest_usd",
        "amount",
        "usd",
        "investment_interest_amount",
        "increase_investment_user",
        "increase_interest_user",
    ] + DailyInvestmentReport.interest_fields()
    for item in query:
        data = asset_data_map[item.asset]
        site_cur_user_bitmap[item.asset].update(BitMap.deserialize(item.site_cur_user_bitmap) if item.site_cur_user_bitmap else BitMap([]))
        site_cur_interest_user_bitmap[item.asset].update(
            BitMap.deserialize(item.site_cur_interest_user_bitmap) if item.site_cur_interest_user_bitmap else BitMap([])
        )

        for field in fields:
            data[field] += getattr(item, field) or 0

    days = (end_month - start_month).days
    for asset, data in asset_data_map.items():
        record = MonthlyInvestmentReport.get_or_create(report_date=start_month, asset=asset)
        record.amount = quantize_amount(data.pop("amount", 0) / days, 8)
        record.usd = quantize_amount(data.pop("usd", 0) / days, 8)
        record.user_count = (len(site_cur_user_bitmap[asset]),)
        record.interest_user_count = len(site_cur_interest_user_bitmap[asset])
        for k, v in data.items():
            setattr(record, k, v)
        db.session_add_and_commit(record)


def update_monthly_site_investment_report(start_month, end_month):
    asset_rows = DailyInvestmentReport.query.filter(
        DailyInvestmentReport.report_date >= start_month, DailyInvestmentReport.report_date < end_month
    )
    days = (end_month - start_month).days

    site_cur_user_bitmap = BitMap()
    site_cur_interest_user_bitmap = BitMap()

    for item in asset_rows:
        site_cur_user_bitmap.update(BitMap.deserialize(item.site_cur_user_bitmap) if item.site_cur_user_bitmap else BitMap([]))
        site_cur_interest_user_bitmap.update(
            BitMap.deserialize(item.site_cur_interest_user_bitmap) if item.site_cur_interest_user_bitmap else BitMap([])
        )

    site_model = DailySiteInvestmentReport
    site_rows = site_model.query.filter(site_model.report_date >= start_month, site_model.report_date < end_month)

    data_map = defaultdict(Decimal)
    fields = [
        "increase_investment_user",
        "increase_interest_user",
        "investment_interest_usd",
        "usd",
    ] + DailyInvestmentReport.interest_fields()
    for item in site_rows:
        for field in fields:
            data_map[field] += getattr(item, field) or 0

    record = MonthlySiteInvestmentReport.get_or_create(report_date=start_month)
    record.usd = quantize_amount(data_map.pop("usd", 0) / days, 8)
    record.investment_user_count = len(site_cur_user_bitmap)
    record.interest_user_count = len(site_cur_interest_user_bitmap)
    for k, v in data_map.items():
        setattr(record, k, v)
    db.session_add_and_commit(record)

