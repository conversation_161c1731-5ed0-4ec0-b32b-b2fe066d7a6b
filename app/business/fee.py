import json
from collections import defaultdict
from decimal import Decimal
from enum import Enum

from celery.schedules import crontab
from sqlalchemy import or_, and_

from app.business.func_cache import mem_cached
from app.business.fee_constant import (
    NORMAL_LEVEL, DEFAULT_SPOT_TAKER_FEE, DEFAULT_SPOT_MAKER_FEE, DEFAULT_PERPETUAL_TAKER_FEE,
    DEFAULT_PERPETUAL_MAKER_FEE, VIP_LEVEL_DICT, MARKET_MAKER_DICT, PERPETUAL_MARKET_MAKER_DICT,
    ALL_MARKETS, MAKER_CASHBACK_LEVEL_RATE_MAP,
)
from app.business.lock import lock_call
from app.business.site import SiteSettings
from app.caches import AmmMarketCache, MarketCache
from app.caches.order import OrderTradeFeeDiscountCache
from app.caches.user import UserClassIdentityCache, UserSpecialFeeCache
from app.common import TradeType, TradeBusinessType, CeleryQueues
from app.exceptions import InvalidArgument
from app.models import (
    <PERSON><PERSON><PERSON><PERSON>, MarketMaker, User, InnerMarketMaker, User<PERSON>onfig<PERSON>rade<PERSON><PERSON>,
    UserTradeFeeDiscount, SubAccount,
)
from app.utils import now, amount_to_str, scheduled, celery_task, route_module_to_celery_queue
from app.utils.date_ import current_timestamp


route_module_to_celery_queue(__name__, CeleryQueues.MARKET_MAKER)


class UserClassIdentitySpotEnum(Enum):

    VIP = 'VIP'
    MM = 'MM'  # market maker

    def get_fee(self, level):
        result = {
                TradeType.TAKER: DEFAULT_SPOT_TAKER_FEE,
                TradeType.MAKER: DEFAULT_SPOT_MAKER_FEE
        }
        if self == self.VIP and level > NORMAL_LEVEL:
            result[TradeType.MAKER] = VIP_LEVEL_DICT[level]["maker_fee_rate"]
            result[TradeType.TAKER] = VIP_LEVEL_DICT[level]["taker_fee_rate"]
        if self == self.MM and level > NORMAL_LEVEL:
            result[TradeType.MAKER] = MARKET_MAKER_DICT[level]["maker_fee_rate"]
            result[TradeType.TAKER] = MARKET_MAKER_DICT[level]["taker_fee_rate"]
        return result

    def get_settle_fee(self, level):
        # 结算费率，兼容现货负费率的显示
        result = self.get_fee(level)
        if self == self.MM and level in MAKER_CASHBACK_LEVEL_RATE_MAP:
            result[TradeType.MAKER] = -1 * MAKER_CASHBACK_LEVEL_RATE_MAP[level]
        return result


class UserClassIdPerpetualEnum(Enum):

    VIP = 'VIP'
    MM = 'MM'

    def get_fee(self, level):
        result = {
                TradeType.TAKER: DEFAULT_PERPETUAL_TAKER_FEE,
                TradeType.MAKER: DEFAULT_PERPETUAL_MAKER_FEE
        }
        if self == self.VIP and level > NORMAL_LEVEL:
            result[TradeType.MAKER] = VIP_LEVEL_DICT[level]["perpetual_maker_fee_rate"]
            result[TradeType.TAKER] = VIP_LEVEL_DICT[level]["perpetual_taker_fee_rate"]
        if self == self.MM and level > NORMAL_LEVEL:
            result[TradeType.MAKER] = PERPETUAL_MARKET_MAKER_DICT[level]["maker_fee_rate"]
            result[TradeType.TAKER] = PERPETUAL_MARKET_MAKER_DICT[level]["taker_fee_rate"]
        return result

    def get_settle_fee(self, level):
        return self.get_fee(level)


class UserFeeParser(object):
    """
    settle_fee 表示结算费率，由于现货server不支持负费率，实现形式为第二天返USDT
    trade_fee 表示交易费率，即用户下单时候订单收取的实际费率
    合约的settle_fee与trade_fee是相同的
    """

    @classmethod
    def get_main_user_id(cls, user_id: int):
        sub_user = SubAccount.query.filter(
                SubAccount.status == SubAccount.Status.VALID,
                SubAccount.user_id == user_id).first()
        if sub_user:
            return sub_user.main_user_id
        return user_id

    def __init__(self, user_id: int):
        self.main_user_id = self.get_main_user_id(user_id)
        self.main_user_type = User.query.get(self.main_user_id).main_user_type

    @classmethod
    def get_enum(cls, business_type: TradeBusinessType):
        m = {
            TradeBusinessType.SPOT: UserClassIdentitySpotEnum,
            TradeBusinessType.PERPETUAL: UserClassIdPerpetualEnum
        }
        return m[business_type]

    def get_user_level_data(self):
        vip_level = self.get_vip_level()
        spot_result = {
            UserClassIdentitySpotEnum.VIP: vip_level,
            UserClassIdentitySpotEnum.MM: self.get_spot_mm_level(),
        }
        spot_is_normal = max(spot_result.values()) == NORMAL_LEVEL
        perpetual_result = {
            UserClassIdPerpetualEnum.VIP: vip_level,
            UserClassIdPerpetualEnum.MM: self.get_perpetual_mm_level(),
        }
        perpetual_is_normal = max(perpetual_result.values()) == NORMAL_LEVEL
        return {
            TradeBusinessType.SPOT:
                dict(
                    is_normal=spot_is_normal,
                    value=spot_result
                ),
            TradeBusinessType.PERPETUAL:
                dict(
                    is_normal=perpetual_is_normal,
                    value=perpetual_result
                )
        }

    def get_level_cache_data(self, business_type) -> dict:
        _cache = UserClassIdentityCache(self.main_user_id, business_type)
        if not _cache.exists():
            # 如果没找到缓存, 重新更新缓存
            self.save_user_level_to_cache()
        return _cache.read()

    def save_user_level_to_cache(self):
        result = self.get_user_level_data()
        for _business_type, _value in result.items():
            cache = UserClassIdentityCache(self.main_user_id, _business_type)
            for _id_type, _level in _value["value"].items():
                _id_type: Enum
                cache.hset(_id_type.name, _level)

    @classmethod
    def save_user_special_fee_to_cache(cls, record: UserConfigTradeFee):
        cache = UserSpecialFeeCache(record.user_id)
        if record.market_name == '':
            market = ALL_MARKETS
        else:
            market = record.market_name
        key = f"{record.business_type.name}-{record.trade_type.name}-{market}"
        if record.status == UserConfigTradeFee.StatusType.DELETE:
            cache.hdel(key)
        else:
            expired_ts = int(record.expired_time.timestamp()) \
                if record.expired_time else 0

            ts = current_timestamp(to_int=True)
            value = dict(
                fee=str(record.fee),
                expired_time=expired_ts
            )
            if expired_ts == 0:
                cache.hset(key, json.dumps(value))
            else:
                if expired_ts - ts > 10:
                    cache.hset(key, json.dumps(value))
                else:
                    cache.hdel(key)

    def get_user_special_fee(self):
        cache = UserSpecialFeeCache(self.main_user_id)

        def parse_key(_key):
            # noinspection PyBroadException
            try:
                _result = _key.split("-")
                business_type_str, trade_type_str, _market = _result
                return True, (
                              TradeBusinessType[business_type_str],
                              TradeType[trade_type_str],
                              _market)
            except Exception:
                return False, ()

        def parse_value(_value):
            # noinspection PyBroadException
            try:
                _value = json.loads(_value)
                _fee = Decimal(_value["fee"])
                _expired_time = int(_value["expired_time"])
                ts = current_timestamp(to_int=True)
                if _expired_time > ts or _expired_time == 0:
                    return True, dict(fee=_fee, ts=_expired_time)
                else:
                    return False, dict()
            except Exception:
                return False, dict()
        result = dict()
        for key, value_str in cache.read().items():
            _key_result, _key_tuple = parse_key(key)
            if not _key_result:
                continue
            _value_result, _value_dict = parse_value(value_str)
            if not _value_result:
                continue
            business_type, trade_type, market = _key_tuple
            fee, expired_time = _value_dict["fee"], _value_dict["ts"]
            result[(business_type, trade_type, market)] = dict(
                fee=fee,
                expired_time=expired_time
            )
        return result

    @classmethod
    def get_default_business_type_fee(cls, business_type: TradeBusinessType):
        if business_type == TradeBusinessType.SPOT:
            result = {
                TradeType.TAKER: DEFAULT_SPOT_TAKER_FEE,
                TradeType.MAKER: DEFAULT_SPOT_MAKER_FEE
            }
        elif business_type == TradeBusinessType.PERPETUAL:
            result = {
                TradeType.TAKER: DEFAULT_PERPETUAL_TAKER_FEE,
                TradeType.MAKER: DEFAULT_PERPETUAL_MAKER_FEE
            }
        else:
            raise InvalidArgument
        return result

    def get_settle_fee(self, business_type: TradeBusinessType):
        # 获取交易类型的结算费率
        if business_type == TradeBusinessType.PERPETUAL:
            return self.get_business_type_market_fee(business_type, ALL_MARKETS)
        special_fee_result = self.get_user_special_fee()
        min_fee_result = self.get_level_fee_result(business_type, is_settle=True)

        for _trade_type in TradeType:
            key = (business_type, _trade_type, ALL_MARKETS)
            if key in special_fee_result:
                min_fee = min_fee_result[_trade_type]
                fee = special_fee_result[key]["fee"]
                min_fee_result[_trade_type] = min(min_fee, fee)
        return min_fee_result

    def get_marker_special_fee_info(self, business_type: TradeBusinessType, market: str) -> dict:
        # 获取某个市场的特殊费率
        special_fee_result = self.get_user_special_fee()
        min_fee_info = dict()
        for _trade_type in TradeType:
            key1 = (business_type, _trade_type, market)
            key2 = (business_type, _trade_type, ALL_MARKETS)
            lst = []
            for key in (key1, key2):
                if key not in special_fee_result:
                    continue
                lst.append(special_fee_result[key])
            if lst:
                min_fee_info[_trade_type] = min(lst, key=lambda x: x["fee"])
        return min_fee_info

    def get_level_fee_result(self, business_type: TradeBusinessType, is_settle=False) -> dict:
        # 获取用户身份的最低费率
        enum = self.get_enum(business_type)
        default_fee_result = self.get_default_business_type_fee(business_type)
        level_data = self.get_level_cache_data(business_type)
        _user_level_result = {enum[key]: int(_value) for key, _value in level_data.items()}
        if is_settle:
            _fee_result = [_enum.get_settle_fee(_level) for _enum, _level in _user_level_result.items()]
        else:
            _fee_result = [_enum.get_fee(_level) for _enum, _level in _user_level_result.items()]
        return {
            _trade_t: min([_v[_trade_t] for _v in _fee_result] + [default_fee_result[_trade_t]])
            for _trade_t in TradeType
        }

    def get_market_fee(self, market, is_amm=False):
        market_cache = MarketCache(market).dict
        min_fee_result = {
            TradeType.TAKER: Decimal(market_cache["taker_fee_rate"]),
            TradeType.MAKER: Decimal(market_cache["maker_fee_rate"])
        }
        if is_amm:
            if self.main_user_type in (
                    User.UserType.INTERNAL_MAKER,
                    User.UserType.EXTERNAL_MAKER,
                    User.UserType.EXTERNAL_SPOT_MAKER,
            ):
                min_fee_result[TradeType.MAKER] = Decimal()
        return min_fee_result

    def get_spot_market_level_result(self, market: str, is_settle=False) -> dict:
        market_fee = self.get_market_fee(market)
        if AmmMarketCache.has(market):
            if self.main_user_type in (
                    User.UserType.INTERNAL_MAKER,
                    User.UserType.EXTERNAL_MAKER,
                    User.UserType.EXTERNAL_SPOT_MAKER,
            ):
                market_fee[TradeType.MAKER] = Decimal()
            return market_fee
        else:
            level_ret = self.get_level_fee_result(TradeBusinessType.SPOT, is_settle)
            for _trade_type in TradeType:
                level_ret[_trade_type] = min(level_ret[_trade_type],
                                             market_fee[_trade_type])
            return level_ret

    def get_business_type_market_fee(self, business_type: TradeBusinessType,
                                     market: str, is_settle=False) -> dict:

        special_fee_result = self.get_user_special_fee()
        if business_type == TradeBusinessType.SPOT:
            min_fee_result = self.get_spot_market_level_result(market, is_settle)
        elif business_type == TradeBusinessType.PERPETUAL:
            min_fee_result = self.get_level_fee_result(business_type)
        else:
            raise InvalidArgument

        for _trade_type in TradeType:
            key1 = (business_type, _trade_type, market)
            key2 = (business_type, _trade_type, ALL_MARKETS)
            for key in (key1, key2):
                if key not in special_fee_result:
                    continue

                min_fee = min_fee_result[_trade_type]
                fee = special_fee_result[key]["fee"]
                min_fee_result[_trade_type] = min(min_fee, fee)

        return min_fee_result

    def get_vip_level(self):
        q = VipUser.query.filter(
            VipUser.user_id == self.main_user_id,
            VipUser.status == VipUser.StatusType.PASS
        ).with_entities(
            VipUser.user_id,
            VipUser.level
        ).first()
        return q.level if q else NORMAL_LEVEL

    def is_inner_maker(self):
        q = InnerMarketMaker.query.filter(
            InnerMarketMaker.user_id == self.main_user_id,
            InnerMarketMaker.status == InnerMarketMaker.StatusType.VALID
        ).with_entities(InnerMarketMaker.user_id).first()
        return bool(q)

    def get_spot_mm_level(self):
        # 目前内部做市商等级费率取最高等级的做市商费率
        if self.is_inner_maker():
            return max(MARKET_MAKER_DICT.keys())
        q = MarketMaker.query.filter(
            MarketMaker.user_id == self.main_user_id,
            MarketMaker.maker_type == MarketMaker.MakerType.SPOT,
            MarketMaker.status == MarketMaker.StatusType.PASS
        ).with_entities(
            MarketMaker.level
        ).first()
        return q.level if q else NORMAL_LEVEL

    def get_perpetual_mm_level(self):
        # 目前内部做市商等级费率取最高等级的做市商费率
        if self.is_inner_maker():
            return max(PERPETUAL_MARKET_MAKER_DICT.keys())
        q = MarketMaker.query.filter(
            MarketMaker.user_id == self.main_user_id,
            MarketMaker.maker_type == MarketMaker.MakerType.PERPETUAL,
            MarketMaker.status == MarketMaker.StatusType.PASS
        ).with_entities(
            MarketMaker.level
        ).first()
        return q.level if q else NORMAL_LEVEL

    def fetch_fee_deduction_rate(self, market_name: str = None) -> Decimal:
        """获取CET抵扣"""
        if value := OrderTradeFeeDiscountCache(self.main_user_id).read():
            result = json.loads(value)
            if market_name and (v := result.get(market_name)):
                return Decimal(v)
            if v := result.get(ALL_MARKETS):
                return Decimal(v)
        return SiteSettings.fee_deduction_rate

    @classmethod
    def min_with_zero_as_max(cls, lst):
        # 返回过期时间中最小的数字，0为最大
        lst = [i for i in lst if i is not None]
        return min(lst, key=lambda x: float('inf') if x == 0 else x)


class FeeFetcher(object):

    def __init__(self, user_id: int):
        self.user_id = UserFeeParser.get_main_user_id(user_id)
        self.parser = UserFeeParser(self.user_id)

    def fetch(self, business_type: TradeBusinessType, market: str):
        return self.parser.get_business_type_market_fee(business_type, market)

    def fetch_business_type_display(self, business_type: TradeBusinessType):
        return self.parser.get_business_type_market_fee(business_type, ALL_MARKETS)

    def fetch_fee_deduction_rate(self, market_name: str = None) -> Decimal:
        """获取CET抵扣"""
        if value := OrderTradeFeeDiscountCache(self.user_id).read():
            result = json.loads(value)
            if market_name and (v := result.get(market_name)):
                return Decimal(v)
            if v := result.get(ALL_MARKETS):
                return Decimal(v)
        return SiteSettings.fee_deduction_rate

    def fetch_all_special_market_fee(self):
        return self.parser.get_user_special_fee()


class _UserLevelLocalCache:
    """
    10分钟过期时间的用户等级本地缓存，使用时不要import该类，需import该类的唯一实例

    该缓存目前仅供评论使用，其特征是读多写少，热点明显，且只有主账号可以发表评论，
    所以这里无需UserFeeParser(user_id) 初始化时的main_user查询，以避免本地缓存失效时，对数据库的并发请求
    """

    def __init__(self, id_type: UserClassIdPerpetualEnum, business_type: TradeBusinessType = TradeBusinessType.SPOT):
        self.id_type = id_type
        self.business_type = business_type

    @mem_cached(60 * 10)
    def get(self, user_id):
        level_cache = UserClassIdentityCache(user_id, self.business_type)
        if not level_cache.exists():
            UserFeeParser(user_id).save_user_level_to_cache()
        return level_cache.read().get(self.id_type.name, '0')


UserVipLevelLocalCache = _UserLevelLocalCache(UserClassIdPerpetualEnum.VIP)


@celery_task
@lock_call(with_args=True)
def update_user_fee_task(user_id):
    # 子账号不在这里更新
    user = User.query.filter(User.id == user_id).first()
    if user.is_sub_account:
        return
    tool = UserFeeParser(user_id)
    tool.save_user_level_to_cache()


@celery_task
@lock_call(with_args=True)
def update_user_special_fee_task(user_id):
    # 子账号不在这里更新
    user = User.query.filter(User.id == user_id).first()
    if user.is_sub_account:
        return
    q = UserConfigTradeFee.query.filter(
        UserConfigTradeFee.user_id == user.id,
    ).all()
    tool = UserFeeParser(user_id)
    for v in q:
        tool.save_user_special_fee_to_cache(v)


@scheduled(crontab(minute="*/10"))
@lock_call()
def update_expired_user_fee_schedule():
    q = UserConfigTradeFee.query.filter(
        UserConfigTradeFee.status == UserConfigTradeFee.StatusType.PASS,
        and_(
            UserConfigTradeFee.expired_time < now(),
            UserConfigTradeFee.expired_time.isnot(None)
        )
    ).all()

    for v in q:
        UserFeeParser.save_user_special_fee_to_cache(v)


@scheduled(crontab(minute="*/30"))
@lock_call()
def update_fee_discount_schedule():
    rows = UserTradeFeeDiscount.query.filter(
        UserTradeFeeDiscount.status == UserTradeFeeDiscount.StatusType.PASS,
        or_(
            UserTradeFeeDiscount.expired_time.is_(None),
            UserTradeFeeDiscount.expired_time > now()
        )
    ).all()
    result = defaultdict(dict)
    for row in rows:
        result[row.user_id][row.market_name] = amount_to_str(row.discount)

    for k, v in result.items():
        OrderTradeFeeDiscountCache(k).set(json.dumps(v))

    all_users = UserTradeFeeDiscount.query.with_entities(
        UserTradeFeeDiscount.user_id.distinct()
    ).all()

    del_users = {x for x, in all_users} - set(result.keys())
    for user_id in del_users:
        OrderTradeFeeDiscountCache(user_id).delete()


@celery_task
@lock_call(with_args=True)
def update_user_fee_discount_task(user_id: int):
    rows = UserTradeFeeDiscount.query.filter(
        UserTradeFeeDiscount.status == UserTradeFeeDiscount.StatusType.PASS,
        UserTradeFeeDiscount.user_id == user_id,
        or_(
            UserTradeFeeDiscount.expired_time.is_(None),
            UserTradeFeeDiscount.expired_time > now()
        )
    ).all()
    result = {}
    for row in rows:
        result[row.market_name] = amount_to_str(row.discount)
    if result:
        OrderTradeFeeDiscountCache(user_id).set(json.dumps(result))
    else:
        OrderTradeFeeDiscountCache(user_id).delete()
