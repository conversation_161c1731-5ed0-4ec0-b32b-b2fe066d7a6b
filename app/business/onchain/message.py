from decimal import Decimal

from flask import current_app
from flask_babel import force_locale, gettext as _

from app.business import lock_call, ServerClient, send_mobile_push_by_user_ids
from app.business.push import get_user_app_lang, get_user_web_lang
from app.business.push_base import PushBusinessHandler
from app.common import CeleryQueues, WebPushChannelType, NoticePushType, WebPushMessageType
from app.common.onchain import OrderSide
from app.models.onchain import OnchainOrder, OnchainToken
from app.utils import celery_task, current_timestamp
from app.utils.onchain import decimal_div, format_decimal_display
from app.utils.push import WebPagePath


@celery_task(queue=CeleryQueues.ONCHAIN)
@lock_call(with_args=True)
def send_order_finish_notice(order_id: int):
    order: OnchainOrder = OnchainOrder.query.get(order_id)
    token: OnchainToken = OnchainToken.query.get(order.token_id)
    user_id = order.user_id
    price = decimal_div(order.money_amount, order.token_amount) if order.token_amount and Decimal(order.token_amount) else 0
    params = dict(
        token_symbol=token.symbol,
        side=order.side,
        amount=format_decimal_display(order.token_amount, 8),
        price=format_decimal_display(price, 8),
        money_asset=order.money_asset,
    )

    def get_title_and_message(lang: str):
        with force_locale(lang):
            side = _('买入') if params['side'] == OrderSide.BUY else _('卖出')
            title = {
                OnchainOrder.Status.FINISHED: _("链上订单成交提醒"),
                OnchainOrder.Status.FAILED: _("链上订单委托提醒"),
                OnchainOrder.Status.CANCELLED: _("链上订单委托提醒"),
            }[order.status]
            message = {
                OnchainOrder.Status.FINISHED: _(
                    "你的%(token_symbol)s%(side)s委托已成交，成交数量%(amount)s %(token_symbol)s，成交均价%(price)s %(money_asset)s。",
                    token_symbol=params['token_symbol'], side=side, amount=params['amount'],
                    price=params['price'], money_asset=params['money_asset'],
                ),
                OnchainOrder.Status.FAILED: _(
                    "你的%(token_symbol)s%(side)s委托失败。",
                    token_symbol=params['token_symbol'], side=side,
                ),
                OnchainOrder.Status.CANCELLED: _(
                    "你的%(token_symbol)s%(side)s已取消。",
                    token_symbol=params['token_symbol'], side=side,
                ),
            }[order.status]
            return title, message

    web_lang = get_user_web_lang(order.user_id).value
    web_title, web_message = get_title_and_message(web_lang)
    notice_push_type = {
        OnchainOrder.Status.FINISHED: NoticePushType.SUCCESS,
        OnchainOrder.Status.FAILED: NoticePushType.FAIL,
        OnchainOrder.Status.CANCELLED: NoticePushType.NOTICE,
    }[order.status]
    try:
        ServerClient().notice_user_message(
            user_id,
            WebPushChannelType.NORMAL.value,
            dict(
                title=web_title,
                content=web_message,
                url=WebPagePath.ONCHAIN_ORDER_HISTORY.value,
                type=notice_push_type,
                msg_type=WebPushMessageType.ONCHAIN.value,
            )
        )
    except Exception as e:
        current_app.logger.error(f"onchain server notice error: {e}")

    handler = PushBusinessHandler(None)
    if not handler.can_push([user_id]):
        return
    app_lang = get_user_app_lang(user_id).value
    app_title, app_message = get_title_and_message(app_lang)
    send_mobile_push_by_user_ids.delay(
        user_ids=[user_id],
        content=app_message,
        title=app_title,
        ttl=0,
        url=WebPagePath.ONCHAIN_ORDER_HISTORY.value,
        created_at=current_timestamp(to_int=True),
    )
    handler.set_pushed([user_id])
