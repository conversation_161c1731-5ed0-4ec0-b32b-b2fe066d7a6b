# -*- coding: utf-8 -*-

from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from itertools import chain
from typing import Dict
from sqlalchemy import func
from flask_babel import gettext as _

from flask import current_app
from app import config
from app.assets.asset import asset_to_default_chain, get_asset_config
from app.business import SPOT_ACCOUNT_ID, ServerClient
from app.business.alert import send_alert_notice
from app.business.clients.wallet import WalletClient
from app.business.external_dbs import TradeLogDB
from app.common.constants import BalanceBusiness, PrecisionEnum, ServerBalanceType
from app.exceptions.basic import InvalidArgument
from app.exceptions.legacy import InvestmentTransferError
from app.models import db, User
from app.models.daily import DailyStakingReport
from app.models.staking import StakingAccount, StakingHistory, StakingPool, StakingRewardHistory, StakingSystemSummary, StakingUserSummary
from app.utils.amount import quantize_amount
from app.utils.date_ import date_to_datetime, now
from app.utils.iterable import batch_iter

_configs = {
    "CET": {
        "min_staking_amount": Decimal("1000"),
        "min_unstaking_amount": Decimal("1000"),
        "validator_amount": Decimal("1000000"),
        "staking_threshold": Decimal("********"),
        "unstaking_threshold": Decimal("********"),
        "system_sharing_ratio": Decimal(),
    },
    "ETH": {
        "min_staking_amount": Decimal("0.01"),
        "min_unstaking_amount": Decimal("0.01"),
        "validator_amount": Decimal("32"),
        "staking_threshold": Decimal("1024"),
        "unstaking_threshold": Decimal("1024"),
        "system_sharing_ratio": Decimal("0.1"), # 平台佣金比例
    },
    "SOL": {
        "min_staking_amount": Decimal("0.1"),
        "min_unstaking_amount": Decimal("0.1"),
        "validator_amount": Decimal("250"),
        "staking_threshold": Decimal("5000"),
        "unstaking_threshold": Decimal("5000"),
        "system_sharing_ratio": Decimal("0.1"),
    },
    "ADA": {
        "min_staking_amount": Decimal("100"),
        "min_unstaking_amount": Decimal("100"),
        "validator_amount": Decimal("10000"),
        "staking_threshold": Decimal("1000000"),
        "unstaking_threshold": Decimal("1000000"),
        "system_sharing_ratio": Decimal("0.1"),
    },
    "TRX": {
        "min_staking_amount": Decimal("100"),
        "min_unstaking_amount": Decimal("100"),
        "validator_amount": Decimal("10000"),
        "staking_threshold": Decimal("2000000"),
        "unstaking_threshold": Decimal("2000000"),
        "system_sharing_ratio": Decimal("0.1"),
    },
    "DOT": {
        "min_staking_amount": Decimal("0.01"),
        "min_unstaking_amount": Decimal("0.01"),
        "validator_amount": Decimal("10000"),
        "staking_threshold": Decimal("5000"),
        "unstaking_threshold": Decimal("5000"),
        "system_sharing_ratio": Decimal("0.1"),
    }
}


class StakingOperation:

    def __init__(self, asset: str) -> None:
        self.asset = asset
        self.server_client = ServerClient(current_app.logger)


    @classmethod
    def get_asset_order_list(cls):
        return list(_configs)


    def get_configs(self):
        return _configs[self.asset]

    def transfer_in(self, user: User, amount: Decimal) -> StakingHistory.Status:
        from app.business.user import require_user_not_only_withdrawal
        require_user_not_only_withdrawal(user)
        user_id = user.id
        conf = _configs[self.asset]
        summary = StakingUserSummary.get_or_create(user_id=user_id, asset=self.asset)
        if not summary.staking_amount:
            summary.staking_amount = 0
        if self._get_user_balance(user.id, account_id=SPOT_ACCOUNT_ID) < amount \
            or amount < conf["min_staking_amount"]: 
            raise InvalidArgument(message=_("可质押数量不足"))
        
        db.session.add(summary)
        sys_amount = WalletClient().get_staking_summary(self.asset)['activated_staking_amount']
        pool = StakingPool.query.filter(StakingPool.asset == self.asset).first()
        if sys_amount - pool.amount + pool.unstaking_amount >= amount:
            status = StakingHistory.Status.CREATED
        else:
            status = StakingHistory.Status.QUEUED
        history = StakingHistory(
            user_id=user_id,
            asset=self.asset,
            amount=amount,
            type=StakingHistory.Type.STAKE,
            status=status,
            transfer_status=StakingHistory.TransferStatus.CREATED,
        )
        db.session_add_and_commit(history)
        result = self.do_transfer_in(history, pool, summary)
        if not result:
            raise InvalidArgument(message=_("质押失败，请重试"))
        return status
        

    def transfer_out(self, user: User, amount: Decimal) -> None:
        balance = self._get_user_balance(user.id, account_id=StakingAccount.ACCOUNT_ID)
        summary = StakingUserSummary.query.filter(StakingUserSummary.user_id == user.id,
                                                  StakingUserSummary.asset == self.asset).first()
        unstaking_amount = summary.unstaking_amount
        conf = _configs[self.asset]
        if amount + unstaking_amount > balance or amount < conf["min_unstaking_amount"]:
            raise InvalidArgument(message=_("可赎回余额不足"))
        history = StakingHistory(
            user_id=user.id,
            asset=self.asset,
            amount=amount,
            type=StakingHistory.Type.UNSTAKE,
        )
        db.session.add(history)
        summary.unstaking_amount += amount
        history.status = StakingHistory.Status.QUEUED
        pool = StakingPool.query.filter(StakingPool.asset == self.asset).first()
        pool.unstaking_amount += amount
        db.session.commit()



    def do_transfer_out(self, history: StakingHistory, 
                        pool: StakingPool, 
                        summary: StakingUserSummary) -> bool:
        history_id = history.id
        transfer_from, transfer_to = StakingAccount.ACCOUNT_ID, SPOT_ACCOUNT_ID
        business = BalanceBusiness.STAKING_REMOVE

        if history.transfer_status == StakingHistory.TransferStatus.CREATED:
            try:
                self.server_client.add_user_balance(
                    user_id=history.user_id,
                    asset=self.asset,
                    business=business,
                    business_id=history_id,
                    amount=Decimal(-abs(history.amount)),
                    detail={"remark": "investment for transfer"},
                    account_id=transfer_from,
                )
            except Exception as e:
                current_app.logger.error(f'Staking Transfer {history_id} deduct failed: {e!r}')
                return False
            
            assert summary.unstaking_amount >= history.amount
            summary.unstaking_amount -= history.amount
            pool.amount -= history.amount
            pool.unstaking_amount -= history.amount
            history.transfer_status = StakingHistory.TransferStatus.DEDUCTED
            db.session.commit()
        
        if history.transfer_status != StakingHistory.TransferStatus.DEDUCTED:
            return False
        try:
            self.server_client.add_user_balance(
                user_id=history.user_id,
                asset=history.asset,
                business=business,
                business_id=history_id,
                amount=Decimal(abs(history.amount)),
                detail={"remark": "investment for transfer"},
                account_id=transfer_to,
            )
        except Exception as e:
            current_app.logger.error(f'Staking Transfer {history_id} add failed: {e!r}')
            raise InvestmentTransferError
        history.transfer_status = StakingHistory.TransferStatus.FINISHED
        
        history.status = StakingHistory.Status.FINISHED
        history.finished_at = now()
        db.session.commit()
        return True
    
    def do_transfer_in(self, history: StakingHistory, 
                       pool: StakingPool, 
                       summary: StakingUserSummary) -> bool:
        history_id = history.id
        transfer_from, transfer_to = SPOT_ACCOUNT_ID, StakingAccount.ACCOUNT_ID
        business = BalanceBusiness.STAKING_ADD

        if history.transfer_status == StakingHistory.TransferStatus.CREATED:
            try:
                self.server_client.add_user_balance(
                    user_id=history.user_id,
                    asset=self.asset,
                    business=business,
                    business_id=history_id,
                    amount=Decimal(-abs(history.amount)),
                    detail={"remark": "investment for transfer"},
                    account_id=transfer_from,
                )
            except Exception as e:
                current_app.logger.error(f'Staking Transfer {history_id} deduct failed: {e!r}')
                return False
            
            summary.staking_amount += history.amount
            history.transfer_status = StakingHistory.TransferStatus.DEDUCTED
            db.session.commit()
        
        if history.transfer_status != StakingHistory.TransferStatus.DEDUCTED:
            return False
        
        if history.status == StakingHistory.Status.QUEUED:
            is_lock = True
        else:
            is_lock = False
        try:
            if is_lock:
                meth = self.server_client.add_and_lock_user_balance
            else:
                meth = self.server_client.add_user_balance
            meth(
                user_id=history.user_id,
                asset=history.asset,
                business=business,
                business_id=history_id,
                amount=Decimal(abs(history.amount)),
                detail={"remark": "investment for transfer"},
                account_id=transfer_to,
            )
        except Exception as e:
            current_app.logger.error(f'Staking Transfer {history_id} add failed: {e!r}')
            raise InvestmentTransferError
        history.transfer_status = StakingHistory.TransferStatus.FINISHED
        
        if not is_lock:
            assert summary.staking_amount >= history.amount

            summary.staking_amount -= history.amount
            pool.amount += history.amount
            history.status = StakingHistory.Status.FINISHED
            history.finished_at = now()
        db.session.commit()
        return True
    
    
    def _get_user_balance(self, user_id: int, account_id: int) -> Decimal:
        result = self.server_client.get_user_balances(user_id, 
                                                      self.asset, account_id=account_id)
        return Decimal(result.get(self.asset, {}).get("available", Decimal()))
    
    def get_snapshot_balance(self, ts):
        table = TradeLogDB.slice_balance_table(ts)
        if not table:
            return
        # result = table.select(
        #         'user_id',
        #         where=f'account={StakingAccount.ACCOUNT_ID} and t '
        #               f'in ({ServerBalanceType.FROZEN.value}, '
        #               f'{ServerBalanceType.LOCK.value})')
        # if result:
        #     current_app.logger.error(f"staking balance type error, ts {ts}")
        #     assert not bool(result)
        fields = ('user_id', 'asset', 'balance')
        results = table.select(
                "user_id", "asset", 'SUM(`balance`) `balance`',
                where=f"account={StakingAccount.ACCOUNT_ID} "
                      f"and t = {ServerBalanceType.AVAILABLE.value} and asset = '{self.asset}'",
                group_by='`user_id`, `asset`'
                )
        results = [dict(zip(fields, r)) for r in results]
        return results

    def _reward_transfer(self, balance_log):
        try:
            self.server_client.add_user_balance(
                    user_id=balance_log.user_id,
                    asset=balance_log.asset,
                    amount=str(balance_log.amount),
                    business=BalanceBusiness.STAKING_INCOME,
                    business_id=balance_log.id,
                    detail={"remark": "staking reward"},
                    account_id=SPOT_ACCOUNT_ID,
                    )
            balance_log.status = StakingRewardHistory.Status.FINISHED
            db.session.commit()
        except Exception as e:
            balance_log.status = StakingRewardHistory.Status.FAILED
            db.session.commit()
            raise e

    def get_total_staking_amount(self, report_at: datetime | None) -> Decimal:
        """
        平台总质押数，取链上激活的质押数
        """
        chain_amount = 0
        if report_at:
            chain_amount = StakingSystemSummary.query.filter(
                StakingSystemSummary.report_at == report_at,
                StakingSystemSummary.asset == self.asset,
            ).with_entities(StakingSystemSummary.staking_amount).scalar() or 0
        if not chain_amount:
            r = WalletClient().get_staking_summary(self.asset, 
                                                   asset_to_default_chain(self.asset))
            chain_amount = r['activated_staking_amount']
        return chain_amount


    def _sum_reward(self, start_time: datetime, end_time: datetime, user_reward: Decimal) -> Dict[int, Decimal]:
        if user_reward <= 0:
            current_app.logger.warning(f"staking hourly reward: {user_reward} less than 0 {start_time}")
            return {}
        unstaking_map = defaultdict(Decimal)
        unstake_history = StakingHistory.query.filter(
            StakingHistory.status.in_((StakingHistory.Status.QUEUED,
                                       StakingHistory.Status.FAILED)),
            StakingHistory.type == StakingHistory.Type.UNSTAKE,
            StakingHistory.created_at < end_time,
            StakingHistory.asset == self.asset,
        ).with_entities(
            StakingHistory.user_id,
            StakingHistory.amount
        ).all()

        finished_unstake_history = StakingHistory.query.filter(
            StakingHistory.status == StakingHistory.Status.FINISHED,
            StakingHistory.type == StakingHistory.Type.UNSTAKE,
            StakingHistory.created_at < end_time,
            StakingHistory.finished_at > end_time,
            StakingHistory.asset == self.asset,
        ).with_entities(
            StakingHistory.user_id,
            StakingHistory.amount
        ).all()

        for item in chain(unstake_history, finished_unstake_history):
            unstaking_map[item.user_id] += item.amount
        new_staking_records = StakingHistory.query.filter(
            StakingHistory.finished_at >= start_time,
            StakingHistory.finished_at < end_time,
            StakingHistory.status == StakingHistory.Status.FINISHED,
            StakingHistory.type == StakingHistory.Type.STAKE,
            StakingHistory.asset == self.asset,
        ).group_by(
            StakingHistory.user_id
        ).with_entities(StakingHistory.user_id, func.sum(StakingHistory.amount)).all()
        new_staking_map = dict(new_staking_records)
        new_unstaking_records = StakingHistory.query.filter(
            StakingHistory.finished_at >= start_time,
            StakingHistory.finished_at < end_time,
            StakingHistory.status == StakingHistory.Status.FINISHED,
            StakingHistory.type == StakingHistory.Type.UNSTAKE,
            StakingHistory.asset == self.asset,
        ).group_by(StakingHistory.user_id).with_entities(StakingHistory.user_id, 
                                                         func.sum(StakingHistory.amount)).all()
        new_unstaking_map = dict(new_unstaking_records)
        total_unstaking_amount = sum(unstaking_map.values())
        total_balance = 0
        balances = self.get_snapshot_balance(int(end_time.timestamp()))
        if balances is None:
            send_alert_notice(
                f"staking account balance not found: {end_time}", config["ADMIN_CONTACTS"]["web_notice"])
            return {}
        if balances:
            total_balance = sum(item['balance'] for item in balances)
        total_effective_balance = total_balance - total_unstaking_amount
        participant_ids = set(new_staking_map.keys()) | set(new_unstaking_map.keys())
        for id_ in participant_ids:
            stake_ = new_staking_map.get(id_, 0)
            unstake_ = new_unstaking_map.get(id_, 0)
            net_stake = stake_ - unstake_
            if net_stake < 0:
                continue
            total_effective_balance -= net_stake
        
        next_dt = date_to_datetime(start_time.date() + timedelta(days=1))
        total_effective_balance = max(total_effective_balance, self.get_total_staking_amount(next_dt))
        if total_effective_balance <= 0:
            return {}
        result_map = dict()
        for item in balances:
            balance = item['balance']
            unstaking_amount = unstaking_map.get(item['user_id'], 0)
            new_staking_amount = new_staking_map.get(item['user_id'], 0)
            new_unstaking_amount = new_unstaking_map.get(item['user_id'], 0)
            net_new_staking_amount = new_staking_amount - new_unstaking_amount
            effective_balance = balance - unstaking_amount
            if net_new_staking_amount > 0:
                effective_balance -= net_new_staking_amount
            if effective_balance <= 0:
                continue
            _reward = user_reward * effective_balance / total_effective_balance
            # print("HERE", item['user_id'], user_reward, effective_balance, total_effective_balance)
            _reward = quantize_amount(_reward, PrecisionEnum.COIN_PLACES)
            if _reward <= 0:
                continue
            result_map[item['user_id']] = _reward
        return result_map


    def _get_daily_reward(self, start_time: datetime, end_time: datetime) -> Decimal:
        apr = 0
        try:
            apr = WalletClient().get_staking_summary(self.asset)['apr']
        except Exception:
            current_app.logger.error(f"staking apr get error {start_time} {self.asset}")
        user_apr = apr * (1 - self.get_configs()["system_sharing_ratio"])
        # 用最后一天的年化率
        if not user_apr:
            last_report = DailyStakingReport.query.filter(
                DailyStakingReport.asset == self.asset,
            ).order_by(DailyStakingReport.report_date.desc()).first()
            user_apr = last_report.income_rate
        total_staking_amount = self.get_total_staking_amount(end_time)
        reward = total_staking_amount * user_apr / 365
        return quantize_amount(reward, PrecisionEnum.COIN_PLACES)

    def send_reward(self, start_time: datetime, end_time: datetime) -> Decimal:
        r = StakingRewardHistory.query.filter(StakingRewardHistory.asset == self.asset, 
                                              StakingRewardHistory.reward_at == end_time).first()
        if r:
            return
        total_reward_map = defaultdict(Decimal)
        daily_reward = self._get_daily_reward(start_time, end_time)
        if daily_reward <= 0:
            current_app.logger.warning(f"daily reward: {daily_reward} less than 0 {start_time} {self.asset}")
            return

        while start_time < end_time:
            start = start_time
            end = start_time + timedelta(hours=1)
            hourly_reward = quantize_amount(daily_reward / 24, PrecisionEnum.COIN_PLACES)
            reward_map = self._sum_reward(start, end, hourly_reward)
            for k, v in reward_map.items():
                total_reward_map[k] += v
            start_time += timedelta(hours=1)
        new_history = []
        for user_id, amount in total_reward_map.items():
            history = StakingRewardHistory(
                user_id=user_id,
                reward_at=end_time,
                asset=self.asset,
                amount=amount,
            )
            new_history.append(history)
        for rows in batch_iter(new_history, 1000):
            db.session.bulk_save_objects(rows)
        db.session.commit()
        records = StakingRewardHistory.query.filter(
            StakingRewardHistory.reward_at == end_time,
            StakingRewardHistory.asset == self.asset,
        ).with_entities(StakingRewardHistory.id).all()
        record_ids = [r.id for r in records]
        has_failed = False
        for id_ in record_ids:
            r = StakingRewardHistory.query.get(id_)
            try:
                self._reward_transfer(r)
            except Exception:
                current_app.logger.error(f"staking reward transfer error user_id: {r.user_id}")
                has_failed = True
        if has_failed:
            send_alert_notice(
                f"staking reward send error, please check", config["ADMIN_CONTACTS"]["web_notice"])


    def has_reward(self, reward_at: datetime) -> bool:
        end_time = reward_at
        start_time = end_time - timedelta(days=1)
        reward = WalletClient().get_staking_reward(self.asset, start_time, end_time)
        for item in reward.get('reward_detail', {}).values():
            if item > 0:
                return True
        return False

    def get_stake_or_unstake_amount(self, pool) -> Decimal:
        """
        返回正数，代表质押；返回负数，代表赎回
        """
        pool_amount = pool.amount
        summary_data = WalletClient().get_staking_summary(pool.asset)
        sys_amount = summary_data['activated_staking_amount']
        pending_record = StakingUserSummary.query.filter(
            StakingUserSummary.asset == pool.asset,
        ).with_entities(
            func.sum(StakingUserSummary.staking_amount).label('staking'),
            func.sum(StakingUserSummary.unstaking_amount).label('unstaking'),
        ).first()
        pending_user_staking = pending_user_unstaking = 0
        if pending_record:
            if pending_record.staking:
                pending_user_staking = pending_record.staking
            if pending_record.unstaking:
                pending_user_unstaking = pending_record.unstaking
        bufsize = sys_amount + summary_data['pending_staking_amount'] - summary_data['pending_unstaking_amount'] \
            - (pool_amount + pending_user_staking - pending_user_unstaking)
        target_bufsize = get_asset_config(self.asset).staking_bufsize

        staking_config = self.get_configs()
        min_bufsize = target_bufsize - staking_config['staking_threshold']
        max_bufsize = target_bufsize + staking_config['unstaking_threshold']
        assert min_bufsize > 0

        new_staking = new_unstaking = 0
        validator_amount = self.get_configs()['validator_amount']
        if bufsize < min_bufsize:
            new_staking = target_bufsize - bufsize
            new_staking = max(new_staking, Decimal())
            if new_staking == Decimal():
                return Decimal()
            if new_staking % validator_amount > Decimal():
                # 向上取整
                new_staking -= new_staking % validator_amount
                new_staking += validator_amount
            return new_staking
        elif bufsize > max_bufsize:
            new_unstaking = bufsize - target_bufsize
            new_unstaking = max(new_unstaking , Decimal())
            if new_unstaking == Decimal():
                return Decimal()
            if new_unstaking % validator_amount > Decimal():
                # 向上取整
                new_unstaking -= new_unstaking % validator_amount
                new_unstaking += validator_amount
            return -abs(new_unstaking)
        else:
            return Decimal()
        
