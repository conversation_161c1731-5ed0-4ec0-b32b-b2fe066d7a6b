#!/usr/bin/env python3
import json
from collections import defaultdict
from datetime import datetime, timedelta, date
from decimal import Decimal
from typing import Dict, List, Tuple

from flask import current_app
from sqlalchemy import func

from app import config

from app.common.constants import PrecisionEnum
from ..utils import yield_query_records_by_time_range

from ...assets import try_get_asset_config
from ...business import lock_call
from ...business.amm import get_user_amm_assets
from ...caches import PerpetualMarketCache, MarketCache, UserDailyWithdrawnAmountCache
from ...caches.p2p import P2pSellRiskReqRecord
from ...common import AccountBalanceType, P2pBusinessType, CeleryQueues
from ...common.onchain import OrderSide
from ...models import (
    Deposit, GiftHistory, MarginLoanOrder, RedPacket,
    RedPacketHistory, RedPacketReturnHistory, RiskUser,
    SubAccount, UpdateAssetBalance, UserCheckRequest,
    <PERSON><PERSON><PERSON>, db, P2p<PERSON>rder<PERSON>rans<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>2p<PERSON><PERSON><PERSON>, <PERSON><PERSON>walWhitelistUser, CoinInformation,
    WithdrawalAudit, WithdrawalAddressBlacklist,
)
from ...models.wallet import AbnormalDepositApplication, AbnormalDepositApplicationTransferHistory, SalaryPay, CommissionPay, AssetPrice
from ...models.exchange import AssetExchangeOrder
from ...models.pre_trading import PreTradingUserAsset
from ...models.onchain import OnchainOrder, OnchainAssetToSpotHistory
from ...models.payment import PaymentTransaction
from ...utils import (
    celery_task, current_timestamp, datetime_to_str, now, route_module_to_celery_queue,
    quantize_amount, timestamp_to_datetime, amount_to_str, datetime_to_time, format_percent, today,
)
from ..clients import PerpetualServerClient, ServerClient
from ..external_dbs import ExchangeLogDB, PerpetualLogDB, TradeLogDB, TradeHistoryDB, TradeSummaryDB
from ..prices import PriceManager
from .base import add_risk_user, RiskControlGroupConfig, \
    ImmediatelyWithdrawalCheckCondition, NewUserImmediatelyWithdrawalCheckCondition, \
    WithdrawalNoOnChainDepositCheckCondition, get_deposit_withdrawal_rc_whitelist
from ...utils.date_ import date_to_datetime

route_module_to_celery_queue(__name__, CeleryQueues.WALLET)


@celery_task
@lock_call(with_args=True)
def execute_user_check_request_task(record_id):
    model = UserCheckRequest
    record = model.query.get(record_id)
    context = json.loads(record.context) if record.context else {}
    if not context:
        # 兼容旧p2p检查
        check_user_abnormal_profit(record.user_id, RiskUser.Reason.ABNORMAL_PROFIT)
    if isinstance(context, str):
        # 兼容旧提现检查
        bus_id = int(context)
        WithdrawalRiskCheck.execute_risk_check(bus_id, record)
    else:
        bus_id = context["bus_id"]
        cls_map = {
            model.CheckBusiness.P2p.name: P2pSellRiskCheck,
            model.CheckBusiness.Withdrawal.name: WithdrawalRiskCheck
        }
        if (bus_name := context.get("business")) in cls_map:
            cls_map[bus_name].execute_risk_check(bus_id, record)


def check_user_abnormal_profit(user_id: int, business: RiskUser.Reason, asset=None):
    """
    检查用户异常盈利
    user_id: 主账户id
    """

    if business == RiskUser.Reason.ABNORMAL_PROFIT:
        realtime = True
    elif business == RiskUser.Reason.PERIOD_ABNORMAL_PROFIT:
        realtime = False
    else:
        raise ValueError(f"invalid argument: {business}")

    source = None
    if realtime and asset:
        source = asset

    current_app.logger.info('check_user_abnormal_profit_task: %s, real_time: %s', user_id, realtime)
    ok, err = _check_user_abnormal_profit(user_id, realtime)
    if not ok:
        permissions = RiskUser.PermissionMap[business]
        add_risk_user(user_id, business, err, permissions=permissions, source=source)


def check_user_withdrawal_no_deposit(user_id: int):
    """
    检查用户 提现/p2p卖出 但没有充值记录
    user_id: 主账户id
    """
    ok, err = _check_user_withdrawal_no_deposit(user_id)
    if not ok:
        add_risk_user(user_id, RiskUser.Reason.WITHDRAWAL_NO_DEPOSIT, err)


def _check_user_abnormal_profit(user_id: int, realtime: bool) -> Tuple[bool, str]:
    """
    检查用户异常盈利，(current_balance + withdrawal - (last_balance + deposit) - change) / (last_balance + deposit) > 150%
    其中withdrawal包括链上交易的buy统计，deposit包括链上交易的sell统计
    """
    # 实时异常盈利检查时间为UTC前一天0点~当前时间，定期异常盈利检查时间为UTC前一天0点~今天0点
    timestamp = int(current_timestamp())
    timestamp = timestamp // 86400 * 86400 - 86400

    sub_user_ids = SubAccount.query.filter(SubAccount.main_user_id == user_id).with_entities(SubAccount.user_id).all()
    sub_user_ids = [x for x, in sub_user_ids]
    asset_prices = PriceManager.assets_to_usd()

    bm = BalanceManager(user_id, sub_user_ids, asset_prices)
    last_balance, start_time = bm.get_snapshot_balance_usd(timestamp)
    start_time = timestamp_to_datetime(start_time)

    if realtime:
        current_balance = bm.get_current_balance_usd()
        end_time = now()
    else:
        current_balance, end_time = bm.get_snapshot_balance_usd(timestamp + 86400)
        end_time = timestamp_to_datetime(end_time)
        if end_time <= start_time:
            current_app.logger.info('get snapshot balance failed: same start and end time.')
            return True, ''

    bcm = BalanceChangManager(user_id, start_time, end_time, sub_user_ids, asset_prices)
    deposit_amount = bcm.get_deposit_usd()
    payment_receive_amount = bcm.get_payment_receive_usd()
    withdrawal_amount = bcm.get_withdrawal_usd()
    payment_pay_amount = bcm.get_payment_pay_usd()
    cancelled_withdrawal_amount = bcm.get_cancelled_withdrawal_usd()
    onchain_buy_amount = bcm.get_onchain_buy_usd()
    onchain_sell_amount = bcm.get_onchain_sell_usd()
    onchain_asset_to_spot_amount = bcm.get_onchain_asset_to_spot_usd()
    asset_change_amount = bcm.get_asset_change_usd()
    gift_amount = bcm.get_gift_usd()
    red_packet_amount = bcm.get_red_packet_usd()
    salary_pay_amount = bcm.get_salary_pay_usd()
    commission_pay_amount = bcm.get_commission_pay_usd()
    p2p_buy_amount, p2p_sell_amount = bcm.get_p2p_usd()
    ab_deposit_amount, ab_change_amount = bcm.get_abnormal_deposit_application_usd()
    # 广义提现, P2P卖出、链上交易买入、扫码付款等均可以视为广义上的提现操作
    general_withdrawal = withdrawal_amount + onchain_buy_amount + p2p_sell_amount + payment_pay_amount
    # 广义充值, P2P买入、链上交易卖出、扫码收款等均可以视为广义上的充值操作
    general_deposit = (deposit_amount + ab_deposit_amount + onchain_sell_amount + p2p_buy_amount +
                       payment_receive_amount + onchain_asset_to_spot_amount)
    principal = last_balance + general_deposit
    # 公式移除赠送金额
    change_amount = (cancelled_withdrawal_amount + asset_change_amount + red_packet_amount +
                     salary_pay_amount + commission_pay_amount + ab_change_amount)
    profit_amount = (current_balance + general_withdrawal) - principal - change_amount

    risk = profit_amount / principal if principal != 0 else float('inf')

    conf = RiskControlGroupConfig().abnormal_profit

    if profit_amount > conf['abnormal_profit_amount'] and risk > conf['abnormal_profit_rate']:
        start_time = datetime_to_str(start_time, 60*8)
        end_time = datetime_to_str(end_time, 60*8)
        err = (f"上次对帐时间{start_time}, 本次对帐时间{end_time}, 上次剩余价值{last_balance.normalize():.2f}USD,  "
               f"本次剩余价值{current_balance.normalize():.2f}USD, "
               f"充值{deposit_amount.normalize():.2f}USD, 提现{withdrawal_amount.normalize():.2f}USD, "
               f"链上交易卖出{onchain_sell_amount.normalize():.2f}USD, 链上交易买入{onchain_buy_amount.normalize():.2f}USD, "
               f"提现取消{cancelled_withdrawal_amount.normalize():.2f}USD, 资产变更{asset_change_amount.normalize():.2f}USD, "
               f"系统赠送{gift_amount.normalize():.2f}USD, 红包收支{red_packet_amount.normalize():.2f}USD, "
               f"薪资发放{salary_pay_amount.normalize():.2f}USD, "
               f"P2P买入{p2p_buy_amount.normalize():.2f}USD, P2P卖出{p2p_sell_amount.normalize():.2f}USD, "
               f"盈利{profit_amount.normalize():.2f}USD,百分比{risk*100: .2f}%")
        return False, err
    return True, ''


def _check_user_withdrawal_no_deposit(user_id: int) -> Tuple[bool, str]:
    """
    检查充提，无充值记录(包括红包)&累计提现大于10USD
    """
    deposit = Deposit.query.filter(
        Deposit.user_id == user_id,
        Deposit.status.in_((
            Deposit.Status.FINISHED,
            Deposit.Status.TO_HOT
        ))
    ).first()
    if deposit:
        return True, ''

    if has_payment_receive(user_id):
        return True, ''

    red_packet = RedPacketHistory.query.filter(
        RedPacketHistory.user_id == user_id,
        RedPacketHistory.status == RedPacketHistory.Status.FINISHED
    ).first()
    if red_packet:
        return True, ''

    withdrawal_assets = BalanceChangManager.query_withdrawal_assets(user_id, end_time=now())
    prices = PriceManager.assets_to_usd()
    withdrawal_amount = sum(prices.get(k, 0) * v for k,v in withdrawal_assets.items())
    withdrawal_amount = quantize_amount(withdrawal_amount, 2)
    if withdrawal_amount > Decimal(10):
        err = f'无充值(红包)记录，累计提现{withdrawal_amount:f}USD'
        return False, err
    return True, ''


def check_user_immediately_withdrawal(user_id: int, asset, amount, created_at, business: RiskUser.Reason, check_bus):
    """
    检查用户即充即提
    user_id: 主账户id
    """
    if not asset:
        return
    if business != RiskUser.Reason.IMMEDIATELY_WITHDRAWAL:
        return
    current_app.logger.info('check_user_immediately_withdrawal: %s', user_id)
    ok, err = _check_user_immediately_withdrawal(user_id, asset, amount, created_at, check_bus)
    if not ok:
        permissions = RiskUser.PermissionMap[business]
        add_risk_user(user_id, business, err, permissions=permissions, source=asset)


def has_deposit(user_id, st=None, et=None, only_chain=False, with_pool_deposit=False):
    model = Deposit
    pool_user_id = config['CLIENT_CONFIGS']['viabtc_pool']['user_id']
    query = model.query.filter(
        model.user_id == user_id,
        model.status.in_([
            model.Status.FINISHED,
            model.Status.CONFIRMING,
            model.Status.TO_HOT,
        ]),
    )
    if only_chain:
        query = query.filter(model.type == model.Type.ON_CHAIN)
    if not with_pool_deposit:
        # 排除矿池来源的充值
        query = query.filter(model.sender_user_id != pool_user_id)
    if st and et:
        query = query.filter(
            model.created_at >= st,
            model.created_at < et,
        )
    return bool(query.first())


def has_p2p_buy(user_id, st=None, et=None):
    model = P2pOrder
    query = P2pOrder.get_user_buy_query(user_id)
    if st and et:
        query = query.filter(
            model.created_at >= st,
            model.created_at < et,
        )
    return bool(query.first())


def has_payment_receive(user_id, st=None, et=None):
    model = PaymentTransaction
    query = model.query.filter(
        model.receiver_id == user_id,
        model.status == model.Status.FINISHED,
    )
    if st and et:
        query = query.filter(
            model.finished_at >= st,
            model.finished_at < et,
        )
    payment_receive_row = query.with_entities(model.id).first()
    return bool(payment_receive_row)


def has_asset_input(user_id, st=None, et=None, only_chain=False, with_pool_deposit=False):
    return has_deposit(user_id, st, et, only_chain, with_pool_deposit) or has_p2p_buy(user_id, st, et)\
           or has_payment_receive(user_id, st, et)


def _check_user_immediately_withdrawal(
        user_id: int, asset, amount, withdrawal_at, check_bus: UserCheckRequest.CheckBusiness
) -> Tuple[bool, str]:
    """https://viabtc.yuque.com/description/coinex/swlw2bq7ubt9gwgm?singleDoc#hrSRJ"""
    user = User.query.get(user_id)
    if user.kyc_status is User.KYCStatus.PASSED:
        prefix = ''
        conf = RiskControlGroupConfig().immediately_charged
    else:
        prefix = 'no_kyc_'
        conf = RiskControlGroupConfig().immediately_charged_no_kyc
    price = PriceManager.asset_to_usd(asset)
    withdrawal_usd = price * amount
    if withdrawal_usd < conf[f'{prefix}ic_monitor_threshold']:
        return True, ''
    if ImmediatelyWithdrawalCheckCondition.check(user_id):
        return True, ''

    def _is_privacy_asset(_asset):
        cfg = try_get_asset_config(_asset)
        if cfg and cfg.is_privacy:
            return True
        return False

    def _trade_result() -> str | None:

        def _check_one_by_one(res: dict, final=False):
            # 先检查单一来源记录，最终检查所有来源记录
            if final:
                if not len(res):
                    return '期间交易市场为：无交易;\n'
                if len(res) == 1:
                    market = list(res.keys())[0]
                    return f'期间交易市场为：{market};\n'
            for market, base_quote_dict in res.items():
                base, quote = base_quote_dict['base'], base_quote_dict['quote']
                if _is_privacy_asset(_asset=base):
                    return f'交易了隐私币: {base};\n'
                if _is_privacy_asset(_asset=quote):
                    return f'交易了隐私币: {quote};\n'
            return

        def _get_exchange_data():
            model = AssetExchangeOrder
            rows = model.query.with_entities(
                model.source_asset,
                model.target_asset,
            ).filter(
                model.user_id == user_id,
                model.created_at >= query_start,
                model.created_at < withdrawal_at,
            ).all()
            _ret = defaultdict(dict)
            for row in rows:
                _ret[f'{row.source_asset}{row.target_asset}'].update({
                    'base': row.source_asset,
                    'quote': row.target_asset,
                })
            return _ret

        def _get_stop_history():
            start_ts = datetime_to_time(query_start)
            end_ts = datetime_to_time(withdrawal_at)
            cond = f' create_time>={start_ts} AND create_time<{end_ts}'
            rows = TradeHistoryDB.get_users_history(
                [user_id],
                fields=['market', ],
                cond=cond,
                table_type='stop_history'
            )
            _ret = defaultdict(dict)
            for row in rows:
                market = row['market']
                market_dict = _get_market_dict(market)
                if not market_dict:
                    continue
                _ret[f'{market}'].update({
                    'base': market_dict['base_asset'],
                    'quote': market_dict['quote_asset'],
                })
            return _ret

        def _get_order_history():
            start_ts = datetime_to_time(query_start)
            end_ts = datetime_to_time(withdrawal_at)
            cond = f' create_time>={start_ts} AND create_time<{end_ts}'
            rows = TradeHistoryDB.get_users_history(
                [user_id],
                fields=['market', ],
                cond=cond,
                table_type='order_history'
            )
            _ret = defaultdict(dict)
            for row in rows:
                market = row['market']
                market_dict = _get_market_dict(market)
                if not market_dict:
                    continue
                _ret[f'{market}'].update({
                    'base': market_dict['base_asset'],
                    'quote': market_dict['quote_asset'],
                })
            return _ret

        def _get_pending_stop_orders():
            start_ts = datetime_to_time(query_start)
            end_ts = datetime_to_time(withdrawal_at)
            _ret = defaultdict(dict)
            client = ServerClient()
            page = 1
            while True:
                orders = client.user_pending_stop_orders(user_id, page=page, limit=100)
                for order in orders:  # 降序
                    ctime = int(order['ctime'])
                    if ctime < start_ts:
                        break
                    if start_ts <= ctime < end_ts:
                        market = order['market']
                        market_dict = _get_market_dict(market)
                        if not market_dict:
                            continue
                        _ret[f'{market}'].update({
                            'base': market_dict['base_asset'],
                            'quote': market_dict['quote_asset'],
                        })
                if not orders.has_next:
                    break
                page += 1
            return _ret

        def _get_pending_orders():
            start_ts = datetime_to_time(query_start)
            end_ts = datetime_to_time(withdrawal_at)
            _ret = defaultdict(dict)
            client = ServerClient()
            page = 1
            while True:
                orders = client.user_pending_orders(user_id, page=page, limit=100)
                for order in orders:  # 降序
                    ctime = int(order['ctime'])
                    if ctime < start_ts:
                        break
                    if start_ts <= ctime < end_ts:
                        market = order['market']
                        market_dict = _get_market_dict(market)
                        if not market_dict:
                            continue
                        _ret[f'{market}'].update({
                            'base': market_dict['base_asset'],
                            'quote': market_dict['quote_asset'],
                        })
                if not orders.has_next:
                    break
                page += 1
            return _ret

        def _get_market_dict(_market):
            if _market not in market_dicts:
                market_dict = MarketCache(_market).dict
                market_dicts[_market] = market_dict
            return market_dicts[_market]

        market_dicts = {}
        exchange_ret = _get_exchange_data()
        _desc = _check_one_by_one(exchange_ret)
        if _desc:
            return _desc
        stop_history_ret = _get_stop_history()
        _desc = _check_one_by_one(stop_history_ret)
        if _desc:
            return _desc
        order_history_ret = _get_order_history()
        _desc = _check_one_by_one(order_history_ret)
        if _desc:
            return _desc
        pending_stop_ret = _get_pending_stop_orders()
        _desc = _check_one_by_one(pending_stop_ret)
        if _desc:
            return _desc
        pending_order_ret = _get_pending_orders()
        _desc = _check_one_by_one(pending_order_ret)
        if _desc:
            return _desc
        ret = defaultdict(dict)
        for market, base_quote in exchange_ret.items():
            ret[market].update(base_quote)  # 重写
        for market, base_quote in stop_history_ret.items():
            ret[market].update(base_quote)
        for market, base_quote in order_history_ret.items():
            ret[market].update(base_quote)
        for market, base_quote in pending_stop_ret.items():
            ret[market].update(base_quote)
        for market, base_quote in pending_order_ret.items():
            ret[market].update(base_quote)
        _desc = _check_one_by_one(ret, final=True)
        return _desc

    def _proportion_of_withdrawal_gt_account():
        from app.business.account_pl import RealtimeAccountProfitLossProcessor, TOTAL_ACCOUNT

        _hours, _threshold = conf[f"{prefix}ic_accumulated_ratio_threshold"][0]
        et = withdrawal_at
        st = et - timedelta(hours=float(_hours))
        model = Withdrawal
        rows = model.query.with_entities(
            model.asset,
            func.sum(model.amount)
        ).filter(
            model.user_id == user_id,
            model.status.in_([
                model.Status.FINISHED,
                model.Status.CONFIRMING,
                model.Status.PROCESSING,
            ]),
            model.created_at >= st,
            model.created_at < et,
        ).group_by(
            model.asset
        ).all()
        asset_prices = PriceManager.assets_to_usd()
        _withdrawal_usd = Decimal()
        for (_asset, _amount) in rows:
            _price = asset_prices.get(_asset, 0)
            _withdrawal_usd += _amount * _price
        _position_usd = RealtimeAccountProfitLossProcessor.get_real_time_account_type_usd(
            user_id,
            TOTAL_ACCOUNT,
            False)
        cur_ratio = _withdrawal_usd / _position_usd if _position_usd else 0
        cur_ratio = quantize_amount(cur_ratio, 4)
        if cur_ratio > Decimal(_threshold):
            return f'近{_hours}小时累计提现市值大于账户资产的占比:{format_percent(cur_ratio, 2)} > {format_percent(_threshold, 2)}'
        return

    def _range_position_usd_result():
        """
        user_account_type_balance_sum_table
        当前此表快照改为 60天，所以最大支持天数为 60
        """

        def _get_slice_balance_usds():
            timestamp = int(withdrawal_at.timestamp())
            timestamp = timestamp - timestamp % (24 * 3600)
            begin_ts = timestamp - 86400 * days
            cur_ts = begin_ts + 86400
            table_func = ExchangeLogDB.user_account_type_balance_sum_table
            sub_user_ids = SubAccount.query.filter(SubAccount.main_user_id == user_id).with_entities(
                SubAccount.user_id).all()
            sub_user_ids = [x for x, in sub_user_ids]
            user_ids = [user_id, ] + sub_user_ids
            user_id_str = ','.join(map(str, user_ids))
            where = f' user_id in ({user_id_str})'
            ret = []
            while cur_ts <= timestamp:
                table = table_func(cur_ts)
                if not table.exists():
                    cur_ts += 86400
                    continue
                rows = table.select(
                    'SUM(`balance_usd`)',
                    where=where
                )
                for balance_usd, in rows:
                    if balance_usd:
                        ret.append(balance_usd)
                cur_ts += 86400
            return ret

        days, _threshold, min_days = conf[f"{prefix}ic_position_usd_threshold"][0]
        days, _threshold, min_days = int(days), Decimal(_threshold), int(min_days)
        balances = _get_slice_balance_usds()
        satisfied_days = sum([1 for balance in balances if balance > _threshold])
        if satisfied_days < min_days:
            return f'过去{days}天，账户持仓市值大于 {_threshold} USD的天数少于{min_days}天'
        return

    def _trade_assets_lt_count():
        from app.common.constants import BalanceBusiness

        def _get_trade_asset_by_summary():
            withdrawal_date = withdrawal_at.date()
            query_date = withdrawal_date - timedelta(days=days)
            rows = TradeSummaryDB.get_user_trade_summary_data(
                query_date,
                withdrawal_date + timedelta(days=1),
                asset_name=None,
                user_ids=[user_id],
                columns=('stock_asset',)
            )
            return {row['stock_asset'] for row in rows}

        def _get_last_hour_trade_asset():
            end_time = int(withdrawal_at.timestamp())
            start_time = end_time - 60 * 60
            client = ServerClient()
            page = 1
            assets = set()
            while True:
                histories = client.get_user_balance_history(
                    user_id,
                    '',
                    account_id=0,
                    business=BalanceBusiness.TRADING,
                    start_time=start_time,
                    end_time=end_time,
                    page=page,
                    limit=100
                )
                for history in histories:
                    assets.add(history['asset'])
                if not histories.has_next:
                    break
                page += 1
            return assets

        days, _threshold = conf[f"{prefix}ic_trade_assets_threshold"][0]
        days, _threshold = int(days), int(_threshold)
        trade_assets = _get_trade_asset_by_summary()
        trade_assets |= _get_last_hour_trade_asset()
        if len(trade_assets) < _threshold:
            return f'{days}天内交易币种少于{_threshold}个'
        return

    def _get_last_deposit_usd():
        model = Deposit
        row = model.query.with_entities(
            model.asset,
            model.amount,
        ).filter(
            model.user_id == user_id,
            model.status.in_([
                model.Status.FINISHED,
                model.Status.CONFIRMING,
                model.Status.TO_HOT,
            ]),
            model.created_at >= query_start,
            model.created_at < withdrawal_at,
        ).order_by(
            model.id.desc()
        ).first()
        if not row:
            return
        _price = PriceManager.asset_to_usd(row.asset)
        return _price * row.amount

    query_start = withdrawal_at - timedelta(minutes=conf[f'{prefix}ic_delta_minutes'])
    if not has_asset_input(user_id, query_start, withdrawal_at):
        return True, ''
    if last_deposit_usd := _get_last_deposit_usd():
        if last_deposit_usd <= conf[f'{prefix}ic_deposit_monitor_threshold']:
            return True, ''
    bus_name = check_bus.value
    desc = f'提现{amount_to_str(withdrawal_usd, 2)}≥{conf[f"{prefix}ic_monitor_threshold"]}风控阈值；\n'
    desc += f'{bus_name}时间距离上一次充值时间在{conf[f"{prefix}ic_delta_minutes"]}分钟内；\n'
    desc += f'单笔充值订单市值{amount_to_str(last_deposit_usd, 2)}≥{conf[f"{prefix}ic_deposit_monitor_threshold"]}风控阈值；\n'
    if _is_privacy_asset(asset):
        desc += f'{bus_name}币种为隐私币: {asset};\n'
        desc += f'本次{bus_name}金额{amount_to_str(withdrawal_usd, 2)}USD;\n'
        return False, desc

    if _desc_str := _trade_result():
        desc += _desc_str
        return False, desc
    if _desc_str := _proportion_of_withdrawal_gt_account():
        desc += _desc_str
        return False, desc
    if _desc_str := _range_position_usd_result():
        desc += _desc_str
        return False, desc
    if _desc_str := _trade_assets_lt_count():
        desc += _desc_str
        return False, desc
    return True, ''


def check_user_new_user_immediately_withdrawal(
        user_id: int, asset, amount, withdrawal_at, business: RiskUser.Reason, check_bus):
    """
    检查新注册即 提现/p2p卖出
    user_id: 主账户id
    """
    if not asset:
        return
    if business != RiskUser.Reason.NEW_USER_IMMEDIATELY_WITHDRAWAL:
        return
    current_app.logger.info('check_user_new_user_immediately_withdrawal: %s', user_id)
    ok, err = _check_user_new_user_immediately_withdrawal(asset, amount, withdrawal_at, user_id, check_bus)
    if not ok:
        permissions = RiskUser.PermissionMap[business]
        add_risk_user(user_id, business, err, permissions=permissions, source=asset)


def _check_user_new_user_immediately_withdrawal(
        asset, amount, withdrawal_at, user_id: int, check_bus: UserCheckRequest.CheckBusiness
) -> Tuple[bool, str]:
    if NewUserImmediatelyWithdrawalCheckCondition.check(user_id):
        return True, ''
    if not has_asset_input(user_id):
        # 如果所有充值都来自矿池转账，则不触发新注册即提现风控
        return True, ''
    price = PriceManager.asset_to_usd(asset)
    withdrawal_usd = price * amount
    user = User.query.get(user_id)
    user_created_at = user.created_at
    delta = withdrawal_at - user_created_at
    if user.kyc_status is User.KYCStatus.PASSED:
        prefix = ''
        conf = RiskControlGroupConfig().new_user_immediately_charged
    else:
        conf = RiskControlGroupConfig().new_user_immediately_charged_no_kyc
        prefix = 'no_kyc_'
    ok = True
    desc = ''
    bus_name = check_bus.value
    for hours, amount_usd in conf[f'{prefix}nuic_hour_amount_threshold']:
        amount_usd = Decimal(amount_usd)
        if delta <= timedelta(hours=float(hours)) and withdrawal_usd >= amount_usd:
            ok = False
            desc = f'{bus_name}时间距离注册时间在{hours}小时内，本次{bus_name}金额{amount_to_str(withdrawal_usd, 2)}USD；'
            break
    return ok, desc


def check_user_withdrawal_no_on_chain_deposit(asset, amount, user_id: int, business: RiskUser.Reason, check_bus):
    """
    检查 提现/p2p卖出 无链上充值记录
    user_id: 主账户id
    """
    if not asset:
        return
    if business != RiskUser.Reason.WITHDRAWAL_NO_ON_CHAIN_DEPOSIT:
        return
    current_app.logger.info('check_user_withdrawal_no_on_chain_deposit: %s', user_id)
    ok, err = _check_user_withdrawal_no_on_chain_deposit(asset, amount, user_id, check_bus)
    if not ok:
        permissions = RiskUser.PermissionMap[business]
        add_risk_user(user_id, business, err, permissions=permissions, source=asset)


def _check_user_withdrawal_no_on_chain_deposit(
        asset, amount, user_id: int, check_bus: UserCheckRequest.CheckBusiness) -> Tuple[bool, str]:
    if WithdrawalNoOnChainDepositCheckCondition.check(user_id):
        return True, ''
    user = User.query.get(user_id)
    if user.kyc_status is User.KYCStatus.PASSED:
        prefix = ''
        conf = RiskControlGroupConfig().withdrawal_no_deposit
    else:
        conf = RiskControlGroupConfig().withdrawal_no_deposit_no_kyc
        prefix = 'no_kyc_'
    price = PriceManager.asset_to_usd(asset)
    withdrawal_usd = price * amount
    if withdrawal_usd < conf[f'{prefix}wnd_monitor_threshold']:
        return True, ''

    def _get_price(date_, asset_):
        if date_ not in date_prices:
            prices = AssetPrice.get_close_price_map(date_)
            date_prices[date_] = prices
        return date_prices[date_].get(asset_, 0)

    def _get_on_chain_deposit_usd():
        model = Deposit
        rows = model.query.with_entities(
            model.asset,
            model.amount,
            model.created_at,
        ).filter(
            model.user_id == user_id,
            model.status.in_([
                model.Status.CONFIRMING,
                model.Status.FINISHED,
                model.Status.TO_HOT,
            ])
        ).all()
        amount_usd = Decimal()
        for row in rows:
            date_ = row.created_at.date()
            amount_usd += _get_price(date_, row.asset) * row.amount
        
        model = AbnormalDepositApplication
        abnormal_usd = model.query.filter(
            model.user_id == user_id,
            model.status.in_((
                model.Status.PROCESSING,
                model.Status.ASSET_CHANGE_PROCESSING,
                model.Status.ASSET_CHANGE_DEDUCTED,
                model.Status.FINISHED
            )),
        ).with_entities(func.sum(model.total_usd)).scalar() or 0
        amount_usd += abnormal_usd
        return quantize_amount(amount_usd, PrecisionEnum.CASH_PLACES)

    def get_p2p_side_usd(side):
        model = P2pOrder
        if side == P2pBusinessType.BUY:
            query = model.get_user_buy_query(user_id)
        else:
            query = model.get_user_sell_query(user_id)
        rows = query.with_entities(
            model.base, model.base_amount, model.created_at
        )
        amount_usd = Decimal()
        for row in rows:
            date_ = row.created_at.date()
            amount_usd += _get_price(date_, row.base) * row.base_amount
        return amount_usd

    def _get_p2p_usd() -> Tuple[Decimal, Decimal]:
        buy_usd = get_p2p_side_usd(P2pBusinessType.BUY)
        sell_usd = get_p2p_side_usd(P2pBusinessType.SELL)
        return buy_usd, sell_usd

    date_prices = {}
    deposit_usd = _get_on_chain_deposit_usd()
    p2p_buy_usd, p2p_sell_usd = _get_p2p_usd()
    bus_name = check_bus.value
    if deposit_usd or p2p_buy_usd:
        calc_threshold = withdrawal_usd * conf[f'{prefix}wnd_accumulated_deposit_withdrawal_ratio']
        if deposit_usd + p2p_buy_usd < calc_threshold + p2p_sell_usd:
            ratio = format_percent(conf[f'{prefix}wnd_accumulated_deposit_withdrawal_ratio'])
            desc = (f'累计链上充值市值+P2P的买入总金额＜本次提现市值*{ratio}+P2P卖出总金额；\n'
                    f'累计链上充值市值{amount_to_str(deposit_usd, 2)}USD，'
                    f'P2P的买入总金额为{amount_to_str(p2p_buy_usd, 2)}USD，'
                    f'P2P卖出总金额为{amount_to_str(p2p_sell_usd, 2)}USD，'
                    f'本次{bus_name}金额{amount_to_str(withdrawal_usd, 2)}USD；')
            return False, desc
    if not has_asset_input(user_id, only_chain=True, with_pool_deposit=True):
        desc = f'该用户无链上充值记录也无P2P买入订单，本次{bus_name}金额{amount_to_str(withdrawal_usd, 2)}USD；'
        return False, desc
    return True, ''


class BalanceManager:

    def __init__(self, user_id: int, sub_user_ids: List[int], asset_prices: Dict[str, Decimal]):
        self.user_id = user_id
        self.asset_prices = asset_prices
        self.all_user_ids = [user_id] + sub_user_ids

    def assets_to_usd(self, assets: Dict[str, Decimal]) -> Decimal:
        amount = sum(self.asset_prices.get(k, 0) * v for k, v in assets.items())
        return quantize_amount(amount, 8)

    def get_snapshot_balance_usd(self, timestamp: int) -> Tuple[Decimal, int]:
        """
        现货+理财+杠杆+合约+AMM-借币+预测市场(质押-发行)
        """
        spot_time, perpetual_time, user_balance_time = self._find_snapshot_time(timestamp)
        user_ids = ','.join(str(x) for x in self.all_user_ids)

        spot_assets = TradeLogDB.slice_balance_table(spot_time).select(
            'asset', 'SUM(`balance`)',
            where=f'user_id in ({user_ids})',
            group_by='`asset`')
        # 包含账户权益
        perpetual_assets = PerpetualLogDB.slice_balance_table(perpetual_time).select(
            'asset', 'SUM(`balance`)',
            where=f'user_id in ({user_ids})',
            group_by='`asset`'
        )
        amm_assets = ExchangeLogDB.user_account_balance_table(user_balance_time).select(
            'asset', 'SUM(`balance`)',
            where=f'account_type="{AccountBalanceType.AMM.name}" AND user_id in ({user_ids})',
            group_by='`asset`'
        )
        loan_assets = ExchangeLogDB.user_balance_table(user_balance_time).select(
            'asset', 'SUM(margin_due_amount)',
            where=f'user_id in ({user_ids})',
            group_by='asset'
        )
        pledge_loan_assets = ExchangeLogDB.user_pledge_table(user_balance_time).select(
            'asset', 'SUM(`total_unflat_amount`)',
            where=f'user_id={self.user_id}',  # 质押只允许主账号
            group_by="asset",
        )
        pre_trading_table = ExchangeLogDB.user_pre_trading_table(user_balance_time)
        if pre_trading_table.exists():
            pre_trading_rows = ExchangeLogDB.user_pre_trading_table(user_balance_time).select(
                'issue_asset', 'SUM(issue_amount)', 'pledge_asset', 'SUM(pledge_amount)',
                where=f'user_id={self.user_id}',  # 预测市场只允许主账号
                group_by='issue_asset,pledge_asset',
            )
        else:
            pre_trading_rows = []

        assets = defaultdict(Decimal)

        for k, v in spot_assets:
            assets[k] += Decimal(v)
        for k, v in perpetual_assets:
            assets[k] += Decimal(v)
        for k, v in amm_assets:
            assets[k] += Decimal(v)
        for k, v in loan_assets:
            assets[k] -= Decimal(v)
        for k, v in pledge_loan_assets:
            assets[k] -= Decimal(v)
        for issue_asset, issue_amount, pledge_asset, pledge_amount in pre_trading_rows:
            assets[issue_asset] -= Decimal(issue_amount)
            assets[pledge_asset] += Decimal(pledge_amount)

        return self.assets_to_usd(assets), user_balance_time

    def _find_snapshot_time(self, timestamp: int, depth=0):
        if depth > 7:
            raise RuntimeError("failed to get snapshot balance")

        spot_time = TradeLogDB.get_slice_history_timestamp(timestamp)
        if not spot_time or abs(spot_time - timestamp) > 60:
            return self._find_snapshot_time(timestamp - 86400, depth + 1)

        perpetual_time = PerpetualLogDB.get_slice_history_timestamp(timestamp)
        if not perpetual_time or abs(perpetual_time - timestamp) > 60:
            return self._find_snapshot_time(timestamp - 86400, depth + 1)
        # user_balance_xx表时间是utc0点
        table = ExchangeLogDB.user_balance_table(timestamp)
        if not table.exists():
            return self._find_snapshot_time(timestamp - 86400, depth + 1)

        table = ExchangeLogDB.user_account_balance_table(timestamp)
        if not table.exists():
            return self._find_snapshot_time(timestamp - 86400, depth + 1)

        return spot_time, perpetual_time, timestamp

    def get_current_balance_usd(self) -> Decimal:
        """获取当前所有账户资产(含子账户)"""
        amount = Decimal()
        for user_id in self.all_user_ids:
            amount += self._get_total_asset_usd(user_id)
        amount -= self._get_loan_asset_usd(self.all_user_ids)
        amount += self._get_pre_asset_pledge_remain_usd(self.user_id)
        return amount

    def _get_total_asset_usd(self, user_id: int) -> Decimal:
        """现货(币币+杠杆+理财) + 合约 + AMM"""
        merge_assets = defaultdict(Decimal)
        result = ServerClient().get_user_accounts_balances(user_id)
        for _, assets in result.items():
            for asset, values in assets.items():
                merge_assets[asset] += values['available']
                merge_assets[asset] += values['frozen']

        perpetual_client = PerpetualServerClient()
        result = perpetual_client.get_user_balances(user_id)
        for asset, values in result.items():
            merge_assets[asset] += values['available']
            merge_assets[asset] += values['frozen']
            merge_assets[asset] += values['margin']
        # 未实现盈亏
        positions = perpetual_client.position_pending(user_id)
        for position in positions:
            asset = PerpetualMarketCache().get_balance_asset(position['market'])
            merge_assets[asset] += Decimal(position['profit_unreal'])

        result = get_user_amm_assets(user_id)
        for asset, amount in result.items():
            merge_assets[asset] += amount

        return self.assets_to_usd(merge_assets)

    def _get_loan_asset_usd(self, user_ids: List[int]) -> Decimal:
        """杠杆、借贷"""
        from app.business.pledge.helper import get_user_pledge_unflat_amount_dict
        # sql:18s
        loans = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id.in_(user_ids),
            MarginLoanOrder.status == MarginLoanOrder.StatusType.PASS
        ).with_entities(
            MarginLoanOrder.asset,
            func.sum(MarginLoanOrder.unflat_amount)
        ).group_by(
            MarginLoanOrder.asset
        ).all()
        loans = {x: y for x, y in loans}
        pledge_unflat_dict = get_user_pledge_unflat_amount_dict(self.user_id)  # 主账号
        return self.assets_to_usd(loans) + self.assets_to_usd(pledge_unflat_dict)

    def _get_pre_asset_pledge_remain_usd(self, user_id: int) -> Decimal:
        # 返回 `质押资产的USD - 已发行token的USD`
        rows = PreTradingUserAsset.query.filter(
            PreTradingUserAsset.user_id == user_id,
            PreTradingUserAsset.status == PreTradingUserAsset.Status.PLEDGE,
        ).with_entities(
            PreTradingUserAsset.asset,
            PreTradingUserAsset.issue_amount,
            PreTradingUserAsset.pledge_asset,
            PreTradingUserAsset.pledge_amount,
        ).all()
        result = defaultdict(Decimal)
        for v in rows:
            result[v.asset] -= v.issue_amount
            result[v.pledge_asset] += v.pledge_amount
        return self.assets_to_usd(result)


class BalanceChangManager:

    def __init__(self, user_id: int, start_time: datetime, end_time: datetime,
                 sub_user_ids: List[int], asset_prices: Dict[str, Decimal]):
        self.user_id = user_id
        self.start_time = start_time
        self.end_time = end_time
        self.asset_prices = asset_prices
        self.all_user_ids = [user_id] + sub_user_ids

    def assets_to_usd(self, assets: Dict[str, Decimal]) -> Decimal:
        amount = sum(self.asset_prices.get(k, 0) * v for k, v in assets.items())
        return quantize_amount(amount, 8)

    def get_deposit_usd(self) -> Decimal:
        deposits = Deposit.query.filter(
            Deposit.user_id == self.user_id,
            Deposit.confirmed_at >= self.start_time,
            Deposit.confirmed_at < self.end_time,
            Deposit.status.in_((
                Deposit.Status.CONFIRMING,
                Deposit.Status.FINISHED,
                Deposit.Status.TO_HOT
            ))
        ).with_entities(
            Deposit.asset,
            func.sum(Deposit.amount)
        ).group_by(
            Deposit.asset
        ).all()

        return self.assets_to_usd(dict(deposits))

    def get_cancelled_withdrawal_usd(self) -> Decimal:
        cancelled_withdrawals = Withdrawal.query.filter(
            Withdrawal.user_id == self.user_id,
            Withdrawal.status == Withdrawal.Status.CANCELLED,
            Withdrawal.updated_at >= self.start_time,
            Withdrawal.updated_at < self.end_time,
            Withdrawal.approved_by_user_at.is_not(None)
        ).with_entities(
            Withdrawal.asset,
            func.sum(Withdrawal.amount)
        ).group_by(
            Withdrawal.asset
        ).all()
        return self.assets_to_usd(dict(cancelled_withdrawals))

    @classmethod
    def query_withdrawal_assets(cls, user_id: int, start_time:datetime = None, end_time: datetime = None) -> Dict[str, Decimal]:
        query = Withdrawal.query.filter(
            Withdrawal.user_id == user_id,
            Withdrawal.status.in_((
                Withdrawal.Status.AUDIT_REQUIRED,
                Withdrawal.Status.AUDITED,
                Withdrawal.Status.PROCESSING,
                Withdrawal.Status.CONFIRMING,
                Withdrawal.Status.FINISHED
            ))
        ).with_entities(
            Withdrawal.asset,
            func.sum(Withdrawal.amount)
        ).group_by(
            Withdrawal.asset
        )
        if start_time:
            query = query.filter(Withdrawal.approved_by_user_at >= start_time)
        if end_time:
            query = query.filter(Withdrawal.approved_by_user_at < end_time)
        return dict(query.all())

    def get_withdrawal_usd(self) -> Decimal:
        withdrawals = self.query_withdrawal_assets(self.user_id, self.start_time, self.end_time)
        return self.assets_to_usd(dict(withdrawals))

    def _get_onchain_buy_or_sell_usd(self, side: OrderSide) -> Decimal:
        order_map = defaultdict(Decimal)
        for order in OnchainOrder.query.filter(
                OnchainOrder.user_id == self.user_id,
                OnchainOrder.side == side,
                OnchainOrder.finished_at >= self.start_time,
                OnchainOrder.finished_at < self.end_time,
                OnchainOrder.status == OnchainOrder.Status.FINISHED,
        ):
            order_map[order.money_asset] += Decimal(order.money_amount)

        return self.assets_to_usd(order_map)

    def get_onchain_buy_usd(self) -> Decimal:
        return self._get_onchain_buy_or_sell_usd(OrderSide.BUY)

    def get_onchain_sell_usd(self) -> Decimal:
        return self._get_onchain_buy_or_sell_usd(OrderSide.SELL)

    def get_onchain_asset_to_spot_usd(self) -> Decimal:
        history_map = defaultdict(Decimal)
        for history in OnchainAssetToSpotHistory.query.filter(
                OnchainAssetToSpotHistory.user_id == self.user_id,
                OnchainAssetToSpotHistory.created_at >= self.start_time,
                OnchainAssetToSpotHistory.created_at < self.end_time,
                OnchainAssetToSpotHistory.status == OnchainAssetToSpotHistory.Status.FINISHED,
        ):
            history_map[history.asset] += Decimal(history.amount)

        return self.assets_to_usd(history_map)

    def get_payment_pay_usd(self) -> Decimal:
        """获取收付款业务的付款金额（付款统计在“站内提现金额”范围内）"""
        pay_rows = PaymentTransaction.query.filter(
            PaymentTransaction.payer_id == self.user_id,
            PaymentTransaction.created_at >= self.start_time,
            PaymentTransaction.created_at < self.end_time,
            PaymentTransaction.status.in_(
                (
                    PaymentTransaction.Status.PROCESSING,
                    PaymentTransaction.Status.FINISHED,
                )
            )
        ).with_entities(
            PaymentTransaction.pay_asset,
            func.sum(PaymentTransaction.pay_amount)
        ).group_by(
            PaymentTransaction.pay_asset,
        ).all()
        return self.assets_to_usd(dict(pay_rows))

    def get_payment_receive_usd(self) -> Decimal:
        """获取收付款业务的收款金额（收款统计在“站内充值金额”范围内）"""
        receive_rows = PaymentTransaction.query.filter(
            PaymentTransaction.receiver_id == self.user_id,
            PaymentTransaction.finished_at >= self.start_time,
            PaymentTransaction.finished_at < self.end_time,
            PaymentTransaction.status == PaymentTransaction.Status.FINISHED,
        ).with_entities(
            PaymentTransaction.receive_asset,
            func.sum(PaymentTransaction.receive_amount)
        ).group_by(
            PaymentTransaction.receive_asset,
        ).all()
        return self.assets_to_usd(dict(receive_rows))

    def get_audit_required_withdrawal_usd(self) -> Decimal:
        withdrawals = Withdrawal.query.filter(
            Withdrawal.user_id == self.user_id,
            Withdrawal.status == Withdrawal.Status.AUDIT_REQUIRED,
        ).with_entities(
            Withdrawal.asset,
            func.sum(Withdrawal.amount),
        ).group_by(
            Withdrawal.asset,
        ).all()
        return self.assets_to_usd(dict(withdrawals))

    def get_gift_usd(self) -> Decimal:
        gifts = GiftHistory.query.filter(
            GiftHistory.user_id.in_(self.all_user_ids),
            GiftHistory.created_at >= self.start_time,
            GiftHistory.created_at < self.end_time,
            GiftHistory.status == GiftHistory.Status.FINISHED
        ).with_entities(
            GiftHistory.asset,
            func.sum(GiftHistory.amount)
        ).group_by(
            GiftHistory.asset
        ).all()
        return self.assets_to_usd(dict(gifts))

    def get_asset_change_usd(self) -> Decimal:
        changes = UpdateAssetBalance.query.filter(
            UpdateAssetBalance.user_id.in_(self.all_user_ids),
            UpdateAssetBalance.updated_at >= self.start_time,
            UpdateAssetBalance.updated_at < self.end_time,
            UpdateAssetBalance.status == UpdateAssetBalance.Status.FINISHED
        ).with_entities(
            UpdateAssetBalance.asset,
            func.sum(UpdateAssetBalance.amount)
        ).group_by(
            UpdateAssetBalance.asset
        ).all()
        return self.assets_to_usd(dict(changes))

    def get_red_packet_usd(self) -> Decimal:
        sends = RedPacket.query.filter(
            RedPacket.user_id == self.user_id,
            RedPacket.effective_at >= self.start_time,
            RedPacket.effective_at < self.end_time
        ).with_entities(
            RedPacket.asset,
            func.sum(RedPacket.total_amount)
        ).group_by(
            RedPacket.asset
        ).all()

        returns = RedPacketReturnHistory.query.join(RedPacket).filter(
            RedPacket.user_id == self.user_id,
            RedPacketReturnHistory.created_at >= self.start_time,
            RedPacketReturnHistory.created_at < self.end_time,
            RedPacketReturnHistory.status == RedPacketReturnHistory.Status.FINISHED
        ).with_entities(
            RedPacketReturnHistory.asset,
            func.sum(RedPacketReturnHistory.return_amount)
        ).group_by(
            RedPacketReturnHistory.asset
        ).all()

        grabs = RedPacketHistory.query.filter(
            RedPacketHistory.user_id == self.user_id,
            RedPacketHistory.effective_at >= self.start_time,
            RedPacketHistory.effective_at < self.end_time
        ).with_entities(
            RedPacketHistory.asset,
            func.sum(RedPacketHistory.amount)
        ).group_by(
            RedPacketHistory.asset
        ).all()

        return self.assets_to_usd(dict(returns)) + self. assets_to_usd(dict(grabs)) - self.assets_to_usd(dict(sends))

    def get_salary_pay_usd(self) -> Decimal:
        salary_pay = SalaryPay.query.filter(
            SalaryPay.user_id.in_(self.all_user_ids),
            SalaryPay.updated_at >= self.start_time,
            SalaryPay.updated_at < self.end_time,
            SalaryPay.status == SalaryPay.Status.FINISHED,
        ).with_entities(
            SalaryPay.asset,
            func.sum(SalaryPay.amount)
        ).group_by(
            SalaryPay.asset
        ).all()

        return self.assets_to_usd(dict(salary_pay))

    def get_commission_pay_usd(self) -> Decimal:
        rows = CommissionPay.query.filter(
            CommissionPay.user_id.in_(self.all_user_ids),
            CommissionPay.status == CommissionPay.Status.FINISHED,
            CommissionPay.finished_at >= self.start_time,
            CommissionPay.finished_at < self.end_time,
        ).group_by(
            CommissionPay.asset,
        ).with_entities(
            CommissionPay.asset,
            func.sum(CommissionPay.amount)
        ).all()
        return self.assets_to_usd(dict(rows))

    def get_abnormal_deposit_application_usd(self) -> Tuple[Decimal, Decimal]:
        # 充值找回资产变更：1. 50U的手续费(扣，这个算资产变更) ； 2.【充错链，可入账】的 资产变更(增加，这个算充值)
        deposit_rows = (
            AbnormalDepositApplicationTransferHistory.query.filter(
                AbnormalDepositApplicationTransferHistory.to_user_id == self.user_id,
                AbnormalDepositApplicationTransferHistory.finished_at >= self.start_time,
                AbnormalDepositApplicationTransferHistory.finished_at < self.end_time,
            )
            .with_entities(
                AbnormalDepositApplicationTransferHistory.asset,
                func.sum(AbnormalDepositApplicationTransferHistory.amount),
            )
            .group_by(AbnormalDepositApplicationTransferHistory.asset)
            .all()
        )

        change_rows = (
            AbnormalDepositApplicationTransferHistory.query.filter(
                AbnormalDepositApplicationTransferHistory.from_user_id == self.user_id,
                AbnormalDepositApplicationTransferHistory.deducted_at >= self.start_time,
                AbnormalDepositApplicationTransferHistory.deducted_at < self.end_time,
            )
            .with_entities(
                AbnormalDepositApplicationTransferHistory.asset,
                func.sum(AbnormalDepositApplicationTransferHistory.amount),
            )
            .group_by(AbnormalDepositApplicationTransferHistory.asset)
            .all()
        )

        deposit_usd = self.assets_to_usd(dict(deposit_rows))
        change_usd = -self.assets_to_usd(dict(change_rows))  # AbnormalDepositApplicationTransferHistory.amount大于0
        return deposit_usd, change_usd

    def get_p2p_usd(self) -> tuple[Decimal, Decimal]:
        """P2P入账金额-P2P出账金额"""
        model = P2pOrderTransHistory
        to_histories = model.query.filter(
            model.to_user_id == self.user_id,
            model.updated_at >= self.start_time,
            model.updated_at < self.end_time,
            model.status == model.Status.FINISH
        ).group_by(
            model.asset
        ).with_entities(
            model.asset,
            func.sum(model.to_amount)
        ).all()
        from_histories = model.query.filter(
            model.from_user_id == self.user_id,
            model.updated_at >= self.start_time,
            model.updated_at < self.end_time,
            model.status == model.Status.FINISH
        ).group_by(
            model.asset
        ).with_entities(
            model.asset,
            func.sum(model.from_amount)
        ).all()
        return self.assets_to_usd(dict(to_histories)), self.assets_to_usd(dict(from_histories))


class RiskCheckBase:
    CheckList = []
    Business: UserCheckRequest.Business

    @classmethod
    def gen_check_task(cls, row_id, user_id):
        rows = []
        for bus in cls.CheckList:
            context = dict(
                bus_id=row_id,
                business=cls.Business.name
            )
            record = UserCheckRequest(
                user_id=user_id,
                business=bus,
                context=json.dumps(context)
            )
            rows.append(record)
        db.session.add_all(rows)
        db.session.commit()
        for row in rows:
            execute_user_check_request_task.delay(row.id)

    @classmethod
    def need_add_risk_request(cls, user_id, asset, amount):
        """
        add user withdrawal check request. risk control system will check the user later.
        """
        user = User.query.get(user_id)
        if user.is_sub_account:
            user_id = user.sub_account_ref.main_user_id
        # 白名单用户不检查
        row = WithdrawalWhitelistUser.query.filter(
            WithdrawalWhitelistUser.user_id == user_id,
            WithdrawalWhitelistUser.status == WithdrawalWhitelistUser.Status.VALID
        ).first()
        if row:
            return False
        # 小额提现检查
        conf = RiskControlGroupConfig().withdrawal_approve
        if (price := PriceManager.asset_to_usd(asset)) and (price * amount <= conf['withdrawal_approve_min_amount']):
            acc_amount = UserDailyWithdrawnAmountCache(user_id).get_withdrawn_amount()
            if acc_amount <= conf['withdrawal_approve_acc_amount']:
                return False
        return True

    @classmethod
    def check_bus_risk(cls, record, asset, amount, created_at):
        model = UserCheckRequest
        bus = record.business
        if record.status != model.Status.CREATED:
            return
        if bus == model.Business.ABNORMAL_PROFIT:
            check_user_abnormal_profit(record.user_id, RiskUser.Reason.ABNORMAL_PROFIT, asset)
        elif bus == model.Business.IMMEDIATELY_WITHDRAWAL:
            check_user_immediately_withdrawal(
                record.user_id, asset, amount, created_at, RiskUser.Reason.IMMEDIATELY_WITHDRAWAL, cls.Business)
        elif bus == model.Business.NEW_USER_IMMEDIATELY_WITHDRAWAL:
            check_user_new_user_immediately_withdrawal(
                record.user_id, asset, amount, created_at, RiskUser.Reason.NEW_USER_IMMEDIATELY_WITHDRAWAL,
                cls.Business)
        elif bus == model.Business.WITHDRAWAL_NO_ON_CHAIN_DEPOSIT:
            check_user_withdrawal_no_on_chain_deposit(
                asset, amount, record.user_id, RiskUser.Reason.WITHDRAWAL_NO_ON_CHAIN_DEPOSIT, cls.Business)
        elif bus == model.Business.WITHDRAWAL_NO_DEPOSIT:
            # 当前已停用，无地方写 WITHDRAWAL_NO_DEPOSIT 记录
            check_user_withdrawal_no_deposit(record.user_id)
        else:
            current_app.logger.error(f"unkown check business {record.business}")
            return
        record.status = model.Status.COMPLETED
        db.session.commit()

    @classmethod
    def add_check_request(cls, row):
        raise NotImplementedError

    @classmethod
    def execute_risk_check(cls, bus_id, record: UserCheckRequest):
        raise NotImplementedError


class WithdrawalRiskCheck(RiskCheckBase):
    CheckList = UserCheckRequest.__WITHDRAWAL_CHECK_BUSINESSES__
    Business = UserCheckRequest.CheckBusiness.Withdrawal

    @classmethod
    def add_check_request(cls, w: Withdrawal):
        if cls.need_add_risk_request(w.user_id, w.asset, w.amount):
            cls.gen_check_task(w.id, w.user_id)

    @classmethod
    def execute_risk_check(cls, bus_id, record):
        w = Withdrawal.query.get(bus_id)
        cls.check_bus_risk(record, w.asset, w.amount, w.created_at)


class P2pSellRiskCheck(RiskCheckBase):
    CheckList = UserCheckRequest.__WITHDRAWAL_CHECK_BUSINESSES__
    Business = UserCheckRequest.CheckBusiness.P2p

    @classmethod
    def add_check_request(cls, p: P2pOrderTransHistory):
        user_id = p.from_user_id
        if cls.need_add_risk_request(user_id, p.asset, p.from_amount):
            P2pSellRiskCheck.gen_check_task(p.id, user_id)

    @classmethod
    def execute_risk_check(cls, bus_id, record):
        p = P2pOrderTransHistory.query.get(bus_id)
        cls.check_bus_risk(record, p.asset, p.from_amount, p.created_at)


class WithdrawalAuditHelper:
    """ 单笔提现记录维度的审核 """

    @classmethod
    def withdrawal_is_need_audit(cls, w: Withdrawal) -> bool:
        is_need_adr_bl_audit = cls.address_blacklist_need_audit(w)
        return is_need_adr_bl_audit

    @classmethod
    def address_blacklist_need_audit(cls, w: Withdrawal) -> bool:
        """ 提现地址黑名单 是否存在｜需要审核 """
        if not WithdrawalAddressBlacklist.is_blacklisted(chain=w.chain, address=w.address, memo=w.memo):
            return False

        row = WithdrawalAudit.query.filter(
            WithdrawalAudit.withdrawal_id == w.id,
            WithdrawalAudit.type == WithdrawalAudit.Type.WITHDRAWAL_ADDRESS_BLACKLISTED,
        ).first()
        if row:
            if row.status == WithdrawalAudit.Status.AUDITED:
                return False
            else:
                # 待审核
                return True

        row = WithdrawalAudit(
            withdrawal_id=w.id,
            status=WithdrawalAudit.Status.AUDIT_REQUIRED,
            type=WithdrawalAudit.Type.WITHDRAWAL_ADDRESS_BLACKLISTED,
        )
        db.session.add(row)
        # 增加风控用户：这笔提现会被卡住，用户后面新增了其他正常的提现也会因为权限被卡住
        add_risk_user(w.user_id, RiskUser.Reason.WITHDRAWAL_ADDRESS_BLACKLISTED, detail=str(w.id), source=str(w.asset))
        db.session.commit()
        return True

    @classmethod
    def cancel_withdrawal_audit(cls, w_id: int):
        """ 取消提现审核。不处理RiskUser的权限，需要在admin审核通过才行 """
        w_audits: list[WithdrawalAudit] = WithdrawalAudit.query.filter(
            WithdrawalAudit.withdrawal_id == w_id,
            WithdrawalAudit.status.not_in(
                [
                    WithdrawalAudit.Status.CANCELLED,
                    WithdrawalAudit.Status.AUDITED,
                ]
            ),
        ).all()
        for w_audit in w_audits:
            w_audit.status = WithdrawalAudit.Status.CANCELLED

    @classmethod
    def do_audit_address_blacklist_by_wid(
        cls,
        w_id: int,
        audit_status: WithdrawalAudit.Status,
        audited_by: int,
        audited_at: datetime,
        remark: str,
    ):
        """ 审核：提现地址黑名单 """
        w_audit: WithdrawalAudit = WithdrawalAudit.query.filter(
            WithdrawalAudit.withdrawal_id == w_id,
            WithdrawalAudit.type == WithdrawalAudit.Type.WITHDRAWAL_ADDRESS_BLACKLISTED,
        ).first()
        if not w_audit:
            return
        if w_audit.status == WithdrawalAudit.Status.CANCELLED:
            return
        if w_audit.status != audit_status:
            w_audit.status = audit_status
            w_audit.audited_by = audited_by
            w_audit.audited_at = audited_at
            w_audit.remark = remark
        return w_audit


@celery_task()
@lock_call(with_args=True)
def add_p2p_sell_risk_check_req(row_id):
    p = P2pOrderTransHistory.query.get(row_id)
    cache = P2pSellRiskReqRecord(p.id)
    if not cache.exists():
        P2pSellRiskCheck.add_check_request(p)
        cache.add_record()


class AccumulatedWithdrawalHelper:

    def __init__(self):
        self._date_asset_prices_map = {}

    def get_last_n_day_avg_data(self, days: int = 7, min_usd: Decimal = 1000) -> dict[str, dict]:
        """
        查询最近N日均值：
        （1）如果当天提现市值＜1000USD（为0也算低于1000刀）则不纳入统计；
        （2）计算7日均值时往前追溯数据，有符合要求（提现市值≥1000USD）的数据才算进去；
        （3）如果追溯到上币那天还没有7天符合要求的数据，则有几天数据则除几；
        （4）如果连1天数据都没有，则该币种无7日均值数据，不进行监控；
        """
        max_days = days * 2  # 最多往前追溯1倍的天数
        end_date = today()
        start_date = end_date - timedelta(days=max_days)
        start_time = date_to_datetime(start_date)
        end_time = date_to_datetime(end_date)

        rows = yield_query_records_by_time_range(
            table=Withdrawal, start_time=start_time, end_time=end_time,
            select_fields=(
                Withdrawal.id,
                Withdrawal.type,
                Withdrawal.user_id,
                Withdrawal.asset,
                Withdrawal.amount,
                Withdrawal.status,
                Withdrawal.created_at,
            ),
        )
        assets = self.get_assets()
        new_assets = self.get_new_assets(start_time)
        date_asset_data_map = defaultdict(lambda: defaultdict(lambda: {
            'count': Decimal(),
            'amount': Decimal(),
        }))
        whitelist = self.get_whitelist_users()
        for row in rows:  # rows id倒序
            if row.type == Withdrawal.Type.LOCAL:
                continue
            if row.status not in (
                    Withdrawal.Status.CONFIRMING,
                    Withdrawal.Status.FINISHED):
                continue
            if row.user_id in whitelist:
                continue
            if row.asset not in assets:
                continue

            create_dt = row.created_at.date()
            online_time = new_assets.get(row.asset)
            if online_time:  # 新币上线时间，在某一天当中情况处理
                get_date = (online_time + timedelta(days=1)).date()
                if get_date >= end_date:
                    continue
                if create_dt < get_date:
                    continue
            date_asset_data_map[create_dt][row.asset]['count'] += 1
            date_asset_data_map[create_dt][row.asset]['amount'] += row.amount
            if self.check_last_n_day_data(date_asset_data_map, days, min_usd):
                break

        asset_dates_map = self.get_asset_dates_map(date_asset_data_map, min_usd)
        ret = {}
        for asset, dates in asset_dates_map.items():
            last_dates = list(sorted(dates, reverse=True))[:days]  # 取最新的N天
            if not last_dates:
                continue
            values = [date_asset_data_map[dt][asset] for dt in last_dates]
            denominator = len(values)
            total_count = sum([i['count'] for i in values])
            total_amount = sum([i['amount'] for i in values])
            if not total_count or not total_amount or not denominator:
                # 一天的数据都没有，不计算均值，也不会监控
                continue
            last_7d_avg_count = Decimal(total_count) / denominator
            last_7d_avg_amount = Decimal(total_amount) / denominator
            avg_data = dict(
                last_7d_avg_count=amount_to_str(last_7d_avg_count),
                last_7d_avg_amount=amount_to_str(last_7d_avg_amount),
            )
            ret[asset] = avg_data
        return ret

    @classmethod
    def get_whitelist_users(cls) -> set[int]:
        return get_deposit_withdrawal_rc_whitelist()

    @classmethod
    def get_assets(cls) -> set:
        model = CoinInformation
        rows = model.query.with_entities(
            model.code,
        ).filter(
            model.status == model.Status.VALID,
        ).all()
        return {row.code for row in rows}

    @classmethod
    def get_new_assets(cls, start_time) -> dict[str, datetime]:
        model = CoinInformation
        rows = model.query.with_entities(
            model.code,
            model.online_time,
        ).filter(
            model.status == model.Status.VALID,
            model.online_time > start_time,
        ).all()
        return {row.code: row.online_time for row in rows}

    def check_last_n_day_data(self, date_asset_data_map: dict[date, dict], days: int, min_usd: Decimal) -> bool:
        if len(date_asset_data_map) < days:
            return False
        asset_dates_map = self.get_asset_dates_map(date_asset_data_map, min_usd)
        date_counts = [len(i) for i in asset_dates_map.values()]
        if not date_counts:
            return False
        return min(date_counts) >= days and len(asset_dates_map) == len(date_asset_data_map)

    def get_asset_dates_map(self, date_asset_data_map: dict[date, dict], min_usd: Decimal) -> dict[str, set]:
        asset_dates_map = defaultdict(set)  # 币种满足最小市值的天数列表
        for dt, asset_data_map in date_asset_data_map.items():
            asset_prices = self.get_date_asset_prices(dt)
            for asset, data in asset_data_map.items():
                usd = data['amount'] * asset_prices.get(asset, 0)
                if usd >= min_usd:
                    asset_dates_map[asset].add(dt)
        return asset_dates_map

    def get_date_asset_prices(self, dt: date) -> dict[str, Decimal]:
        if not (v := self._date_asset_prices_map.get(dt)):
            self._date_asset_prices_map[dt] = v = AssetPrice.get_close_price_map(dt)
        return v
