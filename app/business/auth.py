# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from typing import Dict, List, Optional, Iterable

from app import config
from app.caches import AdminUserPermissionCache
from app.models import AdminRolePermission, db, AdminPermission, \
    AdminUserRole, AdminUserMenu, <PERSON><PERSON><PERSON>ser, User, UserSpecialConfigChangeLog
from app.utils.iterable import batch_iter


def _get_user_permission(user_id: int) -> List[str]:
    """
    管理员拥有的菜单列表
    :param user_id: 管理员id
    :return: permission_list
    """
    user_roles = AdminUserRole.query.filter(
        AdminUserRole.user_id == user_id,
        AdminUserRole.status == AdminUserRole.Status.PASSED
    ).all()

    role_ids = [user_role.admin_role_id for user_role in user_roles]

    role_permissions = AdminRolePermission.query.filter(
        AdminRolePermission.admin_role_id.in_(role_ids),
        AdminRolePermission.status == AdminRolePermission.Status.PASSED
    ).all()

    permission_ids = [role_permission.admin_permission_id
                      for role_permission in role_permissions]

    permission_records = AdminPermission.query.filter(
        AdminPermission.id.in_(permission_ids),
        AdminPermission.status == AdminPermission.Status.PASSED
    ).with_entities(
        AdminPermission.name
    ).distinct().all()

    menus = [permission.name for permission in permission_records]
    return menus


def get_user_menus(user_id: int) -> List[str]:
    """
    获取管理员拥有的菜单
    :param user_id: 管理员id
    :return: menu_list
    """
    user_menus = AdminUserMenu.query.filter(
        AdminUserMenu.user_id == user_id,
        AdminUserMenu.status == AdminUserMenu.Status.PASSED
    ).all()

    menus = [user_menu.name for user_menu in user_menus]
    return menus


def update_menus(user_ids: Optional[List] = None):
    """
    更新菜单
    :param
    :return:
    """
    if not user_ids:
        user_ids = [user.id for user in AdminUser.query.all()]

    # 管理员拥有的权限
    for user_id in user_ids:
        new_menus = _get_user_permission(user_id)
        old_menus = get_user_menus(user_id)

        AdminUserMenu.query.filter(
            AdminUserMenu.user_id == user_id
        ).update(
            dict(status=AdminUserMenu.Status.DELETED),
            synchronize_session=False
        )

        change_menus = list(set(new_menus) & set(old_menus))
        create_menus = list(set(new_menus) - set(change_menus))

        AdminUserMenu.query.filter(
            AdminUserMenu.user_id == user_id,
            AdminUserMenu.name.in_(change_menus)
        ).update(
            dict(status=AdminUserMenu.Status.PASSED),
            synchronize_session=False
        )

        result = [AdminUserMenu(
            user_id=user_id,
            name=name,
        ) for name in create_menus]

        db.session.add_all(result)
        db.session.commit()


def update_permission_cache(user_ids: Optional[List] = None):
    """
    批量更新用户权限缓存
    :param user_ids: 用户列表
    :return:
    """
    if not user_ids:
        user_ids = [admin_user.user_id for admin_user in AdminUser.query.with_entities(
            AdminUser.user_id).all()]

    for user_id in user_ids:
        user_permission_list = []

        admin_user: AdminUser = AdminUser.query.filter(
            AdminUser.user_id == user_id
        ).first()
        if admin_user and admin_user.status == AdminUser.Status.PASSED:
            user_roles = AdminUserRole.query.filter(
                AdminUserRole.user_id == user_id,
                AdminUserRole.status == AdminUserRole.Status.PASSED
            ).with_entities(AdminUserRole.admin_role_id).all()
            role_ids = [v.admin_role_id for v in user_roles]

            if user_roles:
                user_permissions = AdminRolePermission.query.filter(
                    AdminRolePermission.admin_role_id.in_(
                        role_ids
                    ),
                    AdminRolePermission.status == AdminRolePermission.Status.PASSED
                ).with_entities(AdminRolePermission.admin_permission_id).all()
                admin_permission_ids = [v.admin_permission_id for v in user_permissions]

                if user_permissions:
                    all_permissions = AdminPermission.query.filter(
                        AdminPermission.id.in_(admin_permission_ids),
                        AdminPermission.status == AdminPermission.Status.PASSED
                    ).with_entities(
                        AdminPermission.rule,
                        AdminPermission.method,
                        AdminPermission.app
                    ).all()

                    user_permission_list = [
                        f'{permission.rule}-{permission.method.name}-{permission.app.name}'
                        for permission in all_permissions]

        AdminUserPermissionCache(user_id).set(json.dumps(user_permission_list))


def update_role_permission_cache(role_id: int):
    """
    更新拥有role_id用户权限缓存
    :param role_id:
    :return:
    """
    user_roles = AdminUserRole.query.filter(
        AdminUserRole.admin_role_id == role_id,
    ).with_entities(AdminUserRole.user_id).all()

    user_ids = [user_role.user_id for user_role in user_roles]

    if user_ids:
        update_permission_cache(user_ids)


def is_super_user(user_id: int) -> bool:
    """
    判断用户是否是超级管理员
    :param user_id: 用户id
    :return: bool
    """
    return int(user_id) in config['SUPER_ADMIN_USER']


def is_super_wallet_user(user_id: int) -> bool:
    """
    判断用户是否是钱包超级管理员
    :param user_id: 用户id
    :return: bool
    """
    return int(user_id) in config.get('WALLET_SUPER_ADMINS', ())


def is_user_has_audit_permission(user_id: int, key: str = 'AUDIT_USERS') -> bool:
    """超管和指定用户有权限"""
    if is_super_user(user_id):
        return True
    if user_id in config[key]:
        return True
    return False


def has_url_permission(user_id: int, rule: str, method: str, app: str) -> bool:
    """
    匹配用户是否有url权限
    """
    # 检查权限
    user_permission_list = AdminUserPermissionCache(user_id).read_aside()
    if not user_permission_list:
        return False
    permission = f'{rule}-{method}-{app}'
    if permission in user_permission_list:
        return True
    return False


def get_url_permission(rule: str, method: str, app: str) -> Optional[AdminPermission]:
    return AdminPermission.query.filter(
        AdminPermission.rule == rule,
        AdminPermission.method == method,
        AdminPermission.app == app,
        AdminPermission.status == AdminPermission.Status.PASSED
    ).first()


def get_admin_user_name_map(user_ids: Iterable[int]) -> Dict[int, str]:
    res = dict()
    for ids_ in batch_iter(user_ids, 1000):
        users = AdminUser.query.filter(
            AdminUser.user_id.in_(ids_)
        ).with_entities(AdminUser.user_id, AdminUser.name).all()
        res.update(dict(users))
    
    missed_user_ids = set(user_ids) - set(res)
    user_emails = User.query.filter(
        User.id.in_(missed_user_ids)
    ).with_entities(User.id, User.email).all()
    res.update(dict(user_emails))
    return res


def get_admin_user_email_and_name_map(user_ids: Iterable[int]) -> Dict[int, list[str]]:
    res = defaultdict(lambda: ['', ''])
    for ids_ in batch_iter(user_ids, 1000):
        users = AdminUser.query.filter(
            AdminUser.user_id.in_(ids_)
        ).with_entities(AdminUser.user_id, AdminUser.name).all()
        for _d in users:
            res[_d.user_id][0] = _d.name

        user_emails = User.query.filter(
            User.id.in_(ids_)
        ).with_entities(User.id, User.email).all()
        for _d in user_emails:
            res[_d.id][1] = _d.email
    return res


def get_special_conf_create_operators(
        record_ids: List, conf_type: UserSpecialConfigChangeLog.SpecialConfigType):
    ret = UserSpecialConfigChangeLog.query.filter(
        UserSpecialConfigChangeLog.op_id.in_(record_ids),
        UserSpecialConfigChangeLog.config_type == conf_type.value,
        UserSpecialConfigChangeLog.op_type == UserSpecialConfigChangeLog.OpType.CREATE.value
    ).with_entities(
        UserSpecialConfigChangeLog.op_id,
        UserSpecialConfigChangeLog.admin_user_id
    ).all()
    op_id_dict = dict(ret)
    admin_ids = list(op_id_dict.values())
    admin_name_map = get_admin_user_name_map(admin_ids)
    op_name_dict = {op_id: admin_name_map.get(user_id, "") for op_id, user_id in op_id_dict.items()}
    return op_id_dict, op_name_dict
