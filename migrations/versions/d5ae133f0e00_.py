"""empty message

Revision ID: d5ae133f0e00
Revises: 0e5363281cdc
Create Date: 2025-09-02 17:30:02.619112

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = 'd5ae133f0e00'
down_revision = '0e5363281cdc'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('monthly_investment_report', schema=None) as batch_op:
        batch_op.add_column(sa.Column('base_interest_usd', mysql.DECIMAL(precision=26, scale=8), nullable=True, comment='基础收益'))
        batch_op.add_column(sa.Column('ladder_interest_usd', mysql.DECIMAL(precision=26, scale=8), nullable=True, comment='阶梯补贴'))
        batch_op.add_column(sa.Column('fixed_interest_usd', mysql.DECIMAL(precision=26, scale=8), nullable=True, comment='固定补贴'))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('monthly_investment_report', schema=None) as batch_op:
        batch_op.drop_column('fixed_interest_usd')
        batch_op.drop_column('ladder_interest_usd')
        batch_op.drop_column('base_interest_usd')

    # ### end Alembic commands ###
