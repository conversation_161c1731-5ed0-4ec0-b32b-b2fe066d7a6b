"""empty message

Revision ID: 82b59f12c947
Revises: cffeb5c5aeb3
Create Date: 2025-09-09 16:54:03.063529

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = '82b59f12c947'
down_revision = 'cffeb5c5aeb3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('staking_income_record',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('report_date', sa.Date(), nullable=False),
    sa.Column('settle_date', sa.Date(), nullable=False),
    sa.Column('asset', sa.String(length=32), nullable=False),
    sa.Column('amount', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('type', app.models.base.StringEnum('INCOME', 'PAYMENT'), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('report_date', 'asset', 'type', name='report_date_asset_type_uniq')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('staking_income_record')
    # ### end Alembic commands ###
