"""video_cover

Revision ID: 08eqc699afa3
Revises: dda931aab1f1
Create Date: 2025-08-25 17:01:23.273154

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = '08eqc699afa3'
down_revision = 'dda931aab1f1'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('video', sa.Column('cover_key', sa.String(length=128), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('video', 'cover_key')
    # ### end Alembic commands ###
