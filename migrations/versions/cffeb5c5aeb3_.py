"""empty message

Revision ID: cffeb5c5aeb3
Revises: d5ae133f0e00
Create Date: 2025-08-28 15:09:33.635379

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = 'cffeb5c5aeb3'
down_revision = 'd5ae133f0e00'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('locked_asset_balance_backup',
                    sa.Column('id', sa.Integer(), nullable=False),
                    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
                    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
                    sa.Column('user_id', sa.Integer(), nullable=False),
                    sa.Column('created_by', sa.Integer(), nullable=False),
                    sa.Column('asset', sa.String(length=32), nullable=False),
                    sa.Column('amount', mysql.DECIMAL(precision=26, scale=8), nullable=False),
                    sa.Column('sub_amount', mysql.DECIMAL(precision=26, scale=8), nullable=True),
                    sa.Column('locked_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
                    sa.Column('unlocked_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
                    sa.Column('status',
                              app.models.base.StringEnum('CREATED', 'LOCKED', 'UNLOCKED', 'FAILED', 'DELETED', 'FROZEN',
                                                         'LOCK_FAIL', 'UNLOCK_FAIL'), nullable=False),
                    sa.Column('remark', sa.String(length=128), nullable=True),
                    sa.Column('retry_type', app.models.base.StringEnum('NO_RETRY', 'RETRY'), nullable=False,
                              comment='重试类型'),
                    sa.Column('lock_type',
                              app.models.base.StringEnum('LOCK', 'ADD_AND_LOCK', 'UNLOCK', 'UNLOCK_AND_SUB'),
                              nullable=False, comment='锁定类型'),
                    sa.Column('lock_business', app.models.base.StringEnum('DEPOSIT', 'WITHDRAWAL', 'WITHDRAWAL_FEE',
                                                                          'DEPOSIT_CANCELLATION',
                                                                          'WITHDRAWAL_CANCELLATION',
                                                                          'WITHDRAWAL_FEE_CANCELLATION', 'TRADING',
                                                                          'TRADING_FEE', 'SYSTEM', 'GIFT',
                                                                          'GIFT_REVOKE', 'COUPON', 'COUPON_RECYCLE',
                                                                          'TRADE_GIFT_COUPON', 'CASHBACK_FEE',
                                                                          'PERPETUAL_SUBSIDY_COUPON',
                                                                          'COPY_TRADING_EXPERIENCE_FEE',
                                                                          'COPY_TRADING_EXPERIENCE_FEE_RECYCLE',
                                                                          'REFERRAL', 'OTC_TRANSFER', 'MARGIN_TRANSFER',
                                                                          'MARGIN_LOAN', 'MARGIN_REPAYMENT',
                                                                          'MARGIN_LIQUIDATION',
                                                                          'MARGIN_LIQUIDATION_FEE',
                                                                          'REALTIME_WALLET_TX_FEE',
                                                                          'REALTIME_ASSET_CONVERSION',
                                                                          'REALTIME_CREDIT_INTEREST',
                                                                          'REALTIME_MARGIN_INTEREST',
                                                                          'REALTIME_WITHDRAW_FEE',
                                                                          'REALTIME_EXCHANGE_FEE',
                                                                          'REALTIME_SIGN_OFF_USER_BALANCE',
                                                                          'REALTIME_CLEANED_BALANCE',
                                                                          'REALTIME_AMM_TRADE_PAY_FEE',
                                                                          'REALTIME_PLEDGE_INTEREST',
                                                                          'REALTIME_PRE_TRADING_FEE',
                                                                          'REALTIME_P2P_ORDER_FEE', 'REALTIME_TRANSFER',
                                                                          'ASSET_CONVERSION', 'SUB_ACCOUNT_TRANSFER',
                                                                          'RED_PACKET', 'RED_PACKET_GRABBING',
                                                                          'RED_PACKET_REFUND', 'MAKER_CASH_BACK',
                                                                          'FUTURE_TRANSFER', 'FUTURE_LOAN',
                                                                          'FUTURE_REPAYMENT', 'FUTURE_LIQUIDATION',
                                                                          'FUTURE_DELIVERY', 'FUTURE_DELIVERY_EXCHANGE',
                                                                          'OPTION_ISSUANCE', 'OPTION_REDEMPTION',
                                                                          'OPTION_DELIVERY', 'INVESTMENT_TRANSFER',
                                                                          'INVESTMENT_INTEREST',
                                                                          'INVESTMENT_INC_INTEREST', 'INVESTMENT_IN',
                                                                          'INVESTMENT_OUT', 'DEX_NODE_VOTING',
                                                                          'LOSS_BALANCE', 'PERPETUAL_TRANSFER_IN',
                                                                          'PERPETUAL_TRANSFER_OUT',
                                                                          'PERPETUAL_TRANSFER',
                                                                          'PERPETUAL_CLEARING_FUNDING',
                                                                          'PERPETUAL_CLEARING_CLOSE', 'CREDIT',
                                                                          'CREDIT_REPAYMENT', 'SMALL_COIN_TRANSFER',
                                                                          'INCOME_TOTAL_TRANSFER', 'BUYBACK_TRANSFER',
                                                                          'INCOME_TO_ADMIN_TRANSFER',
                                                                          'BUYBACK_EXCHANGE', 'ASSET_AUTO_REWARD',
                                                                          'MARGIN_INSURANCE_TRANSFER',
                                                                          'PERPETUAL_INSURANCE_TRANSFER',
                                                                          'INSURANCE_TO_ADMIN_TRANSFER',
                                                                          'SIGNED_OFF_USER_TO_ADMIN_TRANSFER',
                                                                          'CLEANED_BALANCE_TO_ADMIN_TRANSFER',
                                                                          'EXCHANGE_ORDER_TRANSFER',
                                                                          'AUTO_INVEST_TRANSFER', 'BROKER_REFERRAL',
                                                                          'NORMAL_REFERRAL', 'AMBASSADOR_REFERRAL',
                                                                          'INDIRECT_REFERRAL',
                                                                          'EXCHANGE_ORDER_TRANSFER_FEE',
                                                                          'SPOT_GRID_TRANSFER', 'ADD_LIQUIDITY',
                                                                          'REMOVE_LIQUIDITY', 'AMM_FEE_TRANSFER',
                                                                          'MINING_ACTIVITY', 'LAUNCH_POOL_MINING',
                                                                          'ABNORMAL_DEPOSIT_APPLICATION',
                                                                          'IEO_ACTIVITY_SUBSCRIBE',
                                                                          'IEO_ACTIVITY_CANCEL', 'IEO_ACTIVITY_LOTTERY',
                                                                          'DIBS_ACTIVITY_SUBSCRIBE',
                                                                          'DIBS_ACTIVITY_CANCEL',
                                                                          'DIBS_ACTIVITY_LOTTERY',
                                                                          'PLEDGE_LOAN_ASSET_ADD', 'PLEDGE_ASSET_LOCK',
                                                                          'PLEDGE_ASSET_RELEASE', 'PLEDGE_REPAY',
                                                                          'PLEDGE_LIQ', 'PLEDGE_LIQ_FEE',
                                                                          'PRE_TRADING_ISSUE', 'PRE_TRADING_REDEMPTION',
                                                                          'PRE_TRADING_POS_SETTLE',
                                                                          'PRE_TRADING_ISSUE_SETTLE',
                                                                          'COPY_TRADING_TRANSFER',
                                                                          'COPY_PROFIT_SETTLEMENT', 'STAKING_ADD',
                                                                          'STAKING_REMOVE', 'STAKING_INCOME',
                                                                          'BUS_AMB_LOAN', 'BUS_AMB_LOAN_REPAY',
                                                                          'BUS_USER_REFER', 'EQUITY_CASHBACK',
                                                                          'EQUITY_AIRDROP', 'P2P_LOCK', 'P2P_UNLOCK',
                                                                          'P2P_SUB', 'P2P_ADD', 'P2P_MARGIN_PAYMENT',
                                                                          'P2P_MARGIN_REFUND', 'P2P_MARGIN_SYS_ADD',
                                                                          'P2P_MARGIN_SYS_SUB', 'P2P_MARGIN_DEDUCT',
                                                                          'ONCHAIN', 'AMBASSADOR_PACKAGE_SETTLEMENT',
                                                                          'COMMENT_TIP_IN', 'COMMENT_TIP_OUT',
                                                                          'SPOT_OPERATION'), nullable=False,
                              comment='server 锁定业务'),
                    sa.Column('unlock_type',
                              app.models.base.StringEnum('LOCK', 'ADD_AND_LOCK', 'UNLOCK', 'UNLOCK_AND_SUB'),
                              nullable=True, comment='解锁类型'),
                    sa.Column('unlock_business', app.models.base.StringEnum('DEPOSIT', 'WITHDRAWAL', 'WITHDRAWAL_FEE',
                                                                            'DEPOSIT_CANCELLATION',
                                                                            'WITHDRAWAL_CANCELLATION',
                                                                            'WITHDRAWAL_FEE_CANCELLATION', 'TRADING',
                                                                            'TRADING_FEE', 'SYSTEM', 'GIFT',
                                                                            'GIFT_REVOKE', 'COUPON', 'COUPON_RECYCLE',
                                                                            'TRADE_GIFT_COUPON', 'CASHBACK_FEE',
                                                                            'PERPETUAL_SUBSIDY_COUPON',
                                                                            'COPY_TRADING_EXPERIENCE_FEE',
                                                                            'COPY_TRADING_EXPERIENCE_FEE_RECYCLE',
                                                                            'REFERRAL', 'OTC_TRANSFER',
                                                                            'MARGIN_TRANSFER', 'MARGIN_LOAN',
                                                                            'MARGIN_REPAYMENT', 'MARGIN_LIQUIDATION',
                                                                            'MARGIN_LIQUIDATION_FEE',
                                                                            'REALTIME_WALLET_TX_FEE',
                                                                            'REALTIME_ASSET_CONVERSION',
                                                                            'REALTIME_CREDIT_INTEREST',
                                                                            'REALTIME_MARGIN_INTEREST',
                                                                            'REALTIME_WITHDRAW_FEE',
                                                                            'REALTIME_EXCHANGE_FEE',
                                                                            'REALTIME_SIGN_OFF_USER_BALANCE',
                                                                            'REALTIME_CLEANED_BALANCE',
                                                                            'REALTIME_AMM_TRADE_PAY_FEE',
                                                                            'REALTIME_PLEDGE_INTEREST',
                                                                            'REALTIME_PRE_TRADING_FEE',
                                                                            'REALTIME_P2P_ORDER_FEE',
                                                                            'REALTIME_TRANSFER', 'ASSET_CONVERSION',
                                                                            'SUB_ACCOUNT_TRANSFER', 'RED_PACKET',
                                                                            'RED_PACKET_GRABBING', 'RED_PACKET_REFUND',
                                                                            'MAKER_CASH_BACK', 'FUTURE_TRANSFER',
                                                                            'FUTURE_LOAN', 'FUTURE_REPAYMENT',
                                                                            'FUTURE_LIQUIDATION', 'FUTURE_DELIVERY',
                                                                            'FUTURE_DELIVERY_EXCHANGE',
                                                                            'OPTION_ISSUANCE', 'OPTION_REDEMPTION',
                                                                            'OPTION_DELIVERY', 'INVESTMENT_TRANSFER',
                                                                            'INVESTMENT_INTEREST',
                                                                            'INVESTMENT_INC_INTEREST', 'INVESTMENT_IN',
                                                                            'INVESTMENT_OUT', 'DEX_NODE_VOTING',
                                                                            'LOSS_BALANCE', 'PERPETUAL_TRANSFER_IN',
                                                                            'PERPETUAL_TRANSFER_OUT',
                                                                            'PERPETUAL_TRANSFER',
                                                                            'PERPETUAL_CLEARING_FUNDING',
                                                                            'PERPETUAL_CLEARING_CLOSE', 'CREDIT',
                                                                            'CREDIT_REPAYMENT', 'SMALL_COIN_TRANSFER',
                                                                            'INCOME_TOTAL_TRANSFER', 'BUYBACK_TRANSFER',
                                                                            'INCOME_TO_ADMIN_TRANSFER',
                                                                            'BUYBACK_EXCHANGE', 'ASSET_AUTO_REWARD',
                                                                            'MARGIN_INSURANCE_TRANSFER',
                                                                            'PERPETUAL_INSURANCE_TRANSFER',
                                                                            'INSURANCE_TO_ADMIN_TRANSFER',
                                                                            'SIGNED_OFF_USER_TO_ADMIN_TRANSFER',
                                                                            'CLEANED_BALANCE_TO_ADMIN_TRANSFER',
                                                                            'EXCHANGE_ORDER_TRANSFER',
                                                                            'AUTO_INVEST_TRANSFER', 'BROKER_REFERRAL',
                                                                            'NORMAL_REFERRAL', 'AMBASSADOR_REFERRAL',
                                                                            'INDIRECT_REFERRAL',
                                                                            'EXCHANGE_ORDER_TRANSFER_FEE',
                                                                            'SPOT_GRID_TRANSFER', 'ADD_LIQUIDITY',
                                                                            'REMOVE_LIQUIDITY', 'AMM_FEE_TRANSFER',
                                                                            'MINING_ACTIVITY', 'LAUNCH_POOL_MINING',
                                                                            'ABNORMAL_DEPOSIT_APPLICATION',
                                                                            'IEO_ACTIVITY_SUBSCRIBE',
                                                                            'IEO_ACTIVITY_CANCEL',
                                                                            'IEO_ACTIVITY_LOTTERY',
                                                                            'DIBS_ACTIVITY_SUBSCRIBE',
                                                                            'DIBS_ACTIVITY_CANCEL',
                                                                            'DIBS_ACTIVITY_LOTTERY',
                                                                            'PLEDGE_LOAN_ASSET_ADD',
                                                                            'PLEDGE_ASSET_LOCK', 'PLEDGE_ASSET_RELEASE',
                                                                            'PLEDGE_REPAY', 'PLEDGE_LIQ',
                                                                            'PLEDGE_LIQ_FEE', 'PRE_TRADING_ISSUE',
                                                                            'PRE_TRADING_REDEMPTION',
                                                                            'PRE_TRADING_POS_SETTLE',
                                                                            'PRE_TRADING_ISSUE_SETTLE',
                                                                            'COPY_TRADING_TRANSFER',
                                                                            'COPY_PROFIT_SETTLEMENT', 'STAKING_ADD',
                                                                            'STAKING_REMOVE', 'STAKING_INCOME',
                                                                            'BUS_AMB_LOAN', 'BUS_AMB_LOAN_REPAY',
                                                                            'BUS_USER_REFER', 'EQUITY_CASHBACK',
                                                                            'EQUITY_AIRDROP', 'P2P_LOCK', 'P2P_UNLOCK',
                                                                            'P2P_SUB', 'P2P_ADD', 'P2P_MARGIN_PAYMENT',
                                                                            'P2P_MARGIN_REFUND', 'P2P_MARGIN_SYS_ADD',
                                                                            'P2P_MARGIN_SYS_SUB', 'P2P_MARGIN_DEDUCT',
                                                                            'ONCHAIN', 'AMBASSADOR_PACKAGE_SETTLEMENT',
                                                                            'COMMENT_TIP_IN', 'COMMENT_TIP_OUT',
                                                                            'SPOT_OPERATION'), nullable=True,
                              comment='server 解锁业务'),
                    sa.Column('business', app.models.base.StringEnum('HISTORY', 'GIFT', 'ADMIN', 'ONCHAIN'),
                              nullable=False, comment='业务'),
                    sa.Column('business_id', sa.Integer(), nullable=False, comment='业务ID'),
                    sa.PrimaryKeyConstraint('id')
                    )
    with op.batch_alter_table('locked_asset_balance_backup', schema=None) as batch_op:
        batch_op.create_index('ix_business_business_id', ['business', 'business_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('locked_asset_balance_backup', schema=None) as batch_op:
        batch_op.drop_index('ix_business_business_id')

    op.drop_table('locked_asset_balance_backup')
    # ### end Alembic commands ###
