"""empty message

Revision ID: 0e5363281cdc
Revises: 08eqc699afa3
Create Date: 2025-07-23 03:04:37.978697

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = '0e5363281cdc'
down_revision = '08eqc699afa3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('onchain_asset_to_spot_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('token_id', sa.Integer(), nullable=False),
    sa.Column('asset', sa.String(length=32), nullable=False),
    sa.Column('amount', sa.String(length=78), nullable=False),
    sa.Column('status', app.models.base.StringEnum('CREATED', 'DEDUCTED', 'FINISHED'), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'token_id', name='user_id_token_id_uniq')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('onchain_asset_to_spot_history')
    # ### end Alembic commands ###
